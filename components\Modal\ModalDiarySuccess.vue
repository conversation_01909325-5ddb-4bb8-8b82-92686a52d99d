<template>
  <div
    v-if="isOpen"
    class="fixed z-[9999999999999] inset-0 flex items-center justify-center bg-black bg-opacity-50"
    @click="close"
  >
    <div
      class="bg-white rounded-lg px-8 py-4 shadow-lg md:w-1/4 w-[90%]"
      @click.stop
    >
      <div class="flex justify-between">
        <h2 class="text-lg font-bold textShadow">L<PERSON><PERSON> nh<PERSON>t ký thành công</h2>
        <div class="flex justify-end">
          <button
            @click="close"
            class="text-lg font-bold textShadow text-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-6 h-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div>
        <div>
          M<PERSON> nhật ký của bạn là: <span class="font-semibold">123456</span>
        </div>
      </div>
      <div class="w-full flex justify-around mt-4 text-sm">
        <button
          @click="
            handleInitialDiary();
            close();
          "
          class="bg-white border border-primary text-primary py-1 px-4 rounded hover:bg-blue-200"
        >
          Tạo nhật ký mới
        </button>
        <button
          class="bg-primary text-white py-1 px-4 rounded hover:bg-blue-900"
        >
          Tạo đơn từ nhật ký
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

const { handleInitialDiary } = useDiary();
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});
const selectedOptions = ref<{ [key: string]: string }>({});

const route = useRoute();

const emit = defineEmits(["close"]);

const close = () => {
  emit("close");
};
</script>
