<template>
  <div
    v-if="props.isOpen"
    class="fixed z-[9999999999999] inset-0 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="closeModal"
  >
    <div
      class="bg-white rounded-lg shadow-lg md:w-1/3 w-[90%] relative px-6 py-4"
      @click.stop
    >
      <div class="flex justify-between items-center">
        <div></div>
        <h2 class="text-lg font-bold textShadow">ĐĂNG NHẬP VỚI PORTAL</h2>
        <button
          @click="closeModal"
          class="text-gray-500 hover:text-gray-900 font-bold text-2xl"
          aria-label="Close"
        >
          &times;
        </button>
      </div>
      <div class="mt-4">
        <button
          @click="openSSOPopup"
          class="bg-primary text-white py-2 px-6 rounded hover:bg-blue-900 text-center block w-full"
        >
          ĐĂNG NHẬP VỚI PORTAL
        </button>
      </div>
      <!-- <div class="mt-4">
        <p v-if="errorMessage" class="text-red-500">{{ errorMessage }}</p>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(["close"]);

const errorMessage = ref<string | null>(null);
const { $matrixClient } = useNuxtApp();
const route = useRoute();

const openSSOPopup = () => {
  const redirectUrl = encodeURIComponent(window.location.href);
  const config = useRuntimeConfig();

  const popupWidth = 600;
  const popupHeight = 600;
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  const popupLeft = (screenWidth - popupWidth) / 2;
  const popupTop = (screenHeight - popupHeight) / 2;

  const popup = window.open(
    `${config.public.MATRIX_SSO}${redirectUrl}`,
    "SSO Login",
    `width=${popupWidth},height=${popupHeight},top=${popupTop},left=${popupLeft},resizable=yes,scrollbars=yes`
  );

  const pollTimer = setInterval(async () => {
    try {
      if (popup && popup.closed) {
        clearInterval(pollTimer);
        return;
      }

      if (
        popup &&
        popup.location &&
        popup.location.href.includes("loginToken")
      ) {
        const params = new URLSearchParams(new URL(popup.location.href).search);
        const loginToken = params.get("loginToken");

        if (loginToken) {
          popup.close();
          await connectWithToken(loginToken);
          clearInterval(pollTimer);
        }
      }
    } catch (error) {
      throw error;
    }
  }, 200);
};

const connectWithToken = async (loginToken: string) => {
  try {
    await $matrixClient.loginWithAccessToken(loginToken);
    const { token: accessToken } = await $matrixClient.loadCredentials();
    if (accessToken) {
      await $matrixClient.connectMatrix(accessToken);
      emit("close");
    } else {
      throw new Error("Không tìm thấy access token sau khi đăng nhập.");
    }
  } catch (error) {
    console.error("Lỗi kết nối:", error);
    throw error;
  } finally {
    location.reload();
  }
};

const closeModal = () => {
  emit("close");
};

onMounted(async () => {
  const loginToken = route.query.loginToken as string;
  if (loginToken) {
    await connectWithToken(loginToken);
  }
});
</script>
