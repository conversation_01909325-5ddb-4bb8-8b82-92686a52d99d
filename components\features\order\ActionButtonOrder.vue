<template>
  <div class="flex gap-2 items-center justify-end">
    <div
      v-tippy="'Đổi trả hàng'"
      @click="handleNavigate"
      class="bg-white rounded text-primary border border-primary px-3 py-[7px] cursor-pointer min-w-[42px]"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 9.75h4.875a2.625 2.625 0 0 1 0 5.25H12M8.25 9.75 10.5 7.5M8.25 9.75 10.5 12m9-7.243V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185Z"
        />
      </svg>
    </div>
    <!-- in hóa đơn -->
    <div
      v-tippy="'In hóa đơn'"
      @click="handlePrintOrder"
      class="bg-white rounded text-primary border border-primary px-3 py-[6px] cursor-pointer flex items-center justify-center"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.************.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
        />
      </svg>
      <span class="ml-1 text-sm">{{
        getPrintCount(diary?.order?.customAttributes)
      }}</span>
    </div>
    <!-- Đánh giá đơn hàng -->
    <div
      v-tippy="'Gửi đánh giá'"
      @click="handleOpenZnsPopUp('SEND_ORDER_RATE')"
      class="bg-white rounded text-primary border border-primary px-3 py-[7px] cursor-pointer min-w-[42px]"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
        />
      </svg>
    </div>
    <!-- Gửi ZNS -->
    <div
      v-tippy="'Gửi đơn hàng'"
      @click="handleOpenZnsPopUp('SEND_ORDER_INFO')"
      class="border border-primary rounded cursor-pointer min-w-[42px] flex items-center justify-center"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <div>
        <img
          src="~/assets/images/zalo.jpg"
          alt=""
          class="p-1 w-[32px] h-[32px]"
          loading="lazy"
        />
      </div>
    </div>
    <!-- Sử lí ffm đơn hàng -->
    <div
      class="bg-white rounded text-primary border border-primary px-3 py-[7px] min-w-[42px] cursor-pointer"
      v-tippy="'Xử lí FFM đơn hàng'"
      @click="toogleCompleteOrder"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
        />
      </svg>
    </div>
    <!-- Hủy đơn -->
    <div
      class="bg-white rounded text-primary border border-primary px-3 py-[7px] min-w-[42px] cursor-pointer"
      v-tippy="'Hủy đơn'"
      @click="toogleCancelOrder"
      :class="
        orderDetail
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
        />
      </svg>
    </div>
    <!-- Xuất hóa đơn -->
    <div
      v-if="settingOrg?.isExportInvoice"
      class="bg-white rounded text-primary border border-primary px-3 py-[7px] min-w-[42px] cursor-pointer flex items-center justify-center"
      v-tippy="'Xuất hóa đơn'"
      @click="toogleExportInvoice"
      :class="
        orderDetail?.remainTotal === 0
          ? 'cursor-pointer'
          : 'cursor-not-allowed opacity-50 pointer-events-none'
      "
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
        />
      </svg>
    </div>
  </div>
  <SendVoucher
    v-if="isOpenTopicPopup"
    @cancel="handleToogleMessagePopup"
    @confirm="handleSendMessage"
  ></SendVoucher>
  <ZaloZns
    v-if="isOpenZnsPopup"
    @cancel="handleOpenZnsPopUp"
    @confirm="handleSendZns"
    :orderDetail="diary"
    :typeSendZns="typeSendZns"
  ></ZaloZns>
  <ConfirmDialog
    v-if="isAlert"
    :title="'Thông báo'"
    :message="'Đơn đang ở trạng thái nháp bạn có muốn thanh toán'"
    @confirm="confirm"
    @cancel="cancel"
  />
  <CancelOrderPopup
    v-if="isCancelOrderPopup"
    :order="diary"
    :title="'Hủy đơn hàng'"
    :text="`Bạn đang hủy đơn hàng ${diary?.id} vui lòng chọn lý do`"
    :reasonText="'Lý do hủy đơn'"
    :dataReason="dataReason"
    @cancel="toogleCancelOrder"
    @confirm="handleConfirmCancelOrder"
  >
  </CancelOrderPopup>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
  <FFMOrder
    v-if="isOpenCompleteOrder"
    :order="diary"
    @confirm="handleCompleteOrder"
    @cancel="toogleCompleteOrder"
  />
  <ExportInvoicePopup
    v-if="isOpenExportInvoice"
    :order="diary"
    @confirm="toogleExportInvoice"
    @cancel="toogleExportInvoice"
  ></ExportInvoicePopup>
</template>
<script setup lang="ts">
const route = useRoute();
const ordersStore = useOrdersStore();
const props = defineProps(["diary", "isButtonSendMessage", "isNotDraft"]);
const isOpenTopicPopup = ref(false);
const handleToogleMessagePopup = () => {
  if (props.diary?.order?.ownerPartyId) {
    isOpenTopicPopup.value = !isOpenTopicPopup.value;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const { createTopic } = useCustomer();

const handleCreateTopic = async (message: string) => {
  if (props.diary?.order?.ownerPartyId) {
    await createTopic(
      "674fcd11b1538b122be026d0",
      props.diary?.order?.ownerPartyId,
      message
    );
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
  isOpenTopicPopup.value = false;
};
// send message
const { sendMessage, shareOrder } = useComhub();
const handleSendMessage = async (message: string) => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  const zaloId = appId.find((app: any) => app.name === "ZNS");
  if (!zaloId) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  if (!props.diary?.order?.ownerPartyId) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    isOpenTopicPopup.value = false;
    return;
  }

  try {
    await sendMessage(zaloId?.id, message, "TEXT", "SYSTEM", [
      props.diary?.order?.ownerPartyId,
    ]);
  } catch (error) {
    console.error("Error sending message:", error);
  }

  isOpenTopicPopup.value = false;
};
const isOpenZnsPopup = ref(false);
const isLoading = ref(false);
const typeSendZns = ref();

const handleOpenZnsPopUp = (type: string) => {
  if (props.diary?.status === "CANCELLED") {
    useNuxtApp().$toast.warning("Đơn hàng đang ở trạng thái hủy không thể gủi");
    return;
  }
  if (props.diary?.order?.ownerPartyId) {
    // ordersStore.tooltip = null;
    isOpenZnsPopup.value = !isOpenZnsPopup.value;
    typeSendZns.value = type;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const handleSendZns = async (data: any) => {
  try {
    isLoading.value = true;

    await shareOrder(
      data?.templateData,
      data?.app?.apps[0]?.id,
      data?.templateType
    );
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;

    throw error;
  }
  isOpenZnsPopup.value = false;
};
//
const diaryStore = useDiariesStore();
const { printOrderHTML, cancelOrder } = useOrder();
import printJS from "print-js";
import type { Auth } from "~/types/Auth";
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const handlePrintOrder = async () => {
  isLoading.value = true;
  const url = useRequestURL();

  const baseUrl = `${url.origin}/thanh-toan?orderId=${
    props.diary?.id
  }&orgId=${url.searchParams.get("orgId")}&storeId=${url.searchParams.get(
    "storeId"
  )}`;
  try {
    const response = await printOrderHTML(
      props.diary?.id,
      "Chưa thanh toán",
      baseUrl
    );

    const data = response.data;

    printJS({
      printable: data,
      type: "raw-html",
      scanStyles: false,
      style: `
        @page { margin: 0; } /* Xóa margin mặc định của trang in */
        body { margin: 0; } /* Đảm bảo body không có margin thừa */
      `,
    });
    orderStore.updateQuantityPrintOrder();
  } catch (error) {
    console.error("Error printing the order:", error);
  } finally {
    isLoading.value = false;
  }
};
// Import utilities
import { getPrintCount } from "~/utils/orderHelpers";
const isAlert = ref(false);

const cancel = () => {
  isAlert.value = false;
};
const { updateStatusApproved } = useOrder();

const confirm = async () => {
  if (props.diary?.remainTotal > 0) {
    isLoading.value = true;
    await updateStatusApproved(props.diary?.id);
    navigateTo(
      `/payment?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    isLoading.value = false;
  } else {
    useNuxtApp().$toast.warning(
      "Không thể thanh toán,số tiền cần thanh toán là 0"
    );
  }
};
//
const isCancelOrderPopup = ref();
const toogleCancelOrder = () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isCancelOrderPopup.value = !isCancelOrderPopup.value;
};
const auth = useCookie("auth") as any;

const handleConfirmCancelOrder = async (reason: any) => {
  try {
    const data = {
      reason: "CUSTOMER",
      updatedBy: auth?.user?.id,
      note: reason,
      orderType: "SALES",
    };
    const response = await cancelOrder(props.diary?.id, data);
    orderStore.updateStatusOrderDetail(reason);

    useNuxtApp().$toast.success("Hủy đơn hàng thành công");
  } catch (error) {
    throw error;
  } finally {
    toogleCancelOrder();
  }
};
const handleNavigate = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (props.diary?.status === "COMPLETED") {
    useNuxtApp().$toast.warning(
      "Đơn hàng đang ở trạng thái hoàn thành không thể hủy"
    );
    return;
  }
  navigateTo(
    `/order/return?orderReturnId=${props.diary?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};
const isOpenCompleteOrder = ref(false);
const toogleCompleteOrder = () => {
  isOpenCompleteOrder.value = !isOpenCompleteOrder.value;
};
const { completeOrder } = usePortal();
const handleCompleteOrder = async () => {
  try {
    const response = await completeOrder(props.diary?.id, auth.value?.user?.id);
    if (response?.status === 1.0) {
      ordersStore.updateFFMStatus(props.diary);
    }
  } catch (error) {
    throw error;
  } finally {
    toogleCompleteOrder();
  }
};
const dataReason = [
  { name: "Chọn lý do hủy" },
  {
    name: "Khách hàng yêu cầu",
  },
  {
    name: "Thông tin chưa hợp lệ",
  },
  {
    name: "Không đủ hàng trong kho",
  },
  {
    name: "Không thanh toán đơn hàng",
  },
  {
    name: "Khác",
  },
];
const isOpenExportInvoice = ref(false);

// Use tab-isolated context instead of cookies
const { orgId } = useTabContext();

// ✅ Non-blocking lazy load - component renders immediately
const settingData = ref<any[]>([]);
const settingOrg = computed(() => {
  if (!settingData.value || settingData.value.length === 0) return null;
  return settingData.value.find((org: any) => org?.storeId === orgId.value);
});

// ✅ Load setting data after component mount (non-blocking)
onMounted(async () => {
  try {
    const data = (await $fetch("/data/setting.json")) as any[];
    settingData.value = data;
  } catch (error) {
    console.error("Error loading settings:", error);
  }
});
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
</script>
