<template>
  <div
    class="fixed bottom-0 md:relative left-0 right-0 bg-white p-2 shadow md:rounded"
  >
    <div class="flex w-full">
      <div class="md:block hidden w-full">
        <div
          @click="orderStore.handleCreateBuyOrder()"
          class="bg-primary text-center text-white w-full rounded px-2 py-1 cursor-pointer"
        >
          <PERSON><PERSON> toán
        </div>
      </div>
      <!-- mobile -->
      <button
        @click="orderStore.handleCreateBuyOrder()"
        class="bg-primary text-white py-2 px-4 rounded-lg md:px-2 md:py-1 w-full block md:hidden"
      >
        Thanh toán •
        {{ formatCurrency(totalPrice + orderStore.shippingFee || 0) }}
      </button>
    </div>
  </div>
  <!-- <PartialPaymentMessage
    v-if="isPartialPayment"
    @cancel="handleOpenPartialPayment"
    @confirm="handleConfirmPartialPayment"
  ></PartialPaymentMessage> -->
</template>

<script setup lang="ts">
const orderStore = useOrderStore();
const { printOrderHTML, createBuyOrder } = useOrder();
const totalPrice = computed(() => orderStore.orderDetail?.remainTotal);
const orderDetail = computed(() => orderStore.orderDetail);
const isPartialPayment = ref(false);
// const handleOpenPartialPayment = () => {
//   orderStore.paymentAmount = 0;
//   isPartialPayment.value = !isPartialPayment.value;
// };
// const handleConfirmPartialPayment = async () => {
//   await orderStore.handlePlaceOrder();
//   isPartialPayment.value = !isPartialPayment.value;
// };
// const { shareOrder } = useComhub();

// const handleShareZalo = async () => {
//   const phoneNumber = convertPhoneNumber(orderDetail.value?.order?.ownerPhone);
//   const template = {
//     templateData: {
//       orderId: orderDetail.value?.id,
//       orderIdParam: orderDetail.value?.id,
//       phone: phoneNumber,
//       totalPrice: orderDetail.value?.remainTotal,
//       createdStamp: formatTimestampV2(orderDetail.value?.order?.orderDate),
//       name: orderDetail.value?.order?.ownerName,
//       status: orderDetail.value?.statusDescription,
//     },
//     phone: phoneNumber,
//     templateId: "362640",
//     trackingId: orderDetail.value?.id,
//   };
//   await shareOrder(template, "62661ed100adf430d79fb9e5");
// };
// function convertPhoneNumber(phoneNumber: string) {
//   if (typeof phoneNumber === "string" && phoneNumber.startsWith("0")) {
//     return "84" + phoneNumber.slice(1);
//   }
//   useNuxtApp().$toast.warning("Số điện thoại không hợp lệ");
//   return phoneNumber || "Số điện thoại không hợp lệ";
// }
const handleClickPayment = () => {
  useNuxtApp().$toast.warning("Tính năng đang phát triển");
};
</script>

<style lang="css" scoped></style>
