<template>
  <div class="flex flex-col w-full sm:w-auto sm:mb-0">
    <input
      v-if="type === 'text'"
      :type="type"
      :value="modelValue"
      :class="inputClass"
      :placeholder="placeholder"
      @input="$emit('update:modelValue', $event.target.value)"
    />
    <select
      v-if="type === 'select'"
      :value="modelValue"
      :class="selectClass"
      @change="$emit('update:modelValue', $event.target.value)"
    >
      <option :value="defaultValue">{{ defaultText }}</option>
      <option
        v-for="option in options"
        :key="option.id"
        :value="
          option.code ||
          option.id ||
          option.value ||
          option.name ||
          option.description
        "
        class="text-gray-700 hover:bg-gray-100"
      >
        {{ option?.title || option.description || option.name }}
      </option>
    </select>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
      default: () => [],
    },
    inputClass: {
      type: String,
      default:
        "relative flex flex-row p-3 placeholder:text-gray-500 text-xs rounded-md border border-gray-300 hover:border-blue-300 items-center justify-center space-x-2 bg-white outline-none",
    },
    selectClass: {
      type: String,
      default:
        "block text-gray-500 w-full sm:w-72 px-3 py-[7px] mb-1 text-sm rounded appearance-none bg-[#F5F5F5] focus:outline-none",
    },
    defaultValue: {
      type: [String, Number],
      default: "",
    },
    defaultText: {
      type: String,
      default: "Please select",
    },
  },
};
</script>
