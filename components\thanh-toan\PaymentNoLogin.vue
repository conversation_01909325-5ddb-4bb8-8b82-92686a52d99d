<template>
  <div class="h-full flex flex-col">
    <div v-if="isPending" class="flex-1 flex flex-col">
      <!-- Header -->
      <div
        class="flex items-center justify-start mb-4 p-3 bg-gray-50 rounded-lg"
      >
        <button
          @click="handleBack"
          class="p-1 hover:bg-gray-200 rounded transition-colors mr-3"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4 text-gray-600"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </button>
        <div class="text-primary font-semibold text-sm">
          Chọn phương thức thanh toán kh<PERSON>c
        </div>
      </div>
      <!-- <PERSON><PERSON><PERSON> tra trạng thái để xử lý statusCode -->
      <div class="text-left ml-6">
        <!-- <PERSON><PERSON><PERSON> thị mô tả phương thức thanh toán -->

        <span v-if="selectedPayment.statusCode === '1'">
          <!-- Trạng thái đang chờ thanh toán -->

          <!--  kiểm tra thêm nếu có params statusCode -->
          <div
            v-if="route.query.statusCode === '1'"
            class="w-full flex flex-col justify-between md:justify-start gap-2 py-2"
          >
            <div class="text-primary font-semibold">
              {{ selectedPayment.methodDescription }}
            </div>
            <div class="mt-2 text-sm">
              Chúng tôi đã nhận được giao dịch thanh toán của bạn. Vui lòng đợi
              trong giây lát để hệ thống xử lý.
            </div>
          </div>
          <div v-else>
            <div
              class="w-full flex justify-between md:justify-start gap-2 py-2"
            >
              <div class="text-primary font-semibold">
                {{ selectedPayment.methodDescription }}
              </div>
              <span class="text-yellow-600">Chờ thanh toán</span>
            </div>
            <div class="mt-2 text-sm">
              Bạn đang có một giao dịch đang chờ thanh toán.
            </div>
            <div class="mt-4 space-x-4 text-sm text-right w-full">
              <button
                class="bg-primary text-white py-1 px-4 rounded"
                @click="redirectToPaymentUrl"
              >
                Tiếp tục thanh toán
              </button>
            </div>
          </div>
        </span>

        <div v-else-if="selectedPayment.statusCode === '-1'" class="w-full">
          <!-- Trạng thái đã hủy -->
          <div class="w-full flex justify-between md:justify-start  py-1">
            <div class="text-primary font-semibold">
              {{ selectedPayment.methodDescription }}
            </div>
            <span class="text-red-600">Đã hủy</span>
          </div>

          <div class="mt-2 text-sm text-black">
            Giao dịch của bạn đã bị hủy. Vui lòng thử lại hoặc chọn phương thức
            thanh toán khác.
          </div>
          <div class="mt-4 w-full text-right">
            <button
              class="bg-primary text-white py-1 px-4 rounded"
              @click="redirectToPaymentUrl"
            >
              Thanh toán lại với {{ selectedPayment.methodDescription }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="!isOpenQr" class="flex-1 flex flex-col">
      <div class="space-y-3">
        <div
          v-for="item in filteredPaymentMethods"
          :key="item.code"
          class="border-2 rounded-xl p-4 flex items-center gap-3 cursor-pointer transition-all duration-200 hover:border-primary/50 hover:shadow-sm"
          @click="selectPaymentMethod(item)"
        >
          <div class="flex-shrink-0">
            <img
              class="w-8 h-8 object-contain"
              :src="item.image || 'https://placehold.co/0'"
              :alt="item.name"
              loading="lazy"
            />
          </div>
          <span class="font-semibold text-base text-gray-900 flex-1">{{
            item.name
          }}</span>
          <div class="flex-shrink-0">
            <svg
              class="w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <QrPaymentNoLogin
        :payment="dataPayment"
        :paymentMethod="selectedPaymentMethod"
        :isExpired="isExpired"
        :isLoading="isLoading"
        @backQr="handleBack"
        @createNewPayment="handleCreateNewPayment"
      />
      <div v-if="selectedPaymentMethod === 'transfer'">
        <ItemBank
          v-for="(bank, index) in dataBank"
          :key="index"
          :bank="bank"
          :isAndroid="isAndroid"
        />
      </div>
    </div>
    <!-- <Dialog v-if="isAlert" @confirm="confirm" @cancel="cancel" /> -->
  </div>
</template>

<script setup lang="ts">
import type { Auth } from "~/types/Auth";

const props = defineProps(["orderDetails", "dataPaymentMethod"]);
const {
  createPaymentOrder,
  cancelPayment,
  paymentsByOrders,
  getAndroidBank,
  getIosBank,
} = usePayment();

const INTERVAL_TIME = 5000;

const isPending = ref(false);
const selectedPaymentMethod = ref("");
const selectedPayment = ref({} as any);
const isOpenQr = ref(false);
const dataPayment = ref();
const isLoading = ref(false);
const isAlert = ref(false);
const isExpired = ref(false);
const dataBank = ref([] as any);
const isAndroid = ref(false);

const auth = useCookie("auth").value as unknown as Auth;
const { storeId,orgId } = useTabContext();


const paymentStore = usePaymentStore();
const orderStore = useOrderStore();
const route = useRoute();
const router = useRouter();

const dataPaymentOrder = computed(() => paymentStore.dataPaymentOrder);
const filteredPaymentMethods = computed(() =>
  props.dataPaymentMethod.filter((item: any) => item.code !== "manual")
);

let intervalId: any;

// Utility function to create a new payment order
const createNewPaymentOrder = async (
  orderId: string,
  paymentMethod: string
) => {
  const host = window.location.origin;
  const data = {
    orderId,
    paymentMethod,
    appliedAmount: props.orderDetails.data.remainTotal,
    payDate: Date.now(),
    source: "ORDER_SOURCE",
    returnUrl: `${host}/thanh-toan`,
    paymentType: "ONLINE",
    createBy: auth?.user?.id,
    attributes: paymentMethod === "payon" ? { returnQR: true } : {},
  };

  try {
    const response = await createPaymentOrder(data);
    return response;
  } catch (error) {
    console.error("Failed to create payment order:", error);
    throw error;
  }
};

// Handle payment selection
const selectPaymentMethod = async (item: any) => {
  selectedPaymentMethod.value = item.code;
  await paymentStore.getDataPaymentOrder(props.orderDetails.data.id);

  if (["transfer"].includes(selectedPaymentMethod.value)) {
    await processQrPayment();
  } else {
    await redirectToPaymentUrl();
  }
};

// Process QR-based payments
const processQrPayment = async () => {
  const existingPayment = findExistingPayment();
  if (existingPayment) {
    openQrPayment(existingPayment);
  } else {
    const payment = await createNewPaymentOrder(
      props.orderDetails.data.id,
      selectedPaymentMethod.value
    );
    if (payment) openQrPayment(payment);
  }
};

// Redirect to payment URL for non-QR payments
const redirectToPaymentUrl = async () => {
  const existingPayment = findExistingPayment();
  console.log("🚀 ~ redirectToPaymentUrl ~ existingPayment:", existingPayment);

  if (existingPayment) {
    if (existingPayment.statusCode === "1") {
      window.location.href = existingPayment.payUrl;
    } else {
      const payment = await createNewPaymentOrder(
        props.orderDetails.data.id,
        selectedPaymentMethod.value
      );
      if (payment) window.location.href = payment.data;
    }
  } else {
    const payment = await createNewPaymentOrder(
      props.orderDetails.data.id,
      selectedPaymentMethod.value
    );
    if (payment) window.location.href = payment.data;
  }
};

// Open QR payment view
const openQrPayment = (payment: any) => {
  isOpenQr.value = true;
  dataPayment.value = payment;
  intervalId = setInterval(checkPaymentStatus, INTERVAL_TIME);
};

// Find existing payment for the selected method
const findExistingPayment = () => {
  return dataPaymentOrder.value?.find(
    (payment: any) => payment.methodTypeCode === selectedPaymentMethod.value
  );
};
// Handle checking payment status every 5 seconds
const checkPaymentStatus = async () => {
  try {
    const payments = await paymentsByOrders([props.orderDetails.data.id]);
    // kiểm tra tất cả các giao dịch thanh toán có nào có statusCode = 0 không

    if (payments.some((payment: any) => payment.statusCode === "0")) {
      clearInterval(intervalId);
      isAlert.value = true;
      handlePaymentSuccess();
      //reload trang
      window.location.reload();
    } else if (payments.some((payment: any) => payment.statusCode === "-1")) {
      clearInterval(intervalId);
      isExpired.value = true;
    }
  } catch (error) {
    console.error("Error checking payment status:", error);
  }
};

// Handle successful payment
const handlePaymentSuccess = () => {
  orderStore.paymentAmount = 0;
  localStorage.removeItem("paymentAmount");
};

// Handle navigation and cleanup on back
const handleBack = () => {
  clearInterval(intervalId);
  isOpenQr.value = false;
  isPending.value = false;
  selectedPaymentMethod.value = "";
};

// Handle creating a new payment after expiration
const handleCreateNewPayment = async () => {
  isExpired.value = false;

  if (dataPaymentOrder.value?.length > 0) {
    const existingPayment = findExistingPayment();
    if (existingPayment) await cancelPayment(existingPayment.paymentId, "");
  }

  const payment = await createNewPaymentOrder(
    props.orderDetails.data.id,
    selectedPaymentMethod.value
  );
  if (payment) openQrPayment(payment);
};

// Detect platform for device-specific logic
const detectPlatform = () => {
  const userAgent = navigator?.userAgent;
  if (/android/i.test(userAgent)) return "Android";
  if (/iPad|iPhone|iPod/.test(userAgent)) return "iOS";
  if (/windows/i.test(userAgent)) return "Windows";
  return "unknown";
};

// Handle device-based bank data
const handleDevice = async () => {
  const platform = detectPlatform();
  if (platform === "Android") {
    isAndroid.value = true;
    dataBank.value = await getAndroidBank();
  } else if (platform === "iOS") {
    isAndroid.value = false;
    dataBank.value = await getIosBank();
  } else {
    dataBank.value = null;
  }
};

// Lifecycle hooks
onMounted(() => {
  handleDevice();
});
onMounted(async () => {
  await paymentStore.getDataPaymentOrder(route.query.orderId as string);
  handleLastPayment();
});
onUnmounted(() => {
  clearInterval(intervalId);
});

// Handle navigation reset for payments
router.beforeEach(async (to, _, next) => {
  if (to.path === "/thanh-toan") {
    resetPaymentData();
    await paymentStore.getDataPaymentOrder(to.query.orderId as string);
    handleLastPayment();
  }
  next();
});

// Reset payment data
const resetPaymentData = () => {
  selectedPaymentMethod.value = "";
  dataPayment.value = {};
  clearInterval(intervalId);
};

// Handle the last payment on load
const handleLastPayment = () => {
  const lastPayment = dataPaymentOrder.value?.at(-1);
  if (!lastPayment) return;

  if (["transfer"].includes(lastPayment?.methodTypeCode)) {
    selectedPaymentMethod.value = lastPayment?.methodTypeCode;
    openQrPayment(lastPayment);
  } else {
    selectedPaymentMethod.value = lastPayment?.methodTypeCode;
    selectedPayment.value = lastPayment;
    console.log("🚀 ~ handleLastPayment ~ lastPayment:", lastPayment);
    isPending.value = true;
  }
  intervalId = setInterval(checkPaymentStatus, INTERVAL_TIME);
};

</script>
