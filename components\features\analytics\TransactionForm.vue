<template>
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-end md:items-center justify-center z-50 p-0 md:p-4"
  >
    <div
      class="bg-white rounded-t-2xl md:rounded-lg shadow-xl w-full max-w-2xl h-[85vh] md:max-h-[90vh] flex flex-col overflow-hidden"
    >
      <!-- Header -->
      <div
        class="flex items-center justify-between p-4 md:p-8 border-b border-gray-200 bg-white flex-shrink-0"
      >
        <!-- Mobile drag handle -->
        <div
          class="md:hidden absolute top-2 left-1/2 transform -translate-x-1/2"
        >
          <div class="w-8 h-1 bg-gray-300 rounded-full"></div>
        </div>

        <h3 class="text-lg md:text-xl font-semibold text-gray-900 mt-3 md:mt-0">
          {{ isEdit ? "Chỉnh sửa giao dịch" : "Thêm giao dịch mới" }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors mt-3 md:mt-0 p-1"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form
        @submit.prevent="handleSubmit"
        class="flex flex-col flex-1 overflow-hidden"
      >
        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto overscroll-contain">
          <div class="p-4 md:p-8 space-y-4 md:space-y-6 pb-4">
            <!-- Transaction Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3"
                >Loại giao dịch</label
              >
              <div class="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  @click="form.type = 'income'"
                  :class="[
                    'flex items-center justify-center gap-2 px-4 py-4 border rounded-xl transition-colors font-medium',
                    form.type === 'income'
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    />
                  </svg>
                  Thu
                </button>
                <button
                  type="button"
                  @click="form.type = 'expense'"
                  :class="[
                    'flex items-center justify-center gap-2 px-4 py-4 border rounded-xl transition-colors font-medium',
                    form.type === 'expense'
                      ? 'border-red-500 bg-red-50 text-red-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                    />
                  </svg>
                  Chi
                </button>
              </div>
            </div>

            <!-- Amount and Category Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              <!-- Amount -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3"
                  >Số tiền</label
                >
                <div class="relative">
                  <input
                    type="text"
                    v-model="formattedAmount"
                    @input="updateAmount"
                    placeholder="0"
                    class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-right text-lg font-semibold"
                    required
                  />
                  <span
                    class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium"
                    >VNĐ</span
                  >
                </div>
              </div>

              <!-- Category -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3"
                  >Danh mục</label
                >
                <select
                  v-model="form.categoryId"
                  class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent appearance-none bg-white"
                  required
                >
                  <option value="">Chọn danh mục</option>
                  <option
                    v-for="category in filteredCategories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.name }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3"
                >Mô tả</label
              >
              <textarea
                v-model="form.description"
                rows="3"
                placeholder="Nhập mô tả giao dịch..."
                class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
              ></textarea>
            </div>

            <!-- Payment Method -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3"
                >Phương thức thanh toán</label
              >
              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <button
                  type="button"
                  v-for="method in paymentMethods"
                  :key="method.value"
                  @click="form.paymentMethod = method.value"
                  :class="[
                    'flex items-center md:flex-col gap-3 md:gap-2 px-4 py-4 border rounded-xl transition-colors font-medium',
                    form.paymentMethod === method.value
                      ? 'border-primary bg-primary/5 text-primary'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50',
                  ]"
                >
                  <svg
                    class="w-5 h-5 md:w-6 md:h-6 flex-shrink-0"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path :d="method.icon" />
                  </svg>
                  <span class="text-sm md:text-xs font-medium">{{
                    method.label
                  }}</span>
                </button>
              </div>
            </div>

            <!-- Date -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3"
                >Ngày giao dịch</label
              >
              <input
                type="datetime-local"
                v-model="form.date"
                class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              />
            </div>
          </div>
        </div>

        <!-- Actions Footer -->
        <div class="border-t border-gray-200 p-4 md:p-8 bg-white flex-shrink-0">
          <div class="flex flex-col md:flex-row gap-3 md:gap-4 md:justify-end">
            <button
              type="button"
              @click="$emit('close')"
              class="w-full md:w-auto px-6 py-4 md:py-3 text-gray-700 bg-white border border-gray-300 rounded-xl md:rounded-lg hover:bg-gray-50 transition-colors md:min-w-[120px] font-medium"
            >
              Hủy
            </button>
            <button
              type="submit"
              :disabled="!isFormValid"
              class="w-full md:w-auto px-6 py-4 md:py-3 bg-primary text-white rounded-xl md:rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed md:min-w-[120px] font-medium"
            >
              {{ isEdit ? "Cập nhật" : "Thêm" }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  transaction: {
    type: Object,
    default: null,
  },
  categories: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["close", "submit"]);

const form = reactive({
  type: "income",
  amount: 0,
  categoryId: "",
  description: "",
  paymentMethod: "cash",
  date: new Date().toISOString().slice(0, 16),
});

const formattedAmount = ref("");

const paymentMethods = [
  {
    value: "cash",
    label: "Tiền mặt",
    icon: "M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z",
  },
  {
    value: "bank",
    label: "Chuyển khoản",
    icon: "M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z",
  },
  {
    value: "card",
    label: "Thẻ",
    icon: "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z",
  },
];

const filteredCategories = computed(() => {
  return props.categories.filter((cat) => cat.type === form.type);
});

const isFormValid = computed(() => {
  return (
    form.type &&
    form.amount > 0 &&
    form.categoryId &&
    form.paymentMethod &&
    form.date
  );
});

const updateAmount = (event) => {
  const value = event.target.value.replace(/[^\d]/g, "");
  form.amount = parseInt(value) || 0;
  formattedAmount.value = formatCurrency(form.amount);
};

const handleSubmit = () => {
  if (isFormValid.value) {
    emit("submit", { ...form });
  }
};

// Initialize form if editing
onMounted(() => {
  if (props.isEdit && props.transaction) {
    Object.assign(form, {
      type: props.transaction.type,
      amount: props.transaction.amount,
      categoryId: props.transaction.category?.id || "",
      description: props.transaction.description || "",
      paymentMethod: props.transaction.paymentMethod,
      date: new Date(props.transaction.date).toISOString().slice(0, 16),
    });
    formattedAmount.value = formatCurrency(form.amount);
  }
});

// Watch for transaction changes (when editing different transactions)
watch(
  () => props.transaction,
  (newTransaction) => {
    if (props.isEdit && newTransaction) {
      Object.assign(form, {
        type: newTransaction.type,
        amount: newTransaction.amount,
        categoryId: newTransaction.category?.id || "",
        description: newTransaction.description || "",
        paymentMethod: newTransaction.paymentMethod,
        date: new Date(newTransaction.date).toISOString().slice(0, 16),
      });
      formattedAmount.value = formatCurrency(form.amount);
    }
  },
  { immediate: true }
);

// Watch for edit mode changes to reset form when switching to add mode
watch(
  () => props.isEdit,
  (isEdit) => {
    if (!isEdit) {
      // Reset form for add mode
      Object.assign(form, {
        type: "income",
        amount: 0,
        categoryId: "",
        description: "",
        paymentMethod: "cash",
        date: new Date().toISOString().slice(0, 16),
      });
      formattedAmount.value = "";
    }
  }
);
</script>

<style scoped>
/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hide scrollbar on mobile for cleaner look */
  .overflow-y-auto::-webkit-scrollbar {
    display: none;
  }

  .overflow-y-auto {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Focus styles for better mobile accessibility */
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
