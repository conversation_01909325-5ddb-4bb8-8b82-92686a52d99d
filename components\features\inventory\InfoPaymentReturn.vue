<template>
  <div class="pb-2">
    <div class="text-primary font-bold mt-2">
      <span class="mx-2 font-bold text-md">T<PERSON><PERSON> hàng</span>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="">Số lượng hàng trả</div>
      <div>{{ totalProductReturn }}</div>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="">Tổng giá gốc hàng trả</div>
      <div class="text-red-600 font-semibold">
        {{ formatCurrency(totalPriceProductReturn) }}
      </div>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="">Phí trả hàng</div>
      <input
        type="text"
        placeholder="Phí trả hàng"
        class="border bg-secondary rounded p-1 w-[40%] px-2 outline-none"
        v-model="returnStore.feeReturnOrder"
      />
    </div>
    <div class="flex items-center justify-between mx-2 border-t mt-2">
      <div class="font-bold">Tổng tiền trả</div>
      <div class="text-red-600 font-semibold">
        {{
          orderChooseReturn?.length > 0
            ? formatCurrency(
                +totalPriceProductReturn - +returnStore.feeReturnOrder
              )
            : formatCurrency(0)
        }}
      </div>
    </div>
    <!-- <div class="border-t my-2"></div>
    <div class="mx-2 text-primary font-semibold">Phương thức hoàn tiền</div>
    <div class="mx-2 mt-1">
      <select
        id="employee-select"
        class="block w-full outline-none px-2 py-1 rounded cursor-pointer border"
        v-model="returnStore.paymentMethodReturnOrder"
      >
        <option
          v-for="paymentMethod in paymentMethodReturn"
          :key="paymentMethod.id"
          :value="paymentMethod"
        >
          {{ paymentMethod.name }}
        </option>
      </select>
    </div> -->
    <!-- phí trả hàng -->
  </div>
</template>
<script setup>
const paymentMethodReturn = ref([
  {
    id: "1",
    name: "Hoàn tiền",
    paymentMethod: "refund",
    type: "REFUND_INVOICE",
  },
  {
    id: "2",
    name: "Cấn trừ",
    paymentMethod: "clearing_debt",
    type: "CLEARING_DEBT",
  },
]);
const props = defineProps([
  "orderReturn",
  "orderChooseReturn",
  "totalPriceProductReturn",
  "totalProductReturn",
]);
const returnStore = returnOrderStore();
</script>
