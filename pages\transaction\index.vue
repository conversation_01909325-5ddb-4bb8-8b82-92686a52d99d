<template>
  <div class="h-[calc(100vh-56px)] flex flex-col mx-2">
    <!-- Header -->
    <div class="flex-shrink-0 mb-2">
      <TransactionHeader
        :data="dataPagination"
        @export="handleExport"
        @add-transaction="() => {}"
        @date-change="handleDateChange"
      />
    </div>

    <!-- Filters -->
    <div class="flex-shrink-0">
      <TransactionFilters @filter-change="handleFilterChange" />
    </div>

    <!-- Desktop Table Container -->
    <div class="hidden md:block flex-1 min-h-0">
      <div
        class="h-full flex flex-col bg-white rounded-lg border border-gray-200"
      >
        <TransactionTable
          :transactions="transactions"
          :current-page="dataPagination?.index"
          :total-pages="Math.ceil((dataPagination?.total ?? 0) / 10)"
          :total-items="dataPagination?.total"
          :items-per-page="10"
          @view-detail="handleViewTransactionDetail"
          @page-change="handlePageChange"
        />
      </div>
    </div>

    <!-- Mobile Cards Container -->
    <div class="md:hidden flex-1 min-h-0 overflow-hidden">
      <div class="h-full flex flex-col">
        <!-- Mobile Cards List -->
        <div
          class="flex-1 overflow-y-auto overflow-x-hidden py-2"
          style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
        >
          <div v-if="transactions.length > 0" class="space-y-3">
            <TransactionMobileCard
              v-for="transaction in transactions"
              :key="transaction?.id"
              :transaction="transaction"
              @view-detail="handleViewTransactionDetail"
            />
          </div>

          <!-- Mobile Empty State -->
          <div v-else class="flex items-center justify-center h-full">
            <div class="text-center py-12">
              <svg
                class="w-12 h-12 text-gray-400 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Chưa có giao dịch nào
              </h3>
              <p class="text-gray-500 mb-4">
                Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn
              </p>
              <button
                @click="() => {}"
                class="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Thêm giao dịch
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Pagination -->
        <div
          class="flex-shrink-0 p-4 bg-white border-t border-gray-200"
          style="min-height: 70px"
        >
          <div class="flex items-center justify-center gap-2">
            <button
              @click="handlePageChange(pagination.currentPage - 1)"
              :disabled="pagination.currentPage <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
            >
              Trước
            </button>
            <span class="px-3 py-2 text-sm text-gray-700">
              {{ pagination.currentPage }} / {{ pagination.totalPages }}
            </span>
            <button
              @click="handlePageChange(pagination.currentPage + 1)"
              :disabled="pagination.currentPage >= pagination.totalPages"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
            >
              Sau
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Transaction Detail Modal -->
    <TransactionDetailModal
      :is-open="showTransactionDetail"
      :transaction="selectedTransaction"
      @close="closeTransactionDetail"
    />
  </div>
</template>

<script setup lang="ts">
import type { Transaction, TransactionFilters } from "~/types/Transaction";

// SEO
useHead({
  title: "Quản lý giao dịch",
  meta: [
    {
      name: "description",
      content: "Quản lý giao dịch thanh toán doanh nghiệp",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN", "SALE_MANAGER"],
  name: "Quản lý giao dịch",
});

// State
const loading = ref<boolean>(false);
const showTransactionDetail = ref<boolean>(false);
const selectedTransaction = ref<Transaction | null>(null);

// Legacy Category interface for backward compatibility
interface Category {
  id: number;
  name: string;
  type: "income" | "expense";
  description?: string;
  icon?: string;
}

// Data
const transactions = ref<Transaction[]>([]);
const categories = ref<Category[]>([]);

const dataPagination = ref();
// Filters
const filters = reactive<TransactionFilters>({
  status: undefined,
  paymentGatewayType: undefined,
  paymentConfirmStatus: undefined,
  purpose: undefined,
  bankCode: "",
  gateway: "",
  dateFrom: undefined,
  dateTo: undefined,
  amountFrom: undefined,
  amountTo: undefined,
  search: "",
});

const dateRange = reactive({
  start: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    .toISOString()
    .split("T")[0],
  end: new Date().toISOString().split("T")[0],
});

// Pagination
const pagination = reactive({
  currentPage: 1,
  itemsPerPage: 10, // Further reduced to ensure pagination shows
  totalItems: 0,
  totalPages: 1,
});

// Computed
const { searchTransactions, getCashbookTransactionDetail } = useCashbook();
interface DataRequest {
  keyword: string;
  dateFrom: number | null;
  dateTo: number | null;
  currentPage: number;
  pageSize: number;
}
const dataRequest = reactive<DataRequest>({
  keyword: "",
  dateFrom: null,
  dateTo: null,
  currentPage: 1,
  pageSize: 10,
});
// Methods
const loadTransactions = async (data: any) => {
  try {
    const response = await searchTransactions(
      data?.keyword,
      data?.dateFrom,
      data?.dateTo,
      data?.currentPage,
      data?.pageSize
    );

    dataPagination.value = response;
    console.log("dataPagination", dataPagination.value);
    transactions.value = response?.resultList;
  } catch (error) {
    console.error("Error loading transactions:", error);
  }
};

// Event handlers
const handleFilterChange = async (newFilters: any) => {
  console.log("newFilters", Date.parse(newFilters.dateRange[0]));
  dataRequest.dateFrom = Date.parse(newFilters.dateRange[0]);
  dataRequest.dateTo = Date.parse(newFilters.dateRange[1]);
  await loadTransactions(dataRequest);
};

const handleDateChange = (newDateRange: any) => {
  Object.assign(dateRange, newDateRange);
};

const handleViewTransactionDetail = (transaction: Transaction) => {
  selectedTransaction.value = transaction;
  showTransactionDetail.value = true;
};

const closeTransactionDetail = () => {
  showTransactionDetail.value = false;
  selectedTransaction.value = null;
};

const handlePageChange = async (page: number) => {
  console.log("page", page);
  dataRequest.currentPage = page;
  await loadTransactions(dataRequest);

  // pagination.currentPage = page;
};

const handleExport = () => {
  // Mock export functionality
  console.log("Exporting transactions...");
};
// Lifecycle
onMounted(async () => {
  await loadTransactions(dataRequest);
});
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
