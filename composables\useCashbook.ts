import { useNuxtApp } from "#app";

export default function useCashbook() {
  const $sdk = useNuxtApp().$sdk;

  const searchTransactions = async (
    keyword: string,
    dateFrom: number,
    dateTo: number,
    currentPage: number,
    pageSize: number
  ) => {
    try {
      const response = await $sdk.cashbook.searchTransactions(
        keyword,
        dateFrom,
        dateTo,
        currentPage,
        pageSize
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getCashbookTransactionDetail = async (cashTransactionId: string) => {
    try {
      const response = await $sdk.cashbook.getCashbookTransactionDetail(
        cashTransactionId
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    searchTransactions,
    getCashbookTransactionDetail,
  };
}
