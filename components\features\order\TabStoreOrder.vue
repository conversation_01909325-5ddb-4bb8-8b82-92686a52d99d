<template>
  <div class="flex flex-wrap gap-2 items-center text-sm overflow-hidden">
    <transition name="modal-fade">
      <div class="w-full transition-all duration-300 transform overflow-hidden">
        <div
          ref="tabContainer"
          class="relative flex overflow-x-auto scrollbar-hide"
        >
          <button
            v-for="(store, index) in dataStore"
            :key="store?.id"
            ref="tabButtons"
            @click="handleClickStore(store, index)"
            class="h-8 px-4 text-center relative transition-all duration-300 flex items-center justify-center whitespace-nowrap flex-shrink-0"
          >
            {{ store?.name }}
          </button>

          <!-- <PERSON><PERSON> gạch chân có animation -->
          <div
            class="absolute bottom-0 h-1 bg-primary transition-transform duration-300 ease-in-out"
            :style="{
              width: `${underlineWidth}px`,
              transform: `translateX(${underlineLeft}px)`,
            }"
          ></div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
const { getStoreChannelIdsByEmployeeId, getDetailStoreV2 } = useStore();
const dataStore = ref();
const selectedOrderTerm = ref(null);
const underlineLeft = ref(0);
const underlineWidth = ref(0);
const tabButtons = ref<any>([]);
const tabContainer = ref<HTMLElement>();

const getData = async (employeeId: string) => {
  dataStore.value = [];
  try {
    const response = await getStoreChannelIdsByEmployeeId(employeeId);
    const results = await Promise.allSettled(
      response.map((item: any) => getDetailStoreV2(item))
    );
    dataStore.value = results
      .filter((res) => res.status === "fulfilled" && res.value?.enable === true)
      .map((res: any) => res.value);
  } catch (error) {
    console.error("Lỗi khi lấy dữ liệu cửa hàng:", error);
  }
};
const emits = defineEmits(["setStore"]);
const handleClickStore = async (store: any, index: number) => {
  selectedOrderTerm.value = store?.id;
  await nextTick();
  if (tabButtons.value[index]) {
    const tabElement: any = tabButtons.value[index];
    underlineLeft.value = tabElement.offsetLeft;
    underlineWidth.value = tabElement.offsetWidth;

    // Auto scroll to selected tab if it's out of view
    scrollToTab(tabElement);
  }
  useCookie("warehouse").value = store.warehouses;
  useCookie("warehouseId").value = store.warehouseIdDefault
    ? store.warehouseIdDefault
    : store?.warehouses?.[0];
  emits("setStore", store);
};

// Function to scroll tab into view
const scrollToTab = (tabElement: HTMLElement) => {
  const container = tabContainer.value;
  if (!container || !tabElement) return;

  const containerRect = container.getBoundingClientRect();
  const tabRect = tabElement.getBoundingClientRect();

  // Check if tab is out of view
  if (
    tabRect.right > containerRect.right ||
    tabRect.left < containerRect.left
  ) {
    // Calculate scroll position to center the tab
    const scrollLeft =
      tabElement.offsetLeft -
      container.clientWidth / 2 +
      tabElement.offsetWidth / 2;
    container.scrollTo({
      left: Math.max(0, scrollLeft),
      behavior: "smooth",
    });
  }
};
const auth = useCookie("auth") as any;

const route = useRoute();
onMounted(async () => {
  await getData(auth.value?.user?.id);
  await nextTick();
  if (tabButtons.value.length > 0) {
    const index = dataStore.value.findIndex(
      (store: any) => store?.id === route.query.storeId
    );
    if (index >= 0) {
      selectedOrderTerm.value = dataStore.value[index]?.id;
      underlineLeft.value = tabButtons.value[index].offsetLeft;
      underlineWidth.value = tabButtons.value[index].offsetWidth;
      // Auto scroll to the selected tab
      scrollToTab(tabButtons.value[index]);
      return;
    }
    // Default to first tab
    selectedOrderTerm.value = dataStore.value[0]?.id;
    underlineLeft.value = tabButtons.value[0].offsetLeft;
    underlineWidth.value = tabButtons.value[0].offsetWidth;
  }
});
</script>

<style>
/* Hiệu ứng modal */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Smooth scrolling */
.scrollbar-hide {
  scroll-behavior: smooth;
}

/* Ensure container doesn't overflow */
.overflow-hidden {
  overflow: hidden !important;
}

/* Prevent horizontal scroll on parent elements */
.flex-wrap {
  flex-wrap: nowrap !important;
}
</style>
