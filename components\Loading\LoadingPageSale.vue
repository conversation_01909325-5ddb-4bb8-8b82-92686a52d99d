<template>
  <div class="animate-pulse">
    <div class="h-full overflow-y-scroll md:overflow-hidden">
      <div class="items-center gap-[10px] px-2"></div>
      <div class="w-full relative h-full">
        <div class="wrapper lg:my-0 my-0 px-2 md:[px-15] h-full">
          <div class="w-full relative h-full-custom">
            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-2 md:mb-4 md:h-full z-0"
            >
              <div
                class="w-full col-span-2 relative bg-white rounded h-full md:order-1"
              >
                <div class="block md:hidden border-b">
                  <div class="flex items-center justify-between p-1">
                    <div class="w-20 h-6 bg-secondary"></div>
                    <div class="flex items-center justify-center gap-2 mr-2">
                      <div class="bg-secondary w-20 h-6"></div>
                      <div class="bg-secondary w-20 h-6"></div>
                    </div>
                  </div>
                </div>
                <div class="p-2 space-y-2">
                  <div class="w-20 h-6 bg-secondary"></div>
                  <div class="w-full h-10 bg-secondary rounded"></div>
                </div>
                <div class="p-2 space-y-2">
                  <div class="w-40 h-6 bg-secondary"></div>
                  <div v-for="item in 2" class="flex items-center gap-2 w-full">
                    <div class="w-6 h-6 bg-secondary"></div>
                    <div class="w-14 h-14 rounded bg-secondary"></div>
                    <div class="space-y-1">
                      <div class="w-[200px] h-6 bg-secondary"></div>

                      <div class="w-[100px] h-6 bg-secondary"></div>
                      <div class="w-[100px] h-6 bg-secondary"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="w-full col-span-2 md:col-span-1 order-1 md:order-2 mb-24 bg-white"
              >
                <div
                  class="'flex flex-col gap-1 md:overflow-y-scroll md:h-screen-150 bg-white'"
                >
                  <div class="md:block hidden border-b">
                    <div class="flex items-center justify-between p-1 bg-white">
                      <div class="w-20 h-6 bg-secondary"></div>
                      <div class="flex items-center justify-center gap-2 mr-2">
                        <div class="bg-secondary w-20 h-6"></div>
                        <div class="bg-secondary w-20 h-6"></div>
                      </div>
                    </div>
                  </div>
                  <div class="border-b pb-1">
                    <div
                      class="bg-white flex items-center justify-between mx-1"
                    >
                      <div class="w-20 h-6 bg-secondary"></div>
                      <div class="w-20 h-6 bg-secondary"></div>
                    </div>
                    <div class="bg-secondary h-6 m-1 py-4 rounded"></div>

                    <div class="border-b pb-1"></div>
                    <div
                      v-for="item in 3"
                      class="bg-secondary w-[250px] h-6 my-2"
                    ></div>
                    <div class="bg-secondary h-6 w-[100px]"></div>
                    <div
                      v-for="item in 3"
                      class="flex items-center justify-between my-2"
                    >
                      <div class="bg-secondary w-[180px] h-6"></div>
                      <div class="bg-secondary w-[80px] h-6"></div>
                    </div>
                    <div class="bg-secondary h-6 m-1 py-4 rounded"></div>

                    <div
                      v-for="item in 3"
                      class="flex items-center justify-between my-2"
                    >
                      <div class="bg-secondary w-[180px] h-6"></div>
                      <div class="bg-secondary w-[80px] h-6"></div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center justify-center gap-4">
                  <div class="bg-secondary w-[100px] h-[40px]"></div>
                  <div class="bg-secondary w-[100px] h-[40px]"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts"></script>
