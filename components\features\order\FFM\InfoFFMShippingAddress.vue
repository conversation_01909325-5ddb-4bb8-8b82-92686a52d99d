<template>
  <div class="space-y-2 bg-white rounded">
    <div class="flex items-center gap-2 mb-2">
      <div class="w-1/2">
        <label class="text-sm">Đ<PERSON>a chỉ giao hàng</label>
        <input
          type="text"
          placeholder="Số nhà, tên đường"
          class="w-full p-1 rounded outline-none border bg-secondary"
          v-model="address"
          @change="handleSaveShippingAddress"
        />
      </div>
      <div class="w-1/2">
        <label class="text-sm">Số điện thoại</label>
        <input
          type="text"
          placeholder="Số điện thoại"
          class="w-full p-1 rounded outline-none border bg-secondary"
          v-model="phone"
          @change="handleSaveShippingAddress"
        />
      </div>
    </div>

    <div class="flex items-center gap-2">
      <div class="w-1/3">
        <label class="text-sm">Tỉnh/Thành phố</label>
        <select
          class="w-full p-1 rounded bg-secondary outline-none border"
          v-model="selectedCity"
          @change="fetchDistricts"
        >
          <option value="">Chọn thành phố/tỉnh</option>
          <option
            v-for="province in provinces"
            :key="province.geoId"
            :value="province.geoId"
          >
            {{ province.geoName }}
          </option>
        </select>
      </div>

      <div class="w-1/3">
        <label class="text-sm">Quận/Huyện</label>
        <select
          class="w-full p-1 rounded bg-secondary outline-none border"
          v-model="selectedDistrict"
          @change="fetchWards"
        >
          <option value="">Chọn quận/huyện</option>
          <option
            v-for="district in districts"
            :key="district.geoId"
            :value="district.geoId"
          >
            {{ district.geoName }}
          </option>
        </select>
      </div>

      <div class="w-1/3">
        <label class="text-sm">Phường/xã</label>
        <select
          class="w-full p-1 rounded bg-secondary outline-none border"
          v-model="selectedWard"
          @change="handleSaveShippingAddress"
        >
          <option value="">Chọn phường/xã</option>
          <option v-for="ward in wards" :key="ward.geoId" :value="ward.geoId">
            {{ ward.geoName }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const provinces = ref<any>([]);
const districts = ref<any>([]);
const wards = ref<any>([]);

const selectedCity = ref("");
const selectedDistrict = ref("");
const selectedWard = ref("");

const address = ref("");
const phone = ref("");

const isInitializing = ref(true); // Ngăn cập nhật khi trang load lần đầu

const props = defineProps(["order", "dataFFM", "shippingAddress", "customer"]);
const emit = defineEmits(["fetchFFMStatus"]);

const { $sdk } = useNuxtApp();
const auth = useCookie("auth") as any;
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { updateOrderCustomer } = useOrder();

const getProvinces = async () => {
  try {
    provinces.value = await $sdk.user.getProvinces();
  } catch (error) {
    console.error("Error fetching provinces:", error);
  }
};

const fetchDistricts = async () => {
  try {
    districts.value = await $sdk.user.getDistricts(selectedCity.value);
    selectedDistrict.value = "";
    selectedWard.value = "";
    wards.value = [];
  } catch (error) {
    console.error("Error fetching districts:", error);
  }
};

const fetchWards = async () => {
  try {
    wards.value = await $sdk.user.getWards(selectedDistrict.value);
  } catch (error) {
    console.error("Error fetching wards:", error);
  }
};

onMounted(async () => {
  isInitializing.value = true;

  phone.value = props.order.order.shippingAddress.phone;
  address.value = props.order.order.shippingAddress.address1;

  await getProvinces();
  selectedCity.value = props.order.order.shippingAddress?.provinceCode;

  await fetchDistricts();
  selectedDistrict.value = props.order.order.shippingAddress?.districtCode;

  await fetchWards();
  selectedWard.value = props.order.order.shippingAddress?.wardCode;

  isInitializing.value = false;
});

const handleSaveShippingAddress = async () => {
  const res = $sdk.order.validatePhoneNumber(phone.value);
  if (!res) {
    useNuxtApp().$toast.warning("Số điện thoại không hợp lệ");
    return;
  }

  if (!selectedCity.value || !selectedDistrict.value || !selectedWard.value) {
    useNuxtApp().$toast.warning("Vui lòng cập nhật đầy đủ địa chỉ nhận hàng");
    return;
  }

  const data = {
    name: props.order.order.shippingAddress.name,
    phone: phone.value,
    address: address.value,
    province_code: selectedCity.value,
    district_code: selectedDistrict.value,
    ward_code: selectedWard.value,
    address_default: true,
  };

  const response = await orderStore.updateShippingAddress(
    props.customer.id,
    props.shippingAddress.id,
    auth.value.user.id,
    data
  );

  await customerStore.handleUpdateShippingAddress(
    response?.data,
    props.customer.id
  );

  await updateOrderCustomer(
    orderStore.orderDetail?.id,
    props.customer.id,
    props.shippingAddress.id
  );

  await orderStore.updateOrder(orderStore.orderDetail?.id);
};

// --- Watchers ---
watch(selectedCity, async (newVal) => {
  if (isInitializing.value || !newVal) return;
  selectedDistrict.value = "";

  await handleSaveShippingAddress();
  console.log(1);
});
watch(selectedDistrict, async (newVal) => {
  if (isInitializing.value || !newVal) return;
  await handleSaveShippingAddress();
  selectedWard.value = "";
  console.log(2);
});
// watch(
//   selectedWard,
//   async (newVal) => {
//     if (isInitializing.value || !newVal) return;
//     console.log("first");
//     await handleSaveShippingAddress();
//     console.log(3);
//   },
//   { flush: "post" }
// );
</script>
