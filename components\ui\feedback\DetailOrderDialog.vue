<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div class="bg-secondary rounded-lg md:w-[92%] relative p-2">
      <!-- Nút đóng -->
      <button
        @click="cancel"
        class="absolute top-0 right-1 text-red-600 hover:scale-110 transition-transform"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-6 h-6"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
          />
        </svg>
      </button>

      <!-- Nội dung modal -->
      <div class="h-[80vh] max-h-[800px] overflow-y-auto">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
          <!-- Cột trái -->
          <div class="md:col-span-2 bg-white p-4 rounded">
            <SearchOrderCustomer />
            <div class="mt-4">
              <CustomerItemOrder
                v-for="product in order?.activeOrderItemProfiles"
                :key="product.id"
                :product="product.orderLineItem"
              />
            </div>
          </div>

          <!-- Cột phải -->
          <div
            class="md:col-span-1 bg-white p-4 rounded border-l border-gray-200"
          >
            <!-- Mã đơn hàng -->
            <div class="">
              <span class="text-primary font-bold">#{{ order?.id }}</span>
              <span :class="getStatusClass(order?.status)">{{
                order?.statusDescription
              }}</span>
            </div>

            <!-- Thông tin khách hàng -->
            <CustomerOrder
              :name="order?.order?.ownerName"
              :phone="order?.order?.ownerPhone"
            />

            <!-- Thanh toán -->
            <CustomerInfoPayment :order="order" />

            <!-- Chỉnh sửa đơn hàng -->
            <CustomerEditOrder :order="order" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
const emit = defineEmits(["cancel"]);
const props = defineProps(["order"]);

const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const getStatusClass = (statusDescription) => {
  switch (statusDescription) {
    case "REQUEST":
      return "text-[#5BC0EB] rounded-full px-2 py-1 text-sm font-semibold";
    case "OPEN":
      return "text-[#64B5F6] rounded-full px-2 py-1 text-sm font-semibold";
    case "APPROVED":
      return "text-[#4CAF50] rounded-full px-2 py-1 text-sm font-semibold";
    case "IN_PROGRESS":
      return "text-[#FFB74D] rounded-full px-2 py-1 text-sm font-semibold";
    case "WAIT_DELIVERY":
      return "text-[#FF9800] rounded-full px-2 py-1 text-sm font-semibold";
    case "PROCESSING_DELIVERY":
      return "text-[#2196F3] rounded-full px-2 py-1 text-sm font-semibold";
    case "COMPLETED":
      return "text-[#388E3C] rounded-full px-2 py-1 text-sm font-semibold";
    case "RETURNING":
      return "text-[#BA68C8] rounded-full px-2 py-1 text-sm font-semibold";
    case "RETURNED":
      return "text-[#E57373] rounded-full px-2 py-1 text-sm font-semibold";
    default:
      return "text-[#636AE8] rounded-full px-2 py-1 text-sm font-semibold";
  }
};
</script>

<style scoped></style>
