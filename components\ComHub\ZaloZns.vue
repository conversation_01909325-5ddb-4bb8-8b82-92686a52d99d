<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">
          {{
            typeSendZns === "SEND_ORDER_INFO"
              ? "Gửi thông tin đơn hàng"
              : "Gửi đánh giá "
          }}
        </div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 md:size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[300px] overflow-y-auto">
        <!-- phần chọn template -->
        <!-- <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40">Chọn mẫu</label>
          <select
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="selectedTemplate"
          >
            <option
              v-for="templates in template"
              :key="templates.id"
              :value="templates"
            >
              {{ templates.name }}
            </option>
          </select>
        </div> -->
        <!-- phần review template -->
        <div>
          <div v-if="typeSendZns === 'SEND_ORDER_INFO'">
            <div class="font-semibold">Xác nhận đơn mua hàng</div>
            <div>
              Cảm ơn
              <span class="font-semibold">{{
                orderDetail?.order?.ownerName
              }}</span>
              đã mua hàng tại cửa hàng. Đơn hàng của bạn đã được xác nhận với
              chi tiết như sau:
            </div>
            <!--  -->
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Khách hàng</div>
              <div class="font-semibold">
                {{ customer?.name }}
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Điện thoại</div>
              <div class="font-semibold">
                {{ customer?.phone }}
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Mã đơn</div>
              <div class="font-semibold">{{ orderDetail?.id }}</div>
            </div>
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Trạng thái</div>
              <div class="font-semibold">
                {{ orderDetail?.statusDescription }}
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Ngày đặt hàng</div>
              <div class="font-semibold">
                {{ formatTimestampV2(orderDetail?.order?.createdStamp) }}
              </div>
            </div>
            <div class="flex items-center gap-4">
              <div class="w-[120px]">Giá trị đơn hàng</div>
              <div class="font-semibold">
                {{
                  formatCurrency(orderDetail?.order?.totalPrice?.amount || 0)
                }}
              </div>
            </div>
          </div>
          <div v-if="typeSendZns === 'SEND_ORDER_RATE'">
            <div class="font-semibold">Đánh giá đơn hàng</div>
            <div>
              Cảm ơn quý khách
              <span class="font-semibold">{{
                orderDetail?.order?.ownerName
              }}</span>
              đã tin tưởng mua hàng tại cửa hàng với đơn hàng
              <span class="font-semibold">{{ orderDetail?.id }}</span> của chúng
              tôi vào ngày
              <span class="font-semibold">
                {{ formatTimestampV2(orderDetail?.order?.createdStamp) }}
              </span>
              . Quý khách vui lòng đánh giá chất lượng dịch vụ và sản phẩm sau
              khi mua hàng để chúng tôi có thể phục vụ quý khách tốt hơn trong
              lần mua hàng tiếp theo
            </div>
            <div
              v-if="orderDetail.status !== 'COMPLETED'"
              class="bg-yellow-100 py-2 px-1 rounded my-2"
            >
              <span class="font-semibold">*Lưu ý: </span>
              <span class="">
                Đơn hàng hàng chưa hoàn tất. Vui lòng kiểm tra trước khi gửi
                đánh giá</span
              >
              <!-- <span class="font-semibold text-primary">{{
                orderDetail?.statusDescription
              }}</span> -->
            </div>
          </div>
        </div>
      </div>
      <!-- nút xác nhận -->
      <div class="flex items-center justify-end mt-2 gap-2">
        <button @click="cancel" class="px-4 py-1 bg-secondary rounded">
          Hủy
        </button>
        <button
          @click="handleConfirm"
          class="bg-primary text-white px-4 py-1 rounded"
        >
          {{
            typeSendZns === "SEND_ORDER_INFO"
              ? "Gửi thông tin đơn hàng"
              : "Gửi đánh giá "
          }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(["confirm", "cancel"]);
const props = defineProps(["orderDetail", "typeSendZns"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const { getInfoChat } = useComhub();
const template = ref([
  {
    id: 1,
    name: "Gửi thông tin đơn hàng",
    type: "SEND_ORDER_INFO",
  },
  {
    id: 2,
    name: "Đánh giá dịch vụ",
    type: "SEND_ORDER_RATE",
  },
]);
const selectedTemplate = ref({
  id: 1,
  name: "Gửi thông tin đơn hàng",
  type: "SEND_ORDER_INFO",
});
const auth = useCookie("auth").value;
const handleConfirm = () => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");
  const auth = useCookie("auth").value;

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    return;
  }

  const znsId = appId.find((app) => app.name === "ZNS");
  if (!znsId?.apps.length) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    return;
  }
  if (!props.orderDetail?.order?.ownerPartyId) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    return;
  }
  let template;
  if (props.typeSendZns === "SEND_ORDER_INFO") {
    template = {
      templateData: {
        status: props.orderDetail?.statusDescription,
        orderId: props.orderDetail?.id,
        totalPrice: `${props.orderDetail?.order?.totalPrice?.amount}`,
        createdStamp: formatTimestampV2(props.orderDetail?.order?.orderDate),
        customerName: customer.value?.name,
        customerPhone: customer.value?.phone,
        orderIdUrl: props.orderDetail?.id,
      },
      resourceId: props.orderDetail?.id,
      resourceType: "ORDER",
      senderPartyId: auth?.user?.id,
      // receivePartyIds: [props.orderDetail?.order?.ownerPartyId],
      receiveContactIds: [customer.value?.phone],
    };
  } else {
    template = {
      templateData: {
        orderId: props.orderDetail?.id,
        createdStamp: formatTimestampV2(props.orderDetail?.order?.orderDate),
        customerName: props.orderDetail?.order?.ownerName,
      },
      resourceId: props.orderDetail?.id,
      resourceType: "ORDER",
      senderPartyId: auth?.user?.id,
      // receivePartyIds: [props.orderDetail?.order?.ownerPartyId],
      receiveContactIds: [customer.value?.phone],
    };
  }
  const data = {
    templateData: template,
    templateType: props.typeSendZns,
    app: znsId,
  };
  emit("confirm", data);
};
const { getCustomerById } = useCustomer();
const customer = ref();
const handleGetCustomer = async () => {
  try {
    const response = await getCustomerById(
      props.orderDetail?.order?.ownerPartyId
    );
    customer.value = response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  Promise.allSettled([handleGetCustomer(), getInfoChat()]);
});
</script>
