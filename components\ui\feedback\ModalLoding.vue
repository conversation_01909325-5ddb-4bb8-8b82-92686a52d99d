<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-8 rounded-lg w-96">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-bold"><PERSON><PERSON> lấy danh sách cửa hàng</h2>
        <div
          class="h-2 w-2 bg-black rounded-full animate-bounce [animation-delay:-0.3s]"
        ></div>
        <div
          class="h-2 w-2 bg-black rounded-full animate-bounce [animation-delay:-0.15s]"
        ></div>
        <div class="h-2 w-2 bg-black rounded-full animate-bounce"></div>
      </div>
      <div class="mt-4">
        <p class="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup></script>
