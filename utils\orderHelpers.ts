/**
 * Order Helper Utilities
 * Centralized functions for handling order-related operations
 */

// Type definitions
export interface CustomAttribute {
  key: string;
  value: string;
}

export interface OrderWithCustomAttributes {
  customAttributes?: CustomAttribute[];
}

/**
 * Get print count from order custom attributes
 * @param customAttributes - Array of custom attributes or order object
 * @returns Formatted print count string with parentheses
 */
export const getPrintCount = (customAttributes: CustomAttribute[] | any): string => {
  // Handle case where the parameter is an order object with customAttributes
  let attributes: CustomAttribute[] = [];
  
  if (Array.isArray(customAttributes)) {
    attributes = customAttributes;
  } else if (customAttributes?.customAttributes) {
    attributes = customAttributes.customAttributes;
  } else if (customAttributes?.order?.customAttributes) {
    attributes = customAttributes.order.customAttributes;
  }

  const printTimesAttr = attributes?.find((item: CustomAttribute) => item.key === "printTimes");
  
  if (printTimesAttr) {
    return `(${printTimesAttr.value})`;
  } else {
    return "(0)";
  }
};

/**
 * Update print count in order custom attributes
 * @param order - Order object with custom attributes
 * @returns Updated order object
 */
export const updatePrintCount = (order: any): any => {
  if (!order?.customAttributes) {
    return order;
  }

  const printTimesAttr = order.customAttributes.find(
    (attr: CustomAttribute) => attr.key === "printTimes"
  );

  if (printTimesAttr) {
    // Increment existing print count
    printTimesAttr.value = String(Number(printTimesAttr.value) + 1);
  } else {
    // Add new print count attribute
    order.customAttributes.push({ key: "printTimes", value: "1" });
  }

  return order;
};

/**
 * Update print count in order list
 * @param orderList - Array of orders
 * @param orderId - ID of the order to update
 * @returns Updated order list
 */
export const updatePrintCountInList = (orderList: any[], orderId: string): any[] => {
  const orderIndex = orderList.findIndex((order: any) => order.id === orderId);

  if (orderIndex !== -1) {
    const customAttributes = orderList[orderIndex].order.customAttributes;
    const printTimesAttr = customAttributes.find(
      (attr: CustomAttribute) => attr.key === "printTimes"
    );

    if (printTimesAttr) {
      // Increment existing print count
      printTimesAttr.value = String(Number(printTimesAttr.value) + 1);
    } else {
      // Add new print count attribute
      customAttributes.push({ key: "printTimes", value: "1" });
    }
  }

  return orderList;
};

/**
 * Get invoice number from order custom attributes
 * @param order - Order object
 * @returns Invoice number or null
 */
export const getInvoiceNumber = (order: any): string | null => {
  const invoiceAttr = order?.customAttributes?.find(
    (attr: CustomAttribute) => attr.key === "invoiceNumber"
  );
  
  return invoiceAttr?.value || null;
};

/**
 * Check if order has invoice published
 * @param order - Order object
 * @returns Boolean indicating if invoice is published
 */
export const hasInvoicePublished = (order: any): boolean => {
  return order?.customAttribute?.exportVatInvoiceStatus === 'INVOICE_PUBLISHED';
};

/**
 * Get order total amount with fallback
 * @param order - Order object
 * @returns Total amount or 0
 */
export const getOrderTotal = (order: any): number => {
  return order?.order?.totalPrice?.amount || order?.totalPrice?.amount || 0;
};

/**
 * Get order remaining amount
 * @param order - Order object
 * @returns Remaining amount or 0
 */
export const getOrderRemaining = (order: any): number => {
  return order?.remainTotal || 0;
};

/**
 * Check if order can be paid
 * @param order - Order object
 * @returns Boolean indicating if order can be paid
 */
export const canPayOrder = (order: any): boolean => {
  const remaining = getOrderRemaining(order);
  return remaining > 0;
};

/**
 * Get order status description
 * @param order - Order object
 * @returns Status description string
 */
export const getOrderStatusDescription = (order: any): string => {
  return order?.status || order?.order?.status || "";
};

/**
 * Get order financial status description
 * @param order - Order object
 * @returns Financial status description string
 */
export const getOrderFinancialStatus = (order: any): string => {
  return order?.financialStatusDescription || "";
};

/**
 * Get order fulfillment status
 * @param order - Order object
 * @returns Fulfillment status string
 */
export const getOrderFulfillmentStatus = (order: any): string => {
  return order?.order?.fulfillmentStatus || order?.fulfillmentStatus || "";
};

/**
 * Check if order is draft
 * @param order - Order object
 * @returns Boolean indicating if order is draft
 */
export const isOrderDraft = (order: any): boolean => {
  const status = getOrderStatusDescription(order);
  return status === "DRAFT" || status === "draft";
};

/**
 * Get order note
 * @param order - Order object
 * @returns Order note string
 */
export const getOrderNote = (order: any): string => {
  return order?.order?.note || order?.note || "";
};

/**
 * Format order ID for display
 * @param orderId - Order ID
 * @returns Formatted order ID
 */
export const formatOrderId = (orderId: string): string => {
  if (!orderId) return "";
  
  // If it's a temporary order ID, format it nicely
  if (orderId.startsWith("order-")) {
    return orderId.replace("order-", "Đơn hàng #");
  }
  
  return `#${orderId}`;
};

/**
 * Get order creation date
 * @param order - Order object
 * @returns Creation date or null
 */
export const getOrderCreatedAt = (order: any): string | null => {
  return order?.createdAt || order?.order?.createdAt || null;
};

/**
 * Get order updated date
 * @param order - Order object
 * @returns Updated date or null
 */
export const getOrderUpdatedAt = (order: any): string | null => {
  return order?.updatedAt || order?.order?.updatedAt || null;
};
