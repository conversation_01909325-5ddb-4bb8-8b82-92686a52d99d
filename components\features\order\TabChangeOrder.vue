<template>
  <div class="relative flex bg-gray-200 rounded-full p-[3px] text-sm w-max">
    <div
      class="absolute top-0 left-0 w-[50%] h-full bg-primary rounded-full transition-all duration-300"
      :style="{
        transform: `translateX(${
          selectedTab === tabs[tabs?.length - 1].value ? '100%' : '0'
        })`,
      }"
    ></div>

    <!-- Tabs -->
    <button
      v-for="(tab, index) in tabs"
      :key="tab.value"
      @click="handleClicktab(tab.value)"
      class="relative flex items-center px-4 py-[2px] rounded-full transition-all duration-300 z-10"
      :class="selectedTab === tab.value ? 'text-white' : 'text-gray-600'"
    >
      {{ tab.label }}
    </button>
  </div>
</template>

<script setup>
const props = defineProps(["tabs"]);
const selectedTab = ref(props.tabs[0].value);
const emits = defineEmits(["toogleTab"]);

const handleClicktab = (value) => {
  selectedTab.value = value;
  emits("toogleTab", value);
};
</script>
