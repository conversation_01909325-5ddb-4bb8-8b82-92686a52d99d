<template>
  <div class="bg-white md:py-2 md:rounded-xl md:px-[120px]">
    <div
      v-for="paymentMethod in dataPaymentMethod"
      :key="paymentMethod.id"
      class="mx-4 py-2"
    >
      <!-- pttt -->
      <div
        @click="handleChossePaymentMethod(paymentMethod)"
        class="p-3 flex gap-2 items-center cursor-pointer rounded bg-secondary-light"
      >
        <img
          class="w-8 h-8 object-contain mr-2 md:w-6 md:h-6 rounded"
          :src="paymentMethod.image || 'https://placehold.co/20'"
          alt=""
          loading="lazy"
        />
        <span class="font-semibold text-base">{{ paymentMethod.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["dataPaymentMethod"]);
const emits = defineEmits(["choosePaymentMethod"]);

// Trạng thái để lưu phương thức thanh toán được chọn
const selectedPaymentMethod = ref();

const handleChossePaymentMethod = (paymentMethod) => {
  selectedPaymentMethod.value = paymentMethod; // Lưu phương thức được chọn
  emits("choosePaymentMethod", paymentMethod);
};
</script>
