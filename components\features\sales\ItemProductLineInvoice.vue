<template>
  <div class="text-sm flex items-center gap-2 w-full my-1 relative border-b">
    <NuxtImg
      :src="image || 'https://placehold.co/20'"
      alt="Product image"
      class="object-contain w-10 h-10 mt-1 rounded-lg"
      width="40"
      height="40"
      placeholder="Product image"
      loading="lazy"
      preload
    />
    <div class="w-full">
      <div class="text-sm max-w-[300px] truncate">
        {{ product?.productName }}
      </div>

      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-2">
          <div>
            <span class="font-semibold">Id:</span>
            <span>{{ product?.productId }}</span>
          </div>
          <div>
            <span class="font-semibold">SKU:</span>
            <span>{{ product?.sku }}</span>
          </div>
        </div>
        <div class="flex items-center gap-1">
          <div>
            {{ product?.quantity }} <span class="text-red-400 w-auto"> x </span>
            {{ formatCurrency(product?.amount) }}
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between">
        <div>
          <span class="font-semibold">VAT:</span>
          <span>{{ ` ${product?.vatRate || 0}%` }}</span>
        </div>
        <div>
          <span class="font-semibold">Đơn vị: </span>
          <span>{{ product?.unitType || "" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["product"]);
const productStore = useProductStore();
const image = ref("");
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  const url = getImageProducrUrl(props.product?.productId, "PRODUCT");
  image.value = url;
});
</script>
