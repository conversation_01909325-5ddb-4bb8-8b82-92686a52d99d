// middleware/auth.ts
import type { User } from "~/stores/auth";
import type { Roles } from "~/types";
interface AuthData {
  token: string;
  user: User;
}
export default defineNuxtRouteMiddleware(async (to, _from) => {
  const { isFeatureAccessible } = usePermission();
  const auth = useCookie<AuthData>("auth").value;

  const rolesPath = to.meta.permission as Roles;
  const isAccess = isFeatureAccessible(auth?.user.roles || [], rolesPath);
  if (!isAccess && rolesPath) {
    return navigateTo("/access-denied");
  }
});
