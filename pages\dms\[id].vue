<template>
  <div class="bg-white h-full overflow-y-auto">
    <div class="mx-3 flex flex-col gap-2 mb-2 h-full overflow-y-auto">
      <div class="flex flex-col">
        <span class="pb-2 text-sm font-bold mt-5">
          Tên cửa hàng <span class="text-red-500 text-xs">*</span>
        </span>
        <input
          class="w-full py-[10px] border pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
          type="text"
          placeholder="Tên cửa hàng"
          v-model="nameStore"
          @blur="handleBlur('Tên cửa hàng')"
        />
      </div>
      <div class="flex flex-col">
        <span class="pb-2 text-sm font-bold">
          Số điện thoại <span class="text-red-500 text-xs">*</span>
        </span>
        <input
          class="w-full py-[10px] border pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
          type="text"
          placeholder="Số điện thoại"
          v-model="phone"
          @blur="handleBlur('Số điện thoại')"
        />
      </div>
      <div class="flex flex-col">
        <span class="pb-2 text-sm font-bold">
          Vị trí Checkin <span class="text-red-500 text-xs">*</span>
        </span>
        <input
          class="w-full py-[10px] border pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
          type="text"
          placeholder="Vị trí Checkin"
          v-model="location"
          @blur="handleBlur('Vị trí Checkin')"
        />
      </div>
      <div class="flex flex-col">
        <span class="pb-2 text-sm font-bold">Mô tả</span>
        <textarea
          rows="8"
          v-model="description"
          class="py-1 px-2 w-full h-[100px] text-base rounded outline-none border bg-secondary"
          placeholder="Mô tả"
          @blur="handleBlur('Mô tả')"
        ></textarea>
      </div>
      <div class="w-full">
        <ProgressLine :dataProcessPineLine="dataProcessPineLine"></ProgressLine>
      </div>

      <div>
        <span class="text-base font-semibold text-primary"
          >Ảnh checkin điểm bán</span
        >
        <div v-if="checkinStore.image">
          <Swiper :options="{ loop: true }">
            <SwiperSlide v-for="i in checkinStore.image">
              <img
                loading="lazy"
                class="object-contain min-h-[200px] max-h-[550px] z-1 w-screen h-[300px]"
                :src="`https://s3-img-gw.longvan.net/img/omnichannel/${i?.srcPath}`"
                alt="Image"
              />
            </SwiperSlide>
          </Swiper>
        </div>
      </div>
      <!-- <Map></Map> -->
      <div
        @click="handleOpenPopUpCamera"
        class="mb-5 border rounded-md bg-primary text-white p-2 flex items-center justify-center mx-8"
      >
        Chụp ảnh mới
      </div>
    </div>
    <ModalCamera class="z-100" v-if="isOpen" @isClose="isClose"></ModalCamera>
  </div>
</template>

<script setup lang="ts">
import type { Auth } from '~/types/Auth';

const { UpdateWorkEffortDescriptions } = useCheckin();
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission"],
  name: "Chi tiết",
});

useHead({
  title: "Chi tiết",
  meta: [
    {
      name: "description",
      content: "Chi tiết",
    },
  ],
});

const { getOpportunityById, UpdateWorkEffortName, getListWorkEfforts } =
  useCheckin();
const route = useRoute();
const id = route.params.id as string;
const auth = useCookie("auth").value as unknown as Auth;
const nameStore = ref<string>("");
const phone = ref<string>();
const description = ref<string>("");
const location = ref<any>();
const ownerId = ref<string>();
const isOpen = ref<boolean>(false);
const image = ref<any>();
const dataProcessPineLine = ref<any>();
const checkinStore = useCheckinStore();
const handleBlur = async (fieldName: string) => {
  if (fieldName === "Vị trí Checkin") {
    await UpdateWorkEffortDescriptions(
      auth.user.id,
      checkinStore.dataDetail.subTasks[1].id,
      location.value
    );
    await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);
  }
  if (fieldName === "Mô tả") {
    await UpdateWorkEffortDescriptions(
      auth?.user.id,
      checkinStore.dataDetail?.id,
      description.value
    );
    await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);
  }
  if (fieldName === "Tên cửa hàng") {
    await UpdateWorkEffortName(
      auth?.user.id,
      checkinStore.dataDetail?.id,
      nameStore.value
    );
    await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);
  }
  if (fieldName === "Số điện thoại") {
  }
};

const handleOpenPopUpCamera = () => {
  isOpen.value = true;
};
onMounted(async () => {
  await checkinStore.getCheckinById(id);
  nameStore.value = checkinStore.dataDetail.name || "";
  description.value = checkinStore.dataDetail.description || "";
  ownerId.value = checkinStore.dataDetail.ownerId || "";
  dataProcessPineLine.value = checkinStore.dataDetail;
  const resPerSon = checkinStore.dataDetail.person;
  phone.value = checkinStore.dataDetail?.owner?.phone;
  location.value = checkinStore.location;
});

const isClose = async (value: boolean) => {
  isOpen.value = value;
};
</script>
