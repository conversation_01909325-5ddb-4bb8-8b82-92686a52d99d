<template>
  <div v-if="validVouchers.length > 0" class="shadow-sm py-2">
    <div class="font-semibold text-base mx-4 pt-3">
      {{ campaign?.campaignActionName }}
    </div>
    <div class="md:col-span-2 md:grid md:grid-cols-2">
      <div v-for="voucher in validVouchers" :key="voucher.id">
        <ItemVoucher
          :voucher="voucher"
          :order="orderStore.orderDetail"
          :customer="customer"
          :campaignAction="campaign"
          @updateScope="updateScope"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["campaign", "dataVoucher"]);
const emit = defineEmits(["get-campaign"]);
const orderStore = useOrderStore();
const customer = computed(() => orderStore.customerInOrder);
const validVouchers = computed(() =>
  props.dataVoucher.filter(
    (voucher: any) =>
      voucher.campaignId === props.campaign?.campaignId &&
      voucher.campaignActionId === props.campaign?.campaignActionId
  )
);
const updateScope = () => {
  emit("get-campaign");
};
</script>
