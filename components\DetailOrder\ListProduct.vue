<template>
  <div class="md:col-span-9 col-span-6 rounded-lg border p-2">
    <div class="text-lg font-semibold text-primary cursor-pointer">
      <PERSON><PERSON> s<PERSON>ch sản phẩm
    </div>
    <div class="mt-2 overflow-auto">
      <div v-for="item in orderDetail">
        <ItemProduct :product="item"></ItemProduct>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const orderDetail = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);
</script>
