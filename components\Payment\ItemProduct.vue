<template>
  <div
    class="bg-card relative text-card-foreground px-2 rounded-lg border flex items-center overflow-hidden mb-2 md:p-2 py-2"
  >
    <label for="product1" class="cursor-pointer flex items-center w-full ml-2">
      <img
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="flex-shrink-0 w-8 h-8 rounded-lg object-contain"
        loading="lazy"
      />
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-sm line-clamp-2 font-semibold">
            {{ product?.orderLineItem?.orderItemName }}
          </h3>
        </div>
        <div class="flex items-center justify-between mt-1">
          <p class="text-primary font-semibold text-sm">
            {{
              formatCurrency(product?.orderLineItem?.originalTotalPrice?.amount)
            }}
          </p>
          <div class="flex items-center gap-1">
            <span class="text-sm"> Số lượng: </span>
            <span class="font-semibold text-sm">
              {{ product?.orderLineItem?.quantity }}</span
            >
          </div>
        </div>
      </div>
    </label>
  </div>
</template>
<script setup lang="ts">
const { product } = defineProps(["product"]);
const image = ref<string>();
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  const url = getImageProducrUrl(
    product?.orderLineItem?.variant?.id,
    "PRODUCT"
  );
  image.value = url;
});
</script>
