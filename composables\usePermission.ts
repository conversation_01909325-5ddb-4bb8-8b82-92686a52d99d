import type { Roles } from "@/types";

export default function usePermission() {
  const allowedRoles = [
    "ORG_ADMIN",
    "SALE",
    "SALE_ADMIN",
    "REPORT",
    "CUSTOMER_ADMIN",
    "ADMIN_CUSTOMER",
    "SALES",
    "SALE_OP",
    "SUPP_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
  ];

  const hasPermission = (roleUser: Roles) => {
    // kiểm tra xem allowedRoles có phải là mảng không
    if (!allowedRoles || !Array.isArray(allowedRoles)) {
      console.error("allowedRoles is not a valid array.");
      return false;
    }
    // kiểm tra xem roleUser có phải là mảng không
    if (!roleUser) {
      console.error("roleUser is not a valid array.");
      return false;
    }
    // kiểm tra xem roleUser có trong allowedRoles không
    const hasPermission = allowedRoles.some((role: string) =>
      roleUser.includes(role)
    ); // kiểm tra xem roleUser có trong allowedRoles không
    return hasPermission; // trả về kết quả true hoặc false
  };
  const setStore = async (storeId: string) => {
    // Use tab-isolated context instead of cookies
    const { setStoreId } = useTabContext();
    await setStoreId(storeId);

    // Keep cookie for SSR compatibility
    const cookie = useCookie("storeId") as Ref<string>;
    cookie.value = storeId;
  };

  const setOrgId = async (orgId: string) => {
    // Use tab-isolated context instead of cookies
    const { setOrgId: setTabOrgId } = useTabContext();
    await setTabOrgId(orgId);

    // Keep cookie for SSR compatibility
    const cookie = useCookie("orgId") as Ref<string>;
    cookie.value = orgId;
  };
  const isFeatureAccessible = (
    userRoles: Roles,
    featureRoles: Roles
  ): boolean => {
    const userRolesSet = new Set(userRoles);
    const featureRolesSet = new Set(featureRoles);

    // Kiểm tra xem có bất kỳ vai trò nào của người dùng trùng với bất kỳ vai trò nào của tính năng không
    for (const userRole of userRolesSet) {
      if (featureRolesSet.has(userRole)) {
        return true; // Có sự trùng khớp
      }
    }

    return false; // Không có sự trùng khớp
  };
  return {
    hasPermission,
    isFeatureAccessible,
    setStore,
    setOrgId,
  };
}
