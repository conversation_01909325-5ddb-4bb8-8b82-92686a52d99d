import Vue3Toastify, { toast } from "vue3-toastify";
import "vue3-toastify/dist/index.css";

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(Vue3Toastify, {
    autoClose: 1000, // lâu hơn chút để dễ nhìn
    theme: "colored", // dùng màu để nổi bật (light/dark/colored)
    closeOnClick: true,
    pauseOnHover: true,
    hideProgressBar: false,
    draggable: true,
    limit: 3, // tránh spam toast
    toastStyle: {
      fontSize: "15px",
      fontWeight: "semibold",
      padding: "12px 18px",
      borderRadius: "8px",
    },
  });

  return {
    provide: { toast },
  };
});
