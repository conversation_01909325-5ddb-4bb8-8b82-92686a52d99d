<template>
  <div class="mx-2 border-b pb-1">
    <div class="flex items-center justify-between">
      <div class=""><PERSON><PERSON>y tạo đơn:</div>
      <div class="font-semibold">
        {{ formatTimestampV2(orderDetail?.order?.createdStamp) }}
      </div>
    </div>
    <div class="flex items-center justify-between">
      <div class="">Nhân viên tạo đơn:</div>
      <div class="font-semibold">
        {{ employee?.name }}
      </div>
    </div>
    <div class="">
      <div class="font-semibold my-1"><PERSON><PERSON> chú</div>
      <textarea
        v-if="orderDetail?.order"
        rows="4"
        id="note"
        v-model="orderDetail.order.note"
        class="py-1 px-2 w-full text-base rounded outline-none border bg-secondary"
        placeholder="Chiều cao, cân nặng, độ tuổi, gi<PERSON><PERSON> t<PERSON>, ...."
      ></textarea>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["orderDetail", "employee"]);
</script>
