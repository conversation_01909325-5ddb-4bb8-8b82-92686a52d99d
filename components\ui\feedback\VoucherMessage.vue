<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-4xl w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">Thông tin mã giảm giá</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 md:size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[70vh] overflow-y-auto">
        <div v-for="campaign in campaigns">
          <ListCampaign
            :campaign="campaign"
            :dataVoucher="dataVoucher"
            @get-campaign="handleSuggestVoucher"
          ></ListCampaign>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const { searchVoucher, getVoucherAvailableForCustomer, suggestVoucher } =
  useCampaign();
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const orderStore = useOrderStore();
const campaigns = computed(() =>
  orderStore?.campaign?.filter((campaign: any) =>
    [
      "PROMOTION_VOUCHER",
      "PROMOTION_BIRTH_DAY",
      "PROMOTION_POINT_TO_VOUCHER",
    ].includes(campaign.type)
  )
);

const dataVoucher = ref([]);
const customer = computed(() => orderStore.customerInOrder);

const handleSuggestVoucher = async () => {
  try {
    const response = await suggestVoucher(customer.value?.id);
    dataVoucher.value = response?.content;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleSuggestVoucher();
});
</script>

<style scoped></style>
