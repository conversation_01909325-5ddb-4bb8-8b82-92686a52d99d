<template>
  <div
    class="rounded-lg border border-gray-200 bg-white shadow-sm overflow-hidden"
  >
    <div class="w-full">
      <!-- Header -->
      <div
        class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 cursor-pointer hover:from-blue-100 hover:to-indigo-100 transition-colors duration-200"
        @click="toggleVisible"
        role="button"
        :aria-expanded="isVisible"
        aria-controls="payment-history-content"
        tabindex="0"
        @keydown.enter="toggleVisible"
        @keydown.space.prevent="toggleVisible"
      >
        <div class="flex items-center gap-3">
          <div
            class="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4 text-primary"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z"
              />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 text-sm">
              Lịch sử thanh toán
            </h3>
            <p
              v-if="!isVisible && listPayment.length > 0"
              class="text-xs text-gray-500"
            >
              {{ listPayment.length }} giao dịch
            </p>
          </div>
        </div>

        <div class="flex items-center gap-2">
          <!-- Status indicator -->
          <div v-if="listPayment.length > 0" class="flex items-center gap-1">
            <div
              v-if="hasSuccessPayments"
              class="w-2 h-2 bg-green-500 rounded-full"
              title="Có thanh toán thành công"
            ></div>
            <div
              v-if="hasPendingPayments"
              class="w-2 h-2 bg-yellow-500 rounded-full"
              title="Có thanh toán đang chờ"
            ></div>
            <div
              v-if="hasFailedPayments"
              class="w-2 h-2 bg-red-500 rounded-full"
              title="Có thanh toán thất bại"
            ></div>
          </div>

          <!-- Expand/Collapse Icon -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-5 h-5 text-gray-600 transition-transform duration-300 ease-in-out"
            :class="{ 'rotate-180': isVisible }"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            />
          </svg>
        </div>
      </div>
      <ModalConfirmPayment
        v-if="isModalOpen"
        :isEmployee="false"
        :paymentId="paymentId"
        :isOpen="isModalOpen"
        @update:isOpen="isModalOpen = $event"
        @confirm="handleConfirm"
      />

      <!-- Content -->
      <transition
        name="slide-down"
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 transform -translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform -translate-y-2"
      >
        <div
          v-show="isVisible"
          id="payment-history-content"
          class="max-h-[60vh] md:max-h-[40vh] overflow-hidden"
        >
          <!-- Loading State -->
          <div v-if="loading" class="p-6">
            <div class="flex items-center justify-center space-x-2">
              <div
                class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"
              ></div>
              <span class="text-sm text-gray-600"
                >Đang tải lịch sử thanh toán...</span
              >
            </div>
            <!-- Loading Skeleton -->
            <div class="mt-4 space-y-3">
              <div v-for="i in 3" :key="i" class="animate-pulse">
                <div
                  class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg"
                >
                  <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/6"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="hasError" class="p-6 text-center">
            <div class="flex flex-col items-center space-y-3">
              <div
                class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-6 h-6 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">
                  Không thể tải dữ liệu
                </h4>
                <p class="text-xs text-gray-500 mt-1">
                  Đã xảy ra lỗi khi tải lịch sử thanh toán
                </p>
              </div>
              <button
                @click="retryLoadPayments"
                class="px-3 py-1.5 text-xs bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
              >
                Thử lại
              </button>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else-if="listPayment?.length === 0" class="p-6 text-center">
            <div class="flex flex-col items-center space-y-3">
              <div
                class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">
                  Chưa có giao dịch
                </h4>
                <p class="text-xs text-gray-500 mt-1">
                  Đơn hàng này chưa có lịch sử thanh toán nào
                </p>
              </div>
            </div>
          </div>
          <!-- Payment List -->
          <div v-else class="w-full">
            <!-- Desktop Table -->
            <div class="hidden md:block overflow-auto">
              <table class="w-full border-collapse">
                <thead
                  class="sticky top-0 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200"
                >
                  <tr
                    class="text-xs font-semibold text-gray-700 uppercase tracking-wider"
                  >
                    <th class="px-4 py-3 text-left">Thời gian</th>
                    <th class="px-4 py-3 text-left">Phương thức</th>
                    <th class="px-4 py-3 text-center">Trạng thái</th>
                    <th class="px-4 py-3 text-left">Mã giao dịch</th>
                    <th class="px-4 py-3 text-right">Số tiền</th>
                    <th class="px-4 py-3 text-left">Ghi chú</th>
                    <th class="px-4 py-3 text-center">Thao tác</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-100">
                  <tr
                    v-for="payment in listPayment"
                    :key="payment.paymentId"
                    class="hover:bg-gray-50 transition-colors duration-150"
                  >
                    <td class="px-4 py-3 text-sm text-gray-900">
                      <div class="font-medium">
                        {{ formatTimestampV3(payment?.payDate) }}
                      </div>
                    </td>
                    <td class="px-4 py-3 text-sm">
                      <div class="flex items-center">
                        <div
                          class="w-2 h-2 bg-blue-500 rounded-full mr-2"
                        ></div>
                        <span class="text-gray-900">{{
                          payment?.methodDescription
                        }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 text-center">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="statusBadgeClass(payment.statusCode)"
                      >
                        {{ statusText(payment.statusCode) }}
                      </span>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900 font-mono">
                      {{ payment?.transactionId || "-" }}
                    </td>
                    <td
                      class="px-4 py-3 text-sm text-right font-semibold text-gray-900"
                    >
                      {{ formatCurrency(payment?.totalAmount) }}
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-600">
                      <div
                        class="max-w-32 truncate"
                        :title="payment?.paymentNote"
                      >
                        {{ payment?.paymentNote || "-" }}
                      </div>
                    </td>
                    <td class="px-4 py-3 text-center">
                      <button
                        v-if="payment?.statusCode === '1'"
                        @click="toogleConfirmPaymentPopup(payment)"
                        class="inline-flex items-center px-3 py-1 text-xs font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md transition-colors duration-200"
                      >
                        Xác nhận
                      </button>
                      <span v-else class="text-gray-400 text-xs">-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile Cards -->
            <div class="md:hidden p-4 space-y-4 overflow-auto max-h-[50vh]">
              <div
                v-for="payment in listPayment"
                :key="payment.paymentId"
                class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <!-- Header -->
                <div class="flex items-start justify-between mb-3">
                  <div class="flex-1">
                    <div class="text-sm font-medium text-gray-900">
                      {{ formatTimestampV3(payment?.payDate) }}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      {{ payment?.methodDescription }}
                    </div>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ml-2"
                    :class="statusBadgeClass(payment.statusCode)"
                  >
                    {{ statusText(payment.statusCode) }}
                  </span>
                </div>

                <!-- Details -->
                <div class="space-y-2">
                  <div
                    v-if="payment?.transactionId"
                    class="flex justify-between text-sm"
                  >
                    <span class="text-gray-600">Mã giao dịch:</span>
                    <span class="font-mono text-gray-900">{{
                      payment.transactionId
                    }}</span>
                  </div>

                  <div class="flex justify-between text-sm">
                    <span class="text-gray-600">Số tiền:</span>
                    <span class="font-semibold text-gray-900">
                      {{ formatCurrency(payment?.totalAmount) }}
                    </span>
                  </div>

                  <div v-if="payment?.paymentNote" class="text-sm">
                    <span class="text-gray-600">Ghi chú:</span>
                    <p class="text-gray-900 mt-1">{{ payment.paymentNote }}</p>
                  </div>
                </div>

                <!-- Action -->
                <div
                  v-if="payment?.statusCode === '1'"
                  class="mt-4 pt-3 border-t border-gray-100"
                >
                  <button
                    @click="toogleConfirmPaymentPopup(payment)"
                    class="w-full inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md transition-colors duration-200"
                  >
                    <svg
                      class="w-4 h-4 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Xác nhận thanh toán
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
// Async components
const ModalConfirmPayment = defineAsyncComponent(
  () => import("~/components/Modal/ModalConfirmPayment.vue")
);

// Props
const props = defineProps(["orderDetail"]);

// Reactive state
const isVisible = ref(false);
const listPayment = ref<any[]>([]);
const loading = ref(false);
const hasError = ref(false);
const isModalOpen = ref(false);
const paymentId = ref<any>("");

// Composables
const { paymentsByOrders, confirmPayment } = usePayment();
const orderStore = useOrderStore();

// Computed properties
const hasSuccessPayments = computed(() =>
  listPayment.value.some((p) => p.statusCode === "0")
);

const hasPendingPayments = computed(() =>
  listPayment.value.some((p) => p.statusCode === "1")
);

const hasFailedPayments = computed(() =>
  listPayment.value.some((p) => p.statusCode === "-1")
);
const handleConfirm = async (data: any) => {
  isModalOpen.value = false;
  await confirmPayment(
    paymentId.value?.paymentId,
    data.transactionCode,
    data?.note,
    data.employee
  );
  handleChangeStatus(paymentId.value?.paymentId, data?.note);
  await orderStore.updateOrder(props.orderDetail?.id);
};
const handleChangeStatus = (paymentId: string, note: string) => {
  listPayment.value = listPayment.value.map((p) =>
    p.paymentId === paymentId ? { ...p, statusCode: "0", paymentNote: note } : p
  );
};

// Helper functions
const statusBadgeClass = (statusCode: string) => {
  return {
    "bg-green-100 text-green-800": statusCode === "0",
    "bg-red-100 text-red-800": statusCode === "-1",
    "bg-yellow-100 text-yellow-800": statusCode === "1",
  };
};

const statusText = (statusCode: string) => {
  return statusCode === "0"
    ? "Thành công"
    : statusCode === "-1"
    ? "Thất bại"
    : "Đang chờ";
};

const toogleConfirmPaymentPopup = (paymentValue: any) => {
  paymentId.value = paymentValue;
  isModalOpen.value = !isModalOpen.value;
};

const retryLoadPayments = async () => {
  hasError.value = false;
  await getPayment();
};
const getPayment = async () => {
  if (!props.orderDetail?.id) return;

  try {
    loading.value = true;
    hasError.value = false;

    const response = await paymentsByOrders([props.orderDetail.id]);
    listPayment.value = [...(response || [])].sort(
      (a, b) =>
        new Date(b.transactionDate).getTime() -
        new Date(a.transactionDate).getTime()
    );
  } catch (error) {
    console.error("Error loading payment history:", error);
    hasError.value = true;
    listPayment.value = [];
  } finally {
    loading.value = false;
  }
};
const toggleVisible = async () => {
  isVisible.value = !isVisible.value;
  if (isVisible.value) {
    await getPayment();
  }
};
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Pulse animation for skeleton loading */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

[role="button"]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* CSS containment for performance */
.bg-white {
  contain: layout style;
}

/* Hover effects */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .space-y-4 > * + * {
    margin-top: 1rem;
  }

  .space-y-2 > * + * {
    margin-top: 0.5rem;
  }
}

/* Gradient backgrounds */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff;
  --tw-gradient-stops: var(--tw-gradient-from),
    var(--tw-gradient-to, rgba(239, 246, 255, 0));
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff;
}

.hover\:from-blue-100:hover {
  --tw-gradient-from: #dbeafe;
  --tw-gradient-stops: var(--tw-gradient-from),
    var(--tw-gradient-to, rgba(219, 234, 254, 0));
}

.hover\:to-indigo-100:hover {
  --tw-gradient-to: #e0e7ff;
}
</style>
