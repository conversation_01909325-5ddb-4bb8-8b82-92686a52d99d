<template>
  <div
    class="border cursor-pointer bg-white rounded hover:animate-slideUp flex flex-col w-[85px] md:w-[118px] overflow-hidden"
    data-testid="product-card-vertical"
    @click="addProduct(product)"
  >
    <div class="relative overflow-hidden">
      <NuxtImg
        data-testid="image-slot"
        :alt="product.name"
        :src="handleGetImageProductUrl()"
        class="object-contain w-full h-[40px] md:h-[86px]"
        loading="lazy"
        preload
      />
    </div>
    <div
      class="p-2 md:p-2 border-t border-neutral-200 h-full flex flex-col text-neutral-900"
    >
      <div
        class="focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm hover:text-primary-800 active:text-primary-900 text-sm text-inherit no-underline hover:!text-primary-800 active:text-primary-900 visited:!text-inherit line-clamp-2 h-10"
        data-testid="link"
      >
        {{ product.title }}
      </div>
      <span class="block font-bold text-sm text-primary mt-2 md:mt-0">
        {{ formatCurrency(product.price) }}
      </span>
    </div>
  </div>
  <ModalProductDetail
    v-if="isModalOpen"
    :isOpen="isModalOpen"
    :productId="product.id"
    @close="closeModal"
  />
</template>

<script setup>
// Lazy load heavy components
const ModalProductDetail = defineAsyncComponent(() =>
  import("~/components/ui/feedback/ModalProductDetail.vue")
);
const { addProductToOrder, handleCheckInventory } = useOrderStore();
const isModalOpen = ref(false);
// const { product } = defineProps({
//   product: {
//     type: Object,
//     required: true,
//   },
// });
//
const props = defineProps(["product"]);
const isLoading = ref(false);
const addProduct = async () => {
  if (props.product.subType !== "VARIABLE") {
    // thêm vào sản phẩm như bình thường
    isLoading.value = true;
    await addProductToOrder(props.product);
    isLoading.value = false;
    return;
  }
  isModalOpen.value = true;
};
const { getImageProducrUrl } = usePortal();
const handleGetImageProductUrl = () => {
  const url = getImageProducrUrl(props.product.id, "PRODUCT");
  return url;
};
const closeModal = () => {
  isModalOpen.value = false;
};
</script>
