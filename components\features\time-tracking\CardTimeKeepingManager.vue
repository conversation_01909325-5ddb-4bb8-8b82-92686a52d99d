<template>
  <div class="space-y-3">
    <div v-if="!dataCheckin?.length" class="text-center py-12">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
        />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Ch<PERSON>a có dữ liệu chấm công
      </h3>
      <p class="text-gray-500">Nh<PERSON> viên chưa thực hiện chấm công nào</p>
    </div>

    <div v-for="(item, index) in dataCheckin" :key="index">
      <NuxtLink
        :to="`/timekeeping/${item.id}?storeId=${storeId}`"
        @click="handleChooseTimeKeeping(item)"
        class="block"
      >
        <div
          class="bg-white rounded-lg p-4 border border-gray-200 hover:border-primary hover:shadow-md transition-all duration-200"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-primary"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <div class="font-semibold text-gray-900">
                  {{ item?.owner?.name || "Nhân viên" }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ formatDate(item.createdStamp) }} •
                  {{ formatTime(item.createdStamp) }}
                </div>
              </div>
            </div>

            <div :class="getActionClass(item.workEffortTypeId)">
              {{ formatAction(item.workEffortTypeId) }}
            </div>
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
  <ModalEditCheckin v-if="isEditCheckin" @isClose="isClose" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";
const storeId = useCookie("storeId").value;
const { dataCheckin } = defineProps(["dataCheckin"]);
const isEditCheckin = ref(false);
const timeKeepingStore = useTimeKeepingStore();
const isClose = (value: boolean) => {
  isEditCheckin.value = value;
};

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "dd/MM/yyyy", { locale: vi });
};

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm", { locale: vi });
};

const formatAction = (value: string) => {
  if (value === "CHECK_IN") {
    return "Vào ca";
  }
  if (value === "CHECK_OUT") {
    return "Hết ca";
  }
  return value;
};

const getActionClass = (value: string) => {
  const baseClass =
    "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
  if (value === "CHECK_IN") {
    return `${baseClass} bg-green-100 text-green-800`;
  }
  if (value === "CHECK_OUT") {
    return `${baseClass} bg-red-100 text-red-800`;
  }
  return `${baseClass} bg-gray-100 text-gray-800`;
};

const handleChooseTimeKeeping = (item: any) => {
  timeKeepingStore.setTimeKeeping(item);
};
</script>
