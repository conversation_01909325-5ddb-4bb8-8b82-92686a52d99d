<template>
  <div class="px-2 bg-white rounded flex flex-col gap-1">
    <div class="flex items-center gap-1">
      <span class="font-bold text-primary">Thông tin thanh toán</span>
    </div>
    <div></div>
    <div>
      <!-- <div class="flex items-center justify-between">
        <div>Chiế<PERSON> khấu</div>

        <div
          class="flex items-center bg-secondary rounded px-2 py-1 max-w-[130px]"
        >
          <select class="focus:outline-none bg-secondary">
            <option value="MONEY">₫</option>
            <option value="PERCENT">%</option>
          </select>
          <input
            type="text"
            class="w-full focus:outline-none text-right bg-secondary"
            v-model="''"
          />
        </div>
      </div> -->
      <!-- hạng thành viên -->
      <!-- <div>Chiết khấu</div>
      <div
        v-for="voucher in order?.order?.discountApplications"
        class="flex items-center justify-between"
      >
        <div class="flex items-center gap-1">
          <div class="text-red-500 cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </div>
          <div>{{ voucher.voucherCode || voucher.title }}</div>
        </div>
        <div class="text-primary">
          {{
            ` - ${formatCurrency(voucher.value.amount)} ${
              voucher.percent ? `(${voucher.percent})%` : " "
            }`
          }}
        </div>
      </div> -->
    </div>

    <!--  -->
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Tổng số tiền</div>
      <div class="text-primary">
        {{ formatCurrency(order?.order?.currentSubtotalPrice?.amount) }}
      </div>
    </div>
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Giảm giá</div>
      <div class="text-primary">
        {{
          formatCurrency(
            order?.order?.currentSubtotalPrice?.amount -
              order?.order?.discountTotalPrice?.amount || 0
          )
        }}
      </div>
    </div>
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Sau giảm</div>
      <div class="text-primary">
        {{ formatCurrency(order?.order?.discountTotalPrice?.amount || 0) }}
      </div>
    </div>
    <div class="flex items-center justify-between">
      <div class="text-primary">Phí vận chuyển</div>
      <div class="text-primary">
        {{ formatCurrency(order?.order?.totalShippingPrice?.amount) }}
      </div>
    </div>
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Đã thanh toán</div>
      <div class="text-primary">
        {{ formatCurrency(order?.totalAlreadyPaid) }}
      </div>
    </div>
    <!--  -->
    <div class="border-b mx-2 pt-1"></div>

    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Phải thu</div>
      <div class="font-bold text-red-500">
        {{ formatCurrency(order?.remainTotal) }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
/////
const props = defineProps(["order"]);
</script>
