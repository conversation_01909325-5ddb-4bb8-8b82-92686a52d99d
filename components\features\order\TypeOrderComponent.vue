<template>
  <div>
    <div class="w-full bg-white rounded p-2 flex flex-col text-sm">
      <div class="flex items-center justify-between">
        <div class="font-semibold text-primary"><PERSON><PERSON><PERSON> chỉ nhận hàng</div>
        <div v-if="orderDetail?.order?.shippingAddress" class="cursor-pointer">
          <svg
            @click="handleToogleTransport('at-counter')"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
            v-tippy="'Xóa địa chỉ giao hàng'"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
          </svg>
        </div>
        <div class="cursor-pointer" v-else>
          <svg
            v-if="!isShowShippingAddress"
            @click="handleToogleTransport('delivery')"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
            v-tippy="'Thêm địa chỉ giao hàng'"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
          <svg
            v-else
            @click="handleToogleTransport('at-counter')"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
            v-tippy="'Xóa địa chỉ giao hàng'"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14" />
          </svg>
        </div>
      </div>
    </div>
    <!-- nếu có dvvc -->
    <div v-if="orderDetail?.order?.shippingAddress">
      <div class="w-full bg-white rounded p-2 flex flex-col">
        <div>
          <ShippingAddressOrderDetail
            :shippingAddress="orderDetail?.order?.shippingAddress"
            :customer="customer"
            :isPageSale="true"
            @toogleModal="handleListShippingAddress"
          ></ShippingAddressOrderDetail>
        </div>
      </div>
      <div class="bg-white rounded p-2 flex flex-col gap-2">
        <ShippingService :orderDetail="orderDetail"></ShippingService>
      </div>
    </div>
    <!-- else -->
    <div v-else>
      <div
        v-if="isShowShippingAddress"
        class="w-full bg-white rounded p-2 flex flex-col"
      >
        <div class="flex items-center justify-end mb-4">
          <div class="flex gap-2">
            <div>
              <span
                class="text-primary font-bold cursor-pointer"
                @click="handleCreateShippingAddress"
                v-tippy="'Tạo địa chỉ giao hàng'"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 4.5v15m7.5-7.5h-15"
                  />
                </svg>
              </span>
            </div>
            <div>
              <span
                @click="handleListShippingAddress"
                v-tippy="'Chỉnh sửa địa chỉ giao hàng'"
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-5 text-primary cursor-pointer"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                  />
                </svg>
              </span>
            </div>
          </div>
        </div>
        <div v-if="dataShippingAddress && dataDefaultAddress">
          <ShippingAddressDefault
            :shippingAddress="dataDefaultAddress"
            :customer="customer"
            :isPageSale="true"
          ></ShippingAddressDefault>
        </div>
        <div
          v-else
          class="text-sm text-red-600 flex items-center justify-center"
        >
          Không có địa chỉ giao hàng vui vòng tạo mới!
        </div>
      </div>
      <div
        v-if="isShowShippingAddress"
        class="bg-white rounded p-2 flex flex-col gap-2"
      >
        <ShippingService :orderDetail="orderDetail"></ShippingService>
      </div>
    </div>
    <ModalCreateShippingAddress
      v-if="isShowCreateShippingAddress"
      :customer="customer"
      :isPageSale="true"
      @closeModalCreateShippingAddress="handleCreateShippingAddress"
    ></ModalCreateShippingAddress>
    <ModalManageAddressShip
      v-if="isShowListShippingAddress"
      :customer="customer"
      :isPageSale="true"
      @closeModalManageAddressShip="handleListShippingAddress"
    ></ModalManageAddressShip>
  </div>
</template>
<script setup lang="ts" async>
// Lazy load heavy components
const ShippingAddressDefault = defineAsyncComponent(
  () =>
    import(
      "~/components/features/sales/ShippingAddress/ShippingAddressDefault.vue"
    )
);
const ShippingService = defineAsyncComponent(
  () =>
    import("~/components/features/sales/ShippingAddress/ShippingService.vue")
);
const ModalCreateShippingAddress = defineAsyncComponent(
  () => import("~/components/ui/feedback/ModalCreateShippingAddress.vue")
);
const ModalManageAddressShip = defineAsyncComponent(
  () => import("~/components/ui/feedback/ModalManageAddressShip.vue")
);
import "tippy.js/dist/tippy.css";

const {
  removeShippingInfo,
  removeShippingAddress,
  updateOrderCustomer,
  updateShippingFee,
} = useOrder();
const props = defineProps([
  "customer",
  "orderDetail",
  "dataShippingAddress",
  "dataDefaultAddress",
]);
const isShowShippingAddress = ref<boolean>(false);
import type { Auth } from "~/types/Auth";
const orderStore = useOrderStore();
const handleToogleTransport = async (typeOrder: any) => {
  if (
    props.orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }

  const auth = useCookie("auth").value as unknown as Auth;
  if (props.customer) {
    if (typeOrder === "delivery") {
      orderStore.isOpenShippingAddress = true;
      isShowShippingAddress.value = true;
      if (props.dataDefaultAddress?.id) {
        updateOrderCustomer(
          props?.orderDetail?.id,
          props.customer?.id,
          props.dataDefaultAddress?.id
        );
      }
    } else {
      isShowShippingAddress.value = false;
      orderStore.isOpenShippingAddress = false;
      await Promise.allSettled([
        removeShippingAddress(props.orderDetail?.id, auth?.user?.id),
        removeShippingInfo(props.orderDetail?.id, auth?.user?.id),
        updateShippingFee(props.orderDetail?.id, 0),
      ]);
      await orderStore.updateOrder(orderStore?.orderDetail?.id);
      orderStore.setShippingAddressInOrderDetail();
    }
  } else {
    useNuxtApp().$toast.warning(
      "Vui lòng thêm khách hàng trước khi chọn đỉa chỉ nhận hàng"
    );
  }
};
const isShowCreateShippingAddress = ref(false);
const isShowListShippingAddress = ref(false);
const handleCreateShippingAddress = () => {
  isShowCreateShippingAddress.value = !isShowCreateShippingAddress.value;
};
const handleListShippingAddress = () => {
  isShowListShippingAddress.value = !isShowListShippingAddress.value;
};
</script>
