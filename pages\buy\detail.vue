<template>
  <div>
    <div class="h-full overflow-y-scroll md:overflow-hidden">
      <div class="w-full relative h-full">
        <div class="wrapper lg:my-0 my-0 px-2 md:[px-15] h-full">
          <div class="w-full relative h-full-custom">
            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-2 md:mb-4 md:h-full z-0"
            >
              <div
                class="w-full col-span-2 relative bg-white rounded h-full md:order-1"
              >
                <SearchProduct></SearchProduct>
                <div class="md:block hidden">
                  <TableProduct></TableProduct>
                </div>
                <div class="block md:hidden">
                  <ListItemMobile></ListItemMobile>
                </div>
              </div>
              <div
                class="w-full col-span-2 md:col-span-1 order-1 md:order-2 mb-24"
              >
                <div
                  class="flex flex-col gap-1 md:overflow-y-scroll md:h-screen-110 bg-white"
                >
                  <div class="border-b pb-1 pt-1">
                    <CustomerBuy></CustomerBuy>
                  </div>
                  <div class="pb-1">
                    <InforPaymentBuy></InforPaymentBuy>
                  </div>
                </div>
                <FooterBuy></FooterBuy>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Mua hàng",
});
useHead({
  title: "Mua hàng",
  meta: [
    {
      name: "description",
      content: "Mua hàng",
    },
  ],
});
const route = useRoute();
const orderStore = useOrderStore();
const isLoading = ref(false);
onMounted(async () => {
  orderStore.orderDetail = null;
  if (route.query.orderId) {
    isLoading.value = true;
    await orderStore.getBuyOrderById(route.query.orderId as string);
    isLoading.value = false;
  }
});
watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    orderStore.orderDetail = null;
    if (newVal) {
      isLoading.value = true;
      await orderStore.getBuyOrderById(newVal as string);
      isLoading.value = false;
    }
  }
);
</script>
