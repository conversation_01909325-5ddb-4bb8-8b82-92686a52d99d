<template>
  <h2 class="text-xl text-primary-light font-bold mt-5">{{ title }}</h2>
  <div class="mt-5 bg-slate-50 py-5 rounded flex flex-col">
    <div
      class="mx-5 my-2 flex justify-between"
      v-for="(item, index) in titleRevenue"
    >
      <div class="text-primary-dark">{{ item.title }}</div>
      <div>
        {{
          item?.unit === "Đơn"
            ? `${item.value} ${item.unit}`
            : `${formatCurrency(Math.floor(item.value))}`
        }}
      </div>
    </div>
  </div>
  <div class="mx-5 my-2 flex justify-end gap-5 mt-5 flex-wrap" v-if="isButtons">
    <button
      :class="{
        'bg-primary text-white': selectedButton === 1,
        'text-primary': selectedButton !== 1,
      }"
      class="text-blue-600 px-4 py-1 rounded-md text-nowrap"
      @click="selectButton(1)"
    >
      Xem theo ng<PERSON>y
    </button>
    <button
      v-if="isYear || isMonth"
      :class="{
        'bg-primary text-white': selectedButton === 2,
        'text-primary': selectedButton !== 2,
      }"
      class="text-blue-600 px-4 py-1 rounded-md text-nowrap"
      @click="selectButton(2)"
    >
      Xem theo tháng
    </button>
    <button
      v-if="isYear"
      :class="{
        'bg-primary text-white': selectedButton === 3,
        'text-primary': selectedButton !== 3,
      }"
      class="text-blue-600 px-4 py-1 rounded-md text-nowrap"
      @click="selectButton(3)"
    >
      Xem theo năm
    </button>
  </div>
</template>
<script setup lang="ts">
const { isButtons, dataRevenue, isMonth, isYear, selectedButton } = defineProps(
  ["isButtons", "dataRevenue", "isMonth", "isYear", "selectedButton"]
);
const emit = defineEmits(["update-typeView"]);
const route = useRoute();
const title = ref();
const titleRevenue = ref([{}]);

if (route.params.reportItem === "overview") {
  title.value = "Doanh thu cửa hàng";

  titleRevenue.value = [
    {
      title: "Tổng doanh thu",
      value: dataRevenue?.totalAmount,
      unit: "VND",
    },
    {
      title: "Tổng hoàn",
      value: dataRevenue?.totalAmountRefund,
      unit: "VND",
    },
    {
      title: "Số lượng đơn",
      value: dataRevenue?.totalOrders,
      unit: "Đơn",
    },
  ];
} else if (route.params.reportItem === "employees") {
  title.value = "Báo cáo theo nhân viên";
  titleRevenue.value = [
    {
      title: "Tổng doanh thu",
      value: dataRevenue?.totalAmount,
      unit: "VND",
    },
    {
      title: "Số lượng đơn",
      value: dataRevenue?.totalOrders,
      unit: "Đơn",
    },
  ];
} else {
  title.value = "Doanh thu theo phương thức thanh toán";
  titleRevenue.value = [
    {
      title: "Doanh thu đã thanh toán",
      value: dataRevenue?.totalAmountRevenue,

      unit: "VND",
    },
    {
      title: "Tổng chưa thanh toán",
      value: dataRevenue?.totalRemain,

      unit: "VND",
    },
    {
      title: "Tổng doanh thu",
      value: dataRevenue?.totalAmount,
      unit: "VND",
    },
  ];
}
const selectButton = (index: number) => {
  emit("update-typeView", { id: index });
};
</script>
