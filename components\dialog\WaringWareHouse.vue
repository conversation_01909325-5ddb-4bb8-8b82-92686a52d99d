<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-center gap-2 font-bold text-xl">
        <span class="text-red-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"
            />
          </svg>
        </span>
        <span>{{ title ? title : "Không thể tạo đơn" }}</span>
      </div>
      <div v-if="!isAlertRole">
        <span class="font-semibold">L<PERSON>u ý: </span>
        Đ<PERSON>n hàng có sản phẩm với
        <span class="font-semibold text-red-500">tồn kho dưới 5.</span> Theo quy
        định chỉ
        <span class="font-semibold">Quản lý kinh doanh </span>
        mới được phép tạo đơn và xuất hóa đơn trong trường hợp này. Vui lòng
        liên hệ <span class="font-semibold">Quản lý kinh doanh</span> để được hỗ
        trợ.
      </div>
      <div v-else>
        <span class="font-semibold">Lưu ý: </span>
        Quyền hạn của bạn chưa đủ để thực hiện tính năng này Theo quy định chỉ
        <span class="font-semibold">Quản lý kinh doanh </span>
        mới được phép tạo đơn và xuất hóa đơn trong trường hợp này. Vui lòng
        liên hệ <span class="font-semibold">Quản lý kinh doanh</span> để được hỗ
        trợ.
      </div>
      <div class="flex space-x-4 mt-4">
        <button
          @click="cancel"
          class="px-2 py-1 w-full rounded flex items-center justify-center gap-2 bg-red-500 text-white"
        >
          <span
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 18 18 6M6 6l12 12"
              />
            </svg>
          </span>
          <span>Đã hiểu</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["title", "message", "isHideButton", "isAlertRole"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);

const confirm = () => {
  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
</script>

<style scoped></style>
