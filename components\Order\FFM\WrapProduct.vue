<template>
  <div class="space-y-2 p-2 bg-white rounded text-sm">
    <div class="flex items-center justify-between">
      <div class="text-primary font-bold text-sm"><PERSON><PERSON><PERSON> g<PERSON><PERSON> sản phẩm</div>
      <div
        :class="
          dataFFM?.packageStatus === 'READY_FOR_PACKING'
            ? 'text-yellow-400'
            : 'text-green-400'
        "
      >
        {{ handleStatusPackage(dataFFM?.packageStatus) }}
      </div>
    </div>
    <div class="flex flex-row items-center gap-2">
      <select
        class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer border"
        v-model="selectedPackageBox"
      >
        <option v-for="box in dataPackageBox" :key="box.id" :value="box">
          {{ box.name }}
        </option>
      </select>
    </div>
    <div class="space-y-1">
      <div>Thông tin đóng gói</div>
      <!-- 1 -->
      <div class="flex items-start gap-2">
        <div class="flex-1">
          <label class="mb-1">Dài</label>
          <div
            class="border p-1 rounded bg-secondary flex items-center justify-between px-2"
          >
            <div>{{ selectedPackageBox?.dimension?.length }}</div>
            <div>{{ selectedPackageBox?.dimension?.dimensionUnit }}</div>
          </div>
        </div>
        <div class="flex-1">
          <label class="mb-1">Rộng</label>
          <div
            class="border p-1 rounded bg-secondary flex items-center justify-between px-2"
          >
            <div>{{ selectedPackageBox?.dimension?.width }}</div>
            <div>{{ selectedPackageBox?.dimension?.dimensionUnit }}</div>
          </div>
        </div>
        <div class="flex-1">
          <label class="mb-1">Cao</label>
          <div
            class="border p-1 rounded bg-secondary flex items-center justify-between px-2"
          >
            <div>{{ selectedPackageBox?.dimension?.height }}</div>
            <div>{{ selectedPackageBox?.dimension?.dimensionUnit }}</div>
          </div>
        </div>
        <div class="flex-1">
          <div class="">Trọng lượng</div>
          <div
            class="border p-1 rounded bg-secondary flex items-center justify-between px-2"
          >
            <div>{{ selectedPackageBox?.weight }}</div>
            <div>{{ selectedPackageBox?.weightUnit }}</div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="
        dataFFM?.packageStatus !== 'PACKAGED' &&
        dataFFM?.fulfillmentStatus !== 'CANCELLED' &&
        order?.status !== 'CANCELLED' &&
        dataFFM?.exportStatus !== 'READY_TO_EXPORT' &&
        dataFFM?.exportStatus !== 'CANCELLED'
      "
    >
      <button
        @click="handleConfirmPackage"
        class="bg-primary text-white px-2 py-1 rounded w-full"
      >
        Xác nhận đóng gói sản phẩm
      </button>
    </div>
    <div>
      <ConfirmDialog
        v-if="isOpenConfirmPopup"
        title="Thông báo"
        :message="`Đơn hàng đang ở trạng thái 'Mới tạo' bạn có muốn cập nhật sang 'Đã xác nhận'`"
        @confirm="handleConfirm"
        @cancel="toogleConfirmDialog"
      ></ConfirmDialog>
    </div>
  </div>
</template>
<script setup lang="ts">
const { confirmPackage } = usePortal();
const props = defineProps(["dataPackageBox", "order", "dataFFM"]);
const emit = defineEmits(["fetchFFMStatus"]);

const selectedPackageBox = ref();
const auth = useCookie("auth") as any;
const handleConfirmPackage = async () => {
  if (props.order.status === "OPEN") {
    toogleConfirmDialog();
    return;
  }
  if (selectedPackageBox.value?.id === "1") {
    useNuxtApp().$toast.warning("Vui lòng chọn kích thước đóng gói");
    return;
  }
  try {
    const response = await confirmPackage(
      props.order?.id,
      selectedPackageBox.value?.id,
      auth.value?.user?.id
    );
    if (response.status === 1) {
      emit("fetchFFMStatus");
      props.dataFFM.packageStatus = "PACKAGED";
    }
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  if (props.dataFFM?.packedId) {
    const response = props.dataPackageBox.find(
      (box: any) => box?.id === props.dataFFM.packedId
    );
    selectedPackageBox.value = response;
    return;
  }
  selectedPackageBox.value = props.dataPackageBox[0];
});
const handleStatusPackage = (status: string) => {
  switch (status) {
    case "READY_FOR_PACKING":
      return "Sẵn sàng đóng gói";
      break;
    default:
      return "Đã đóng gói";
      break;
  }
};
const orderStore = useOrderStore();
const isOpenConfirmPopup = ref(false);
const toogleConfirmDialog = async () => {
  isOpenConfirmPopup.value = !isOpenConfirmPopup.value;
};
const { updateStatusApproved } = useOrder();
const handleConfirm = async () => {
  await updateStatusApproved(props.order?.id);
  await orderStore.updateOrder(props.order?.id);
};
</script>
