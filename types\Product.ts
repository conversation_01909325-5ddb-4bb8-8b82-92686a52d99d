/**
 * Product related type definitions
 */

export interface UnitDTO {
  __typename: "PartyDetail";
  id: string;
  name: string;
}

export interface ProductUnit {
  id: string;
  name: string;
  description?: string;
}

export interface ProductCategory {
  id: string;
  title: string;
  description?: string;
  level?: number;
}

export interface ProductImage {
  id: string;
  url: string;
  alt?: string;
  isPrimary?: boolean;
}

export interface Product {
  id: string;
  title: string;
  description?: string;
  shortDescription?: string;
  sku?: string;
  price: number;
  compareAtPrice?: number;
  cost?: number;
  vat?: number;
  weight?: number;
  status: "ACTIVE" | "INACTIVE" | "DRAFT";
  visibility: "PUBLIC" | "PRIVATE";
  trackQuantity: boolean;
  continueSellingWhenOutOfStock: boolean;
  requiresShipping: boolean;
  taxable: boolean;
  createdAt: string;
  updatedAt: string;

  // Relations
  unitDTO?: ProductUnit;
  categories: ProductCategory[];
  images?: ProductImage[];

  // Custom attributes
  customAttribute?: Record<string, any>;
}

export interface ProductFilterOptions {
  keyword?: string;
  category?: string;
  status?: string;
  visibility?: string;
  priceMin?: number;
  priceMax?: number;
  maxResult?: number;
  currentPage?: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

export interface ProductSearchResponse {
  data: Product[];
  total: number;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
}

// Component Props Types
export interface ProductSearchProps {
  isLoading?: boolean;
}

export interface ProductTableProps {
  ListProduct: Product[];
  isLoadingMore: boolean;
  isLoading: boolean;
  dataUnit: ProductUnit[];
  dataCategories: ProductCategory[];
}

export interface ProductItemProps {
  product: Product;
  dataUnit: ProductUnit[];
  dataCategories?: ProductCategory[];
}

// Event Types
export interface ProductSearchEvents {
  search: (data: { keyword: string; category: string }) => void;
  clearQuery: (data: { keyword: string; category: string }) => void;
}

export interface ProductTableEvents {
  updateProduct: (productId: string) => void;
  viewDetail: (productId: string) => void;
}

export interface ProductItemEvents {
  updateProduct: (productId: string) => void;
  viewDetail: (productId: string) => void;
}
