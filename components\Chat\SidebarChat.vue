<template>
  <section
    class="flex flex-col flex-none overflow-auto w-24 group lg:max-w-sm md:w-80 transition-all duration-300 ease-in-out"
  >
    <!-- User Login Info -->
    <div
      v-if="userLogin"
      class="header px-4 pt-2 flex flex-row justify-between items-center flex-none bg-blue-50 p-2"
    >
      <div class="flex items-center gap-2">
        <div
          class="w-10 h-10 bg-primary text-white flex justify-center items-center rounded-full"
        >
          <span class="text-lg font-bold">{{
            userLogin?.name?.charAt(0).toUpperCase()
          }}</span>
        </div>
        <span class="font-semibold text-primary">
          {{ userLogin?.name }}
        </span>
      </div>
    </div>

    <!-- Dropdown: Channel Selection -->
    <div class="flex items-center ml-4 mt-2">
      <label
        for="employee-select"
        class="block w-[40%] text-primary font-semibold"
        ><PERSON><PERSON><PERSON> trao đổi</label
      >
      <select
        id="employee-select"
        class="block w-[55%] outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
        v-model="selectedAppId"
        @change="$emit('change-app-id', $event.target.value)"
      >
        <option v-for="appId in listAppId" :key="appId.id" :value="appId.id">
          {{ appId.name }}
        </option>
      </select>
    </div>

    <!-- input search -->
    <div class="search-box p-2 flex-none">
      <div>
        <div class="relative">
          <label>
            <input
              class="rounded py-[6px] pr-6 pl-10 w-full border bg-white focus:outline-none transition duration-300 ease-in"
              type="text"
              placeholder="Tìm kiếm...."
              v-model="keyword"
              @keyup.enter="$emit('search', keyword)"
            />
            <span
              @click="$emit('search')"
              class="absolute top-0 left-0 mt-2 ml-3 inline-block cursor-pointer"
            >
              <svg viewBox="0 0 24 24" class="w-6 h-6">
                <path
                  fill="#bbb"
                  d="M16.32 14.9l5.39 5.4a1 1 0 0 1-1.42 1.4l-5.38-5.38a8 8 0 1 1 1.41-1.41zM10 16a6 6 0 1 0 0-12 6 6 0 0 0 0 12z"
                />
              </svg>
            </span>
            <span
              v-if="keyword"
              @click="handleClearKeyword"
              class="absolute top-0 right-0 mt-3 mr-3 inline-block cursor-pointer"
            >
              <svg
                xmlns="[invalid url, do not cite]"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </span>
          </label>
        </div>
      </div>
    </div>

    <!-- Filter Open/Closed Topics -->
    <div class="flex items-center ml-4 gap-2">
      <div
        @click="toogleChangeStatus('OPENED')"
        :class="
          selectedStatus === 'OPENED'
            ? 'cursor-pointer border-b-2 border-primary text-primary'
            : 'cursor-pointer'
        "
      >
        Đang mở
      </div>
      <div
        @click="toogleChangeStatus('CLOSED')"
        :class="
          selectedStatus === 'CLOSED'
            ? 'cursor-pointer border-b-2 border-primary text-primary'
            : 'cursor-pointer'
        "
      >
        Đang đóng
      </div>
    </div>

    <!-- Topic List -->
    <div
      ref="scrollTopic"
      class="contacts p-2 flex-1 overflow-y-scroll"
      v-if="topics?.length > 0"
    >
      <div
        v-for="(room, index) in topics"
        :key="room.roomId"
        class="flex justify-between items-center cursor-pointer p-3 hover:bg-secondary rounded-lg relative"
        :class="{ 'bg-secondary': room?.id === selectedTopicId }"
        @click="$emit('select-topic', room)"
      >
        <div
          class="w-12 h-12 bg-gray-300 text-white flex justify-center items-center rounded-full"
          :style="{ backgroundColor: getRoomColor(index) }"
        >
          <span class="text-lg font-bold">{{
            room?.name?.charAt(0).toUpperCase()
          }}</span>
        </div>
        <div class="flex-auto min-w-0 ml-4 mr-6">
          <p class="text-xs text-primary">{{ room?.customer?.name }}</p>
          <p class="truncate text-primary max-w-[200px]">
            {{ room.name || "Unnamed Room" }}
          </p>
          <div class="text-xs flex items-center justify-between">
            <div>
              <span class="">KD: </span>
              <span class="font-semibold">{{ room?.accountableName }}</span>
            </div>
            <span>{{ formatTimestampV2(room?.createdStamp) }}</span>
          </div>
        </div>
        <span
          v-if="room.unreadCount > 0"
          class="bg-red-500 text-white text-xs rounded-full px-2 py-1"
        >
          {{ room.unreadCount }}
        </span>
      </div>
    </div>

    <div v-else class="flex-1 flex items-center justify-center">
      <p class="text-gray-500">Không có cuộc trò chuyện nào</p>
    </div>
    <div class="mb-[50px]"></div>
  </section>
</template>

<script setup>
const props = defineProps({
  userLogin: Object,
  topics: Array,
  selectedTopicId: String,
  hasMoreData: Boolean,
});
const selectedAppId = ref();
const keyword = ref();
const listAppId = ref();
const selectedStatus = ref("OPENED");

const emit = defineEmits([
  "select-topic",
  "search",
  "clear-search",
  "set-status",
  "change-app-id",
  "scroll",
]);
onMounted(async () => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");
  listAppId.value = appId;
  const zaloApp = listAppId.value.find((app) => app?.name === "ZALO MESSAGE");
  if (zaloApp) {
    selectedAppId.value = zaloApp?.id;
  } else {
    selectedAppId.value = listAppId.value[0]?.id;
  }
});
const colors = [
  "#FF6B6B",
  "#6BCB77",
  "#4D96FF",
  "#FFAB4C",
  "#FF7F50",
  "#FFD700",
  "#DC143C",
];
const getRoomColor = (index) => colors[index % colors.length];
const handleClearKeyword = () => {
  keyword.value = "";
  emit("search", keyword.value);
};
const toogleChangeStatus = (status) => {
  selectedStatus.value = status;
  emit("set-status", status);
};
const scrollTopic = ref(null);

useInfiniteScroll(
  scrollTopic,
  async () => {
    if (props.hasMoreData) {
      emit("scroll");
      nextTick();
    }
  },
  {
    distance: 10,
  }
);
</script>
