<template>
  <div class="flex flex-col m-0 md:p-4 p-2 bg-white rounded">
    <div class="grid grid-cols-1 gap-2">
      <div class="flex justify-between gap-2">
        <di
          rows="8"
          id="note"
          v-model="orderDescription"
          class="py-1 px-2 w-full h-[100px] text-sm rounded outline-none border bg-secondary"
          type="text"
          placeholder="Mô tả"
        ></di>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { updateOrderDescription } = useDiary();
const orderId = ref("");
const route = useRoute();
const props = defineProps({
  description: {
    type: String,
  },
});
const orderDescription = ref(props.description);
watch(
  () => props.description,
  (value) => {
    orderDescription.value = value;
  }
);
//mounted
onMounted(() => {
  orderId.value = route.params.id as string;
});
const isShow = ref(true);

const toggleContent = () => {
  isShow.value = !isShow.value;
};
</script>

<style scoped></style>
