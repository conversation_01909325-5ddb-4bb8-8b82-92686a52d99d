<template>
  <div class="flex h-screen bg-secondary overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -inset-10 opacity-30">
        <div
          class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"
        ></div>
        <div
          class="absolute top-3/4 left-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-2000"
        ></div>
      </div>
    </div>

    <!-- Sidebar -->
    <Transition name="sidebar" mode="out-in">
      <Sidebar @toggleSidebar="toggleSidebar" :isSlimSidebar="isSlimSidebar" />
    </Transition>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 z-10 min-w-0">
      <!-- Header -->
      <AppHeader :isSlimSidebar="isSlimSidebar" class="relative z-30" />

      <!-- Content Container -->
      <main class="flex-1 overflow-hidden">
        <div class="h-screen-50">
          <!-- Content Wrapper -->
          <div
            class="h-full scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
          >
            <div class="min-h-full">
              <!-- Content Background -->
              <div class="relative">
                <slot></slot>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Loading Overlay -->
    <Transition name="fade">
      <div
        v-if="isLoading"
        class="fixed inset-0 z-50 flex items-center justify-center"
      >
        <div
          class="bg-white rounded-2xl p-8 shadow-2xl border border-white/20 flex flex-col items-center space-y-4"
        >
          <LoadingSpinner />
        </div>
      </div>
    </Transition>

    <!-- Session Expired Modal -->
    <SessionExpiredModal
      :is-visible="isSessionExpiredModalVisible"
      :countdown-seconds="sessionExpiredCountdown"
      :allow-stay-here="false"
      @login-now="handleLoginNow"
      @time-expired="handleTimeExpired"
    />
  </div>
</template>

<script setup>
// Components
const Sidebar = defineAsyncComponent(() =>
  import("~/components/generalElements/Sidebar.vue")
);
const AppHeader = defineAsyncComponent(() =>
  import("~/components/generalElements/AppHeader.vue")
);
const LoadingSpinner = defineAsyncComponent(() =>
  import("~/components/common/LoadingSpinner.vue")
);
const SessionExpiredModal = defineAsyncComponent(() =>
  import("~/components/Modal/SessionExpiredModal.vue")
);

// Session expired modal management
const {
  isSessionExpiredModalVisible,
  sessionExpiredCountdown,
  handleLoginNow,
  handleTimeExpired,
} = useSessionExpired();

// State
const isSlimSidebar = ref(true);
const isLoading = ref(false);

// Functions
const toggleSidebar = () => {
  isSlimSidebar.value = !isSlimSidebar.value;
};

// Route loading management
const router = useRouter();

// Watch for route changes
watch(
  () => router.currentRoute.value.path,
  (newPath, oldPath) => {
    if (newPath !== oldPath && oldPath) {
      isLoading.value = true;
      // Auto hide loading after delay
      setTimeout(() => {
        isLoading.value = false;
      }, 500);
    }
  }
);

// Provide layout state to child components
provide("layoutState", {
  isSlimSidebar: readonly(isSlimSidebar),
  toggleSidebar,
  isLoading: readonly(isLoading),
});
</script>

<style scoped>
/* Sidebar transitions */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.sidebar-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

/* Fade transitions */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.375rem;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background-color: transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Background animation */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient-shift 20s ease infinite;
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .p-6 {
    padding: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gradient-to-br {
    background: #f8fafc;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bg-gradient-to-br {
    animation: none;
  }

  .sidebar-enter-active,
  .sidebar-leave-active,
  .fade-enter-active,
  .fade-leave-active {
    transition: none;
  }
}

/* Focus management */
.focus-within\:ring-2:focus-within {
  --tw-ring-color: #3f51b5;
}
</style>
