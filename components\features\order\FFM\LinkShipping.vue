<template>
  <div class="text-sm">
    <div class="space-y-2 px-2 py-1 bg-white rounded w-full">
      <div>
        <div class="flex items-center justify-between">
          <div class="font-bold text-primary mt-1">Liên kết vận chuyển</div>
          <div
            :class="
              dataFFM?.connectStatus === 'READY_TO_CONNECT'
                ? 'text-yellow-400'
                : 'text-green-400'
            "
          >
            {{ handleStatusConnect(dataFFM?.connectStatus) }}
          </div>
        </div>
      </div>
      <ShippingService
        class="bg-white"
        :orderDetail="order"
        :isPageFFM="true"
      ></ShippingService>
      <InfoFFMShippingAddress
        :order="order"
        :shippingAddress="shippingAddress"
        :customer="customer"
      ></InfoFFMShippingAddress>
      <!--  -->
      <div
        v-if="
          dataFFM?.connectStatus !== 'CONNECTED' &&
          order?.status !== 'CANCELLED' &&
          order.status !== 'COMPLETED' &&
          dataFFM?.packageStatus !== 'READY_FOR_PACKING' &&
          dataFFM?.exportStatus !== 'READY_TO_EXPORT'
        "
      >
        <button
          @click="handleConnectShipment"
          class="text-white bg-primary px-2 py-1 rounded w-full mt-2"
        >
          Liên kết vận chuyển
        </button>
      </div>
      <div
        v-if="
          dataFFM?.takenStatus !== 'TAKEN' &&
          dataFFM?.connectStatus === 'CONNECTED'
        "
      >
        <button
          @click="handleHandlePackage"
          class="text-white bg-primary px-2 py-1 rounded w-full mt-2"
        >
          Bàn giao đơn vị vận chuyển
        </button>
      </div>

      <div>
        <button
          v-if="
            dataFFM?.fulfillmentStatus === 'PROCESSING_DELIVERY' ||
            dataFFM?.fulfillmentStatus === 'DELIVERED'
          "
          @click="handleCompleteOrder"
          class="text-white bg-primary px-2 py-1 rounded w-full mt-2"
        >
          Xác nhận giao hàng
        </button>
      </div>
    </div>
    <div
      v-if="
        order.status !== 'COMPLETED' &&
        dataFFM?.fulfillmentStatus !== 'CANCELLED' &&
        order?.status !== 'CANCELLED'
      "
      class="mt-2 p-2 bg-white"
    >
      <button
        @click="tooglePopupReason"
        class="text-red-500 border border-red-500 px-2 py-1 rounded w-full bg-white"
      >
        Hủy FFM và nhập hàng lại kho
      </button>
    </div>
    <CancelOrderPopup
      v-if="isOpenPopupReason"
      :order="order"
      :title="'Hủy FFM và nhập kho'"
      :text="''"
      :reasonText="'Lý do hủy'"
      :dataReason="dataReason"
      @cancel="tooglePopupReason"
      @confirm="handleCancelFFMOrder"
    >
    </CancelOrderPopup>
    <ConfirmDialog
      v-if="isOpenConfirmPopup"
      title="Thông báo"
      :message="`Đơn hàng đang ở trạng thái 'Mới tạo' bạn có muốn cập nhật sang 'Đã xác nhận'`"
      @confirm="handleConfirm"
      @cancel="toogleConfirmDialog"
    ></ConfirmDialog>
  </div>
</template>
<script setup lang="ts">
const provinces = ref<any>([]);
const districts = ref<any>([]);
const wards = ref<any>([]);
const selectedCity = ref("");
const selectedDistrict = ref("");
const selectedWard = ref("");
const { $sdk } = useNuxtApp();
const props = defineProps(["order", "dataFFM", "shippingAddress", "customer"]);
const emit = defineEmits(["fetchFFMStatus"]);
const getProvinces = async () => {
  try {
    const response = await $sdk.user.getProvinces();
    provinces.value = response;
  } catch (error) {
    console.error("Error fetching provinces:", error);
  }
};
const fetchDistricts = async () => {
  try {
    const response = await $sdk.user.getDistricts(selectedCity.value);
    districts.value = response;
    selectedDistrict.value = "";
    wards.value = [];
    selectedWard.value = "";
  } catch (error) {
    console.error("Error fetching districts:", error);
  }
};

const fetchWards = async () => {
  try {
    const response = await $sdk.user.getWards(selectedDistrict.value);
    wards.value = response;
  } catch (error) {
    console.error("Error fetching wards:", error);
  }
};
const address = ref();
const phone = ref();
// get thông tin kho
const { getInforWarehouse } = useWarehouse();
const warehouse = ref();
const getWarehouse = async () => {
  try {
    const response = await getInforWarehouse(
      props.order?.order?.customAttribute?.facilityId
    );
    warehouse.value = response;
    address.value = response?.address?.address1;
    phone.value = response?.phone;
    selectedCity.value = response?.address?.provinceCode;
    await fetchDistricts();
    selectedDistrict.value = response?.address?.districtCode;
    await fetchWards();
    selectedWard.value = response?.address?.wardCode;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await getProvinces();
});
const {
  connectShipment,
  handlePackage,
  completeOrder,
  completeCancelFFMOrder,
} = usePortal();
const auth = useCookie("auth") as any;
const handleConnectShipment = async () => {
  if (props.order.status === "OPEN") {
    toogleConfirmDialog();
    return;
  }

  try {
    const response = await connectShipment(
      props.order?.id,
      auth.value?.user?.id
    );

    if (response.status === 1) {
      props.dataFFM.connectStatus = "CONNECTED";
      emit("fetchFFMStatus");
      await orderStore.updateOrder(props.order?.id);
    }
  } catch (error) {
    throw error;
  }
};
const orderStore = useOrderStore();

// bàn giao cho đơn vị vận chuyển
const handleHandlePackage = async () => {
  try {
    const response = await handlePackage(props.order?.id, auth.value?.user?.id);
    if (response.status === 1) {
      props.dataFFM.takenStatus = "TAKEN";
      props.dataFFM.fulfillmentStatus = "PROCESSING_DELIVERY";
      emit("fetchFFMStatus");
      await orderStore.updateOrder(props.order?.id);
    }
  } catch (error) {
    throw error;
  }
};
const handleCompleteOrder = async () => {
  try {
    const response = await completeOrder(props.order?.id, auth.value?.user?.id);
    if (response.status === 1) {
      props.dataFFM.fulfillmentStatus = "FULFILLED";
      emit("fetchFFMStatus");
      await orderStore.updateOrder(props.order?.id);
    }
  } catch (error) {
    throw error;
  }
};
const handleStatusConnect = (status: string) => {
  switch (status) {
    case "READY_TO_CONNECT":
      return "Sẵn sàng Liên kết";
      break;
    default:
      return "Đã liên kết";
      break;
  }
};
const handleStatusTaken = (status: string) => {
  switch (status) {
    case "READY_TO_HANDLE":
      return "Sẵn sàng bàn giao";
      break;
    default:
      return "Đã bàn giao";
      break;
  }
};
const isOpenPopupReason = ref(false);
const tooglePopupReason = () => {
  isOpenPopupReason.value = !isOpenPopupReason.value;
};
const handleCancelFFMOrder = async (note: string) => {
  let reason;
  switch (note) {
    case "Khách hàng yêu cầu":
      reason = "CUSTOMER";
      break;
    case "Thông tin chưa hợp lệ":
      reason = "FRAUD";
      break;
    case "Không đủ hàng trong kho":
      reason = "INVENTORY";
      break;
    case "Không thanh toán đơn hàng":
      reason = "DECLINED";
      break;
    default:
      reason = "OTHER";
      break;
  }
  try {
    const response = await completeCancelFFMOrder(
      props.order?.id,
      note,
      reason
    );
    if (response.status === 1) {
      await orderStore.updateOrder(props.order?.id);
    }
    return response;
  } catch (error) {
    throw error;
  }
};
const dataReason = [
  { name: "Chọn lý do hủy" },
  {
    name: "Khách hàng yêu cầu",
    value: "CUSTOMER",
  },
  {
    name: "Thông tin chưa hợp lệ",
    value: "FRAUD",
  },
  {
    name: "Không đủ hàng trong kho",
    value: "INVENTORY",
  },
  {
    name: "Không thanh toán đơn hàng",
    value: "DECLINED",
  },
  {
    name: "Khác",
    value: "OTHER",
  },
];
const isOpenConfirmPopup = ref(false);
const toogleConfirmDialog = async () => {
  isOpenConfirmPopup.value = !isOpenConfirmPopup.value;
};
const { updateStatusApproved } = useOrder();
const handleConfirm = async () => {
  await updateStatusApproved(props.order?.id);
  await orderStore.updateOrder(props.order?.id);
};
</script>
