<template>
  <td class="pl-4 py-3 text-sm text-gray-900 max-w-6 truncate">
    {{ index + 1 }}
  </td>
  <td
    class="px-2 py-3 text-sm text-primary max-w-[150px] truncate"
    @click="handlePageCustomerDetail(customer.id)"
  >
    {{ customer.id }}
  </td>

  <td class="px-2 py-3 text-sm text-gray-900 max-w-[150px] truncate">
    {{ customer.name }}
  </td>
  <td class="px-2 py-3 text-sm text-gray-900 truncate capitalize">
    {{ customer.phone }}
  </td>
  <td class="px-2 py-3 text-sm text-gray-900 max-w-[60px] truncate">
    <div :class="handleMemberLevel(customer?.memberLevel)">
      {{ customer?.memberLevel || "" }}
    </div>
  </td>
  <td
    class="py-3 text-sm text-gray-900 sm:px-3 max-w-[300px] space-y-3 capitalize"
  >
    {{ customer.birthDate ? formatTimestampV2(customer.birthDate) : "" }}
  </td>
  <td
    class="px-2 py-3 text-sm text-gray-900 max-w-[150px] truncate capitalize space-y-3"
  >
    {{ customer.email }}
  </td>
  <td
    class="px-2 py-3 text-sm text-gray-900 max-w-[150px] truncate capitalize space-y-3 text-left"
  >
    {{ customer.address }}
  </td>
  <td
    class="px-2 py-3 text-sm text-gray-900 max-w-[150px] truncate capitalize space-y-3 text-left"
  >
    <input
      type="checkbox"
      class="accent-primary w-4 h-4 cursor-pointer"
      v-model="isChecked"
    />
  </td>

  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
</template>
<script setup lang="ts">
const props = defineProps(["customer", "index"]);
const customerStore = useCustomerStore();
const isChecked = ref(false);
watch(
  () => isChecked.value,
  (newVal, oldVal) => {
    if (newVal) {
      // kiểm tra nếu mảng lớn 0 và đã có phần tử
      if (customerStore.listCustomerAction?.length > 0) {
        const res = customerStore.listCustomerAction.find(
          (item: any) => item.id === props.customer?.id
        );
        if (!res) {
          customerStore.addListCustomerAction(props.customer);
        }
      } else {
        customerStore.addListCustomerAction(props.customer);
      }
    } else {
      customerStore.removeCustomerListAction(props.customer);
    }
  }
);
watch(
  () => customerStore.isCheckGlobal,
  (newVal, oldVal) => {
    if (newVal) {
      isChecked.value = true;
    } else {
      isChecked.value = false;
    }
  }
);
const router = useRouter();
const route = useRoute();
const isLoading = ref(false);
const handlePageCustomerDetail = async (customerId: string) => {
  // Use tab-isolated context instead of cookies
  const { orgId, storeId } = useTabContext();
  let showLoadingTimeout: ReturnType<typeof setTimeout> | null = null;
  showLoadingTimeout = setTimeout(() => {
    isLoading.value = true;
  }, 200);
  await navigateTo(
    `/customer/detail?orgId=${orgId.value}&storeId=${storeId.value}&customerId=${customerId}`
  );
  if (showLoadingTimeout) {
    clearTimeout(showLoadingTimeout);
  }
  isLoading.value = false;
};

onMounted(() => {
  if (customerStore.listCustomerAction?.length > 0) {
    const res = customerStore.listCustomerAction.find(
      (item: any) => item.id === props.customer?.id
    );
    if (res) {
      isChecked.value = true;
    }
  }
});
const handleMemberLevel = (memberLevel: string) => {
  switch (memberLevel) {
    case "MEMBER":
      return "text-primary";
    case "SILVER":
      return "text-gray-500";
    case "GOLD":
      return "text-yellow-300";
    case "PLATINUM":
      return "text-green-500";
    default:
      return "";
  }
};
</script>
