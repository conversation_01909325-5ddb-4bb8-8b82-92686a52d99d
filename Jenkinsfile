podTemplate(yaml: '''
    apiVersion: v1
    kind: Pod
    metadata:
      name: kaniko
      namespace: integration
    spec:
      containers:
      - name: kaniko
        image: 'gcr.io/kaniko-project/executor:v1.22.0-debug'
        command:
        - sleep
        args:
        - 99d
        volumeMounts:
        - name: kaniko-secret
          mountPath: /kaniko/.docker
      restartPolicy: Never
      imagePullPolicy: Always
      volumes:
      - name: kaniko-secret
        secret:
          secretName: dockercred
          items:
          - key: .dockerconfigjson
            path: config.json
''') {
    podTemplate(containers: [
            containerTemplate(
                    name: 'maven',
                    image: 'maven:3.8.6-openjdk-18',
                    command: 'sleep',
                    args: '30d'
            ),
            containerTemplate(
                    name: 'kubectl',
                    image: 'alpine/k8s:1.23.14',
                    command: 'sleep',
                    args: '30d'
            )
    ],
            volumes: [
                    secretVolume(mountPath: '/etc/maven/', secretName: 'maven-settings')
            ],
            envVars: [
                    secretEnvVar(key: 'ARTIFACTS_USERNAME', secretName: 'artifacts-username', secretKey: 'ARTIFACTS_USERNAME'),
                    secretEnvVar(key: 'ARTIFACTS_PASSWORD', secretName: 'artifacts-password', secretKey: 'ARTIFACTS_PASSWORD')
            ]) {
        node(POD_LABEL) {
            def scm_vars = checkout scm
            def commit_branch = scm_vars.GIT_BRANCH.split('/')[1]
            def deploy_env = commit_branch.contains("production") ? "production" : "development"
            def commit_id = scm_vars.GIT_COMMIT.take(7)
            stage('Build Docker Image') {
                    container('kaniko') {
                        sh """
                    export IFS=''
                    /kaniko/executor --context `pwd` --build-arg ENV=${deploy_env}   --destination containers-registry.longvan.vn/longvan-docker/dms-service:${commit_id}-${deploy_env}
                    """
                    }
                }

        }
    }
}