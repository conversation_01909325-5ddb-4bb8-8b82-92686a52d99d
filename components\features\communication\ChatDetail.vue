<template>
  <section class="md:h-[90vh] h-[93svh] w-full">
    <div
      class="flex flex-col flex-auto border-l bg-white h-[93vh] col-span-1"
      v-if="roomName"
    >
      <!-- Chat header -->

      <div
        class="chat-header md:px-6 px-2 py-4 flex flex-row flex-none justify-between items-center shadow-sm"
      >
        <div class="flex items-center">
          <div
            v-if="isMobile && isRoomSelected"
            class="back-button lg:hidden"
            @click="exitConversation"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </div>
          <div
            v-if="threadSubject?.body"
            class="w-10 h-10 bg-gray-300 text-white flex justify-center items-center rounded-full"
          >
            <span class="text-lg font-bold">{{
              activeRoom?.name.charAt(0).toUpperCase()
            }}</span>
          </div>
          <div v-if="threadSubject?.body" class="ml-4 text-sm">
            <p class="font-bold text-primary">{{ roomName }}</p>
            <p class="text-gray-500 font-bold">
              Chủ đề: {{ threadSubject.body }}
            </p>
          </div>
        </div>
        <div class="flex items-center cursor-pointer"></div>

        <!-- Options buttons -->
        <div class="flex items-center cursor-pointer">
          <button
            @click="showModal = true"
            class="text-primary font-bold flex items-center"
          >
            <label class="text-xs cursor-pointer" v-if="threadSubject?.body"
              >Đóng chủ đề</label
            >
          </button>

          <Modal
            :isOpen="showModal"
            @update:isOpen="showModal = $event"
            @confirm="handleConfirm"
          >
            <h2 class="text-lg font-semibold text-gray-800">
              Bạn có chắc chắn muốn đóng chủ đề này?
            </h2>
            <p class="text-gray-600 mt-2">
              Khi bạn đóng chủ đề, bạn sẽ không thể nhận được thông báo từ chủ
              đề này nữa.
            </p>
          </Modal>
        </div>
      </div>
      <!-- Chat body -->
      <div
        ref="chatBodyRef"
        v-if="messages.length >= 0"
        class="p-4 flex-1 overflow-y-scroll bg-[#fff]"
      >
        <div
          v-for="(message, index) in messages"
          :key="message?.eventId"
          class="flex flex-col space-y-1 mb-1 text-sm"
        >
          <!-- tên topic -->
          <div
            :class="
              message?.sender !== '@demo:matrix-synapse.longvan.vn'
                ? 'flex-row-reverse'
                : 'flex-row'
            "
            class="flex items-center"
          >
            <div class="">
              <div>
                <div
                  v-if="message?.sender === '@demo:matrix-synapse.longvan.vn'"
                >
                  <div
                    v-if="
                      index === 0 ||
                      message?.sender !== messages[index - 1]?.sender ||
                      (message?.timestamp &&
                        messages[index - 1]?.timestamp &&
                        differenceInHours(
                          new Date(message.timestamp),
                          new Date(messages[index - 1].timestamp)
                        ) >= 1)
                    "
                    class="space-x-1"
                  >
                    <span class="font-semibold">
                      {{ selectedRoom?.customer?.name || "Ẩn danh" }}
                    </span>
                    <span class="text-xs">{{
                      formatTime24hWithMoment(message?.timestamp)
                    }}</span>
                  </div>
                </div>
                <div v-else class="">
                  <div
                    v-if="
                      index === 0 ||
                      message?.sender !== messages[index - 1]?.sender ||
                      (message?.timestamp &&
                        messages[index - 1]?.timestamp &&
                        differenceInHours(
                          new Date(message.timestamp),
                          new Date(messages[index - 1].timestamp)
                        ) >= 1)
                    "
                    class="space-x-1"
                  >
                    <span class="font-semibold">
                      {{ message?.senderName || "Ẩn danh" }}
                    </span>
                    <span class="text-xs">{{
                      formatTime24hWithMoment(message?.timestamp)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- nội dung tin nhắn -->
          <div
            :class="
              message?.sender !== '@demo:matrix-synapse.longvan.vn'
                ? 'flex-row-reverse'
                : 'flex-row'
            "
            class="flex items-center gap-2"
          >
            <div class="">
              <div
                :class="
                  message?.sender !== '@demo:matrix-synapse.longvan.vn'
                    ? 'bg-myChat text-left'
                    : 'bg-secondary text-left'
                "
                class="px-2 py-1 rounded-2xl max-w-lg"
              >
                <div>
                  <!-- Hiển thị hình ảnh -->
                  <template
                    v-if="message.type === 'm.image' && message?.contentUrl"
                  >
                    <img
                      :src="message.contentUrl"
                      alt="Image"
                      class="rounded-md max-w-full h-auto"
                      loading="lazy"
                    />
                  </template>
                  <!-- Hiển thị video -->
                  <template
                    v-else-if="message.type === 'm.video' && message.contentUrl"
                  >
                    <video controls class="rounded-md max-w-full h-auto">
                      <source :src="message.contentUrl" type="video/mp4" />
                    </video>
                  </template>
                  <!-- Hiển thị audio -->
                  <template
                    v-else-if="message.type === 'm.audio' && message.contentUrl"
                  >
                    <audio controls class="w-full">
                      <source :src="message.contentUrl" type="audio/mpeg" />
                    </audio>
                  </template>
                  <!-- Hiển thị tệp với tùy chọn "Save As" -->
                  <template
                    v-else-if="message.type === 'm.file' && message.contentUrl"
                  >
                    <a
                      :href="message.contentUrl"
                      @click="saveAsFile(message.contentUrl, message.content)"
                      class="text-blue-500 underline"
                    >
                      {{ message.content }}
                    </a>
                  </template>

                  <!-- Hiển thị tin nhắn văn bản -->
                  <template v-else>
                    <p class="leading-relaxed">{{ message.content }}</p>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div ref="mySection" class="h-0"></div>
      </div>

      <!-- Chat footer -->
      <div v-if="threadSubject?.body" class="chat-footer flex-none shadow">
        <div class="flex flex-row items-center w-full">
          <label class="pl-4 pr-2 cursor-pointer">
            <input type="file" class="hidden" @change="handleFileUpload" />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              class="size-5"
            >
              <path
                d="M9.25 13.25a.75.75 0 0 0 1.5 0V4.636l2.955 3.129a.75.75 0 0 0 1.09-1.03l-4.25-4.5a.75.75 0 0 0-1.09 0l-4.25 4.5a.75.75 0 1 0 1.09 1.03L9.25 4.636v8.614Z"
              />
              <path
                d="M3.5 12.75a.75.75 0 0 0-1.5 0v2.5A2.75 2.75 0 0 0 4.75 18h10.5A2.75 2.75 0 0 0 18 15.25v-2.5a.75.75 0 0 0-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5Z"
              />
            </svg>
          </label>

          <div class="relative flex-grow px-2 py-2">
            <input
              v-model="newMessage"
              class="input-message rounded-full py-2 pl-3 pr-10 w-full border bg-secondary focus:bg-secondary focus:outline-none focus:shadow-md transition duration-300 ease-in"
              type="text"
              placeholder="Aa"
              @keyup.enter="sendMessage"
            />
          </div>

          <button @click="sendMessage" type="button" class="pr-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!--  -->
    <div v-else class="h-[93vh]">
      <div
        class="chat-header px-6 py-4 flex flex-row flex-none justify-between items-center shadow-sm"
      >
        <div class="flex items-center">
          <div
            v-if="isMobile && isRoomSelected"
            class="back-button lg:hidden"
            @click="exitConversation"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </div>
          <div
            class="w-10 h-10 bg-gray-300 text-white flex justify-center items-center rounded-full"
          >
            <span class="text-lg font-bold">{{
              selectedRoom?.name?.charAt(0).toUpperCase()
            }}</span>
          </div>
          <div class="ml-4 text-sm">
            <p class="font-bold text-primary">{{ selectedRoom?.name }}</p>
          </div>
        </div>
        <!--  -->
        <div class="flex items-center cursor-pointer">
          <button
            @click="showModal = true"
            class="text-primary font-bold flex items-center"
          >
            <label class="text-xs cursor-pointer">Đóng chủ đề</label>
          </button>

          <Modal
            :isOpen="showModal"
            @update:isOpen="showModal = $event"
            @confirm="handleConfirm"
          >
            <h2 class="text-lg font-semibold text-gray-800">
              Bạn có chắc chắn muốn đóng chủ đề này?
            </h2>
            <p class="text-gray-600 mt-2">
              Khi bạn đóng chủ đề, bạn sẽ không thể nhận được thông báo từ chủ
              đề này nữa.
            </p>
          </Modal>
        </div>
      </div>
      <div
        v-if="!route.query?.roomId"
        class="flex items-center justify-center h-[70vh]"
      >
        <div class="bg-red-200 p-6 text-red-600 rounded">
          Chủ đề này chưa hỗ trợ tính năng chat
        </div>
      </div>
    </div>
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </section>
</template>

<script setup>
const chatBodyRef = ref(null);
import { differenceInHours } from "date-fns";

const props = defineProps(["selectedRoom"]);
const messages = ref([]);
const loading = ref(true);
const newMessage = ref("");
const userMatrixId = ref("");
const threadSubject = ref("Your Thread Subject Here");
const roomName = ref("");
const showSidebar = ref(false);
const showChatSale = ref(false);
const { closeTopic, getMessage } = useChat();

const route = useRoute();
const router = useRouter();
const { $matrixClient } = useNuxtApp();
const roomId = ref(route.query.roomId);
const threadId = ref(route.query.threadId);
const showModal = ref(false);
const { getTopics } = useChat();

const handleConfirm = () => {
  closeTopicChat();
  showModal.value = false;
};

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value;
};

// Toggles ChatSale visibility
const toggleChatSale = () => {
  showChatSale.value = !showChatSale.value;
  if (showChatSale.value) {
    showSidebar.value = false;
  }
};
const scrollToBottom = async () => {
  const chatBody = chatBodyRef.value;
  if (chatBody) {
    await nextTick(); // Đảm bảo DOM đã cập nhật xong
    chatBody.scrollTop = chatBody.scrollHeight;
  }
};

const closeChatSale = () => {
  showChatSale.value = false;
  showSidebar.value = true; // Open Sidebar when ChatSale is closed
};
// Check if room is selected
const isRoomSelected = computed(() => !!route.query.roomId);

const saveAsFile = async (fileUrl, fileName) => {
  try {
    // Fetch the file data
    const response = await fetch(fileUrl);
    if (!response.ok)
      throw new Error(`Failed to fetch file: ${response.statusText}`);

    // Convert the response into a Blob
    const blob = await response.blob();

    // Create a temporary anchor element for the download
    const a = document.createElement("a");
    a.href = URL.createObjectURL(blob);
    a.download = fileName; // Set the file name
    document.body.appendChild(a); // Append anchor to the DOM
    a.click(); // Trigger download
    document.body.removeChild(a); // Clean up the DOM
  } catch (error) {
    console.error("Error during file download:", error);
    alert("Could not download the file. Please try again.");
  }
};

// Check if on mobile
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};
const mxcUrlToHttp = (url) => {
  const baseUrl = $matrixClient.getClient().getHomeserverUrl();
  return url.replace("mxc://", `${baseUrl}/_matrix/media/r0/download/`);
};
// Function to load messages from the Matrix server
const auth = useCookie("auth").value;
const { requestJoinRoom } = useChat();
const config = useRuntimeConfig();
const customerMatrixId = ref();
const handleGetUserId = () => {
  const res = config.public.MATRIX_LINK.split("//")[1];
  // kiểm tra user có tồn tại trong phòng hay chưa
  customerMatrixId.value = `@${props.selectedRoom?.customer?.id}${res}`;
};
const handleCheckRoom = async () => {
  try {
    if (!route.query?.roomId) {
      return;
    }
    // cắt chuỗi để ghép lấy userId
    const res = config.public.MATRIX_LINK.split("//")[1];
    // kiểm tra user có tồn tại trong phòng hay chưa
    const response = await $matrixClient.checkUserExisted(
      roomId.value,
      `@${auth?.user?.id}${res}`
    );
    // nếu chưa có thì mời + accept vào room
    if (!response) {
      try {
        await requestJoinRoom(roomId.value, [auth?.user?.id]);
        await $matrixClient.joinRoom(roomId.value);
      } catch (error) {
        useNuxtApp().$toast.warning(`${error}`);
        messages.value = [];
        roomName.value = "";
        throw error;
      }
    }
  } catch (error) {
    throw error;
  }
};
const loadMessages = async () => {
  if (roomId.value) {
    try {
      const thread = await $matrixClient.loadThreadMessages(
        roomId.value,
        threadId.value
      );
      // Xử lý dữ liệu khi tải thành công
      if (thread) {
        roomName.value = thread.name;
        messages.value = thread.messages;
        threadSubject.value = thread.topic;
        await nextTick();

        scrollToBottom();
      } else {
        messages.value = [];
      }
      console.log("Là gì", thread);
    } catch (error) {
      if (error.message === "Thời gian tải vượt quá 10 giây") {
        useNuxtApp().$toast.warning(
          "Lấy danh sách tin nhắn thất bại: Hết thời gian chờ"
        );
      } else {
        // useNuxtApp().$toast.warning("Lấy danh sách tin nhắn thất bại");
      }
      console.error("Error loading messages:", error);
    }
  }
};

// Send a new message
const sendMessage = async () => {
  if (newMessage.value.trim() === "") return;
  try {
    await $matrixClient.sendThreadMessage(
      roomId.value,
      threadId.value,
      newMessage.value
    );
    newMessage.value = ""; // Clear input after sending
    await loadMessages();
    /// kiểm tra người nhập tin nhắn
    if (!props?.selectedRoom?.accountableId) {
      emits("addAccountableId");
    }
    ////
    scrollToBottom();
  } catch (error) {
    console.error("Error sending message:", error);
    alert("Không thể gửi tin nhắn. Vui lòng thử lại.");
  }
};

// Close topic chat and navigate back
const topics = ref([]);
const emits = defineEmits(["closeTopic", "back", "addAccountableId"]);

const closeTopicChat = async () => {
  roomName.value = null;
  messages.value = [];
  messages.value = "";
  threadSubject.value = null;
  emits("closeTopic");
};
// Exit conversation on mobile
const exitConversation = () => {
  emits("back");
};
const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  try {
    const client = $matrixClient.getClient();

    // Upload file lên Matrix
    const uploadResponse = await client.uploadContent(file, {
      type: file.type,
      rawResponse: false,
    });

    const mxcUrl = uploadResponse.content_uri;

    // Xác định loại msgtype dựa trên MIME
    let msgtype;
    if (file.type.startsWith("image/")) {
      msgtype = "m.image";
    } else if (file.type.startsWith("video/")) {
      msgtype = "m.video";
    } else if (file.type.startsWith("audio/")) {
      msgtype = "m.audio";
    } else {
      msgtype = "m.file"; // Các loại file khác (PDF, ZIP, DOCX,...)
    }

    const message = {
      body: file.name, // Tên file
      info: {
        size: file.size, // Kích thước file
        mimetype: file.type, // Loại MIME
      },
      msgtype: msgtype, // Loại tin nhắn
      url: mxcUrl, // URL file từ Matrix
    };

    // Gửi tin nhắn qua Matrix
    await $matrixClient.sendThreadMessage(
      roomId.value,
      threadId.value,
      message
    );
  } catch (error) {
    console.error("Error uploading file:", error);
    alert("Không thể tải lên tệp. Vui lòng thử lại.");
  }
};

// Initialize Matrix connection and load messages
const isLoading = ref(false);

onMounted(async () => {
  // Bật loading ngay khi bắt đầu
  isLoading.value = true;

  checkMobile();
  window.addEventListener("resize", checkMobile);
  const { token, userId } = await $matrixClient.loadCredentials();
  userMatrixId.value = userId;

  if (token) {
    try {
      await $matrixClient.connectMatrix(token);
      const client = await $matrixClient.getClient();
      await $matrixClient.joinRoom(roomId.value);

      client.on("Room.timeline", (event, room) => {
        if (room.roomId === roomId.value && event.getContent()) {
          const messageContent = event.getContent();
          if (messageContent["m.relates_to"]?.event_id === threadId.value) {
            const sender = event.getSender();
            const member = room.getMember(sender); // Lấy thông tin thành viên từ room
            const senderName = member ? member.name : sender; // Nếu không có name thì dùng ID

            const newMessage = {
              sender: sender,
              senderName: senderName, // Thêm tên hiển thị của người gửi
              content: messageContent.body,
              timestamp: event.getTs(),
              eventId: event.getId(),
              type: messageContent.msgtype,
              contentUrl: messageContent.url
                ? mxcUrlToHttp(messageContent.url)
                : null,
            };
            messages.value.push(newMessage);
            scrollToBottom();
          }
        }
      });

      client.once("sync", async (state) => {
        if (state === "PREPARED") {
          await handleCheckRoom();
          await loadMessages();
          scrollToBottom();
        }
      });
    } catch (error) {
      console.error("Matrix connection error:", error);
    } finally {
      isLoading.value = false;
    }
  }
  isLoading.value = false;
});
// Clean up on unmount
onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
  const client = $matrixClient.getClient();
  if (client) {
    client.removeAllListeners("Room.timeline");
  }
});
watch(
  () => route.query,
  async (newQuery) => {
    if (
      newQuery.roomId !== roomId.value ||
      newQuery.threadId !== threadId.value
    ) {
      roomId.value = newQuery.roomId;
      threadId.value = newQuery.threadId;

      if (newQuery.roomId && newQuery.threadId) {
        let loadingTimer;

        try {
          loadingTimer = setTimeout(() => {
            isLoading.value = true;
          }, 150);

          await handleCheckRoom();
          await loadMessages();
        } catch (error) {
          throw error;
        } finally {
          clearTimeout(loadingTimer);
          isLoading.value = false;
        }

        scrollToBottom();
      } else {
        messages.value = [];
        roomName.value = "";
      }
    }
  }
);
</script>
