<template>
  <div
    class="fixed inset-0 z-[50]"
    aria-labelledby="modal-title"
    aria-modal="true"
  >
    <div
      class="flex items-center justify-center h-screen p-0 text-center sm:block sm:p-0"
    >
      <div
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        aria-hidden="true"
      ></div>
      <div
        v-if="!isLoading"
        class="relative inline-block bg-white shadow w-full m-auto lg:w-[40%] h-full md:rounded-[0.5rem] md:h-full md:top-0"
      >
        <div class="h-[8svh] bg-black backdrop-blur-3xl w-full">
          <div class="flex items-center justify-between h-full px-4">
            <div>
              <button
                type="button"
                class="cursor-pointer text-white bg-transparent rounded-lg text-sm inline-flex items-center"
                @click="handleToogleModal"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2.0"
                  stroke="currentColor"
                  class="size-7 font-bold"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
                  />
                </svg>
              </button>
            </div>
            <div class="flex bg-gray-200 items-center p-1 rounded-full text-sm">
              <div
                @click="setActive('CHECK_IN')"
                :class="[
                  ' px-2 py-1 cursor-pointer rounded-full',
                  isActive === 'CHECK_IN'
                    ? 'bg-primary text-white'
                    : 'bg-gray-200',
                ]"
              >
                Vào ca
              </div>
              <div
                @click="setActive('CHECK_OUT')"
                :class="[
                  ' px-2 py-1 cursor-pointer rounded-full',
                  isActive !== 'CHECK_IN'
                    ? 'bg-primary text-white'
                    : 'bg-gray-200',
                ]"
              >
                Hết ca
              </div>
            </div>

            <div @click="handleChangeCamera">
              <span class="text-white">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                  />
                </svg>
              </span>
            </div>
          </div>
        </div>

        <div v-if="isOpen" class="h-[77svh]">
          <video ref="videoElement" playsinline autoplay></video>
        </div>
        <img
          v-if="previewImages.length"
          class="img-preview w-full"
          :src="previewImages"
          alt="image"
          loading="lazy"
        />
        <!-- icon back về -->

        <!-- các nút chụp ảnh, xóa ảnh, tạo -->
        <div class="w-full bg-black items-center backdrop-blur-3xl h-full">
          <div class="flex items-center justify-center gap-20 h-[15svh]">
            <div
              v-if="!isOpen"
              class="text-white flex flex-col justify-center items-center"
            >
              <div @click="removeImage(0)">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18 18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <div class="text-sm">Chụp lại</div>
            </div>

            <div class="bg-gray-200 items-center p-1 rounded-full">
              <div
                @click="handleTakePhoto"
                class="w-[50px] h-[50px] bg-white rounded-full"
              ></div>
            </div>
            <div
              v-if="!isOpen"
              class="text-white flex flex-col justify-center items-center"
            >
              <span @click="createCheckin">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m4.5 12.75 6 6 9-13.5"
                  />
                </svg>
              </span>
              <div class="text-sm">Chấm công</div>
            </div>
          </div>
        </div>
        <!--  -->
      </div>
      <LoadingSpinner v-if="isLoading" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useNuxtApp } from "#app";

const isClose = ref(false);
const emit = defineEmits(["isClose"]);
const previewImages = ref([]);
const dataImage = ref([]);
const isActive = ref("CHECK_IN");
const isOpen = ref(false);
const { createWorkEfforts, getListWorkEfforts } = useTimeKeeping();
const { uploadImage, AddAttachmentForWorkEffort } = useCheckin();
const isLoading = ref(false);
const auth = useCookie("auth").value;

// New state variable to manage camera mode
const facingMode = ref("environment"); // Default to rear camera

const handleToogleModal = () => {
  isClose.value = false;
  emit("isClose", isClose.value);
  closeCamera();
};

const handleImageUpload = (event) => {
  const files = event.target.files;
  Array.from(files).forEach((file) => {
    dataImage.value.push(file);
  });

  Array.from(files).forEach((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImages.value.push(e.target.result);
    };
    reader.readAsDataURL(file);
  });
};

const removeImage = (index) => {
  previewImages.value.splice(index, 1);
  handleOpenCamera();
};
const route = useRoute();
const { storeId, orgId } = useTabContext();

const attributes = {
  storeId: storeId.value,
  createdBy: auth.user.id,
};
//
const isManager = ref();
const rolesToCheck = [
  "SALE_OP",
  "SUPP_ADMIN",
  "ORG_ADMIN",
  "SALE_MANAGER",
  "SUPP_OP",
];

const checkUserRoles = () => {
  if (auth?.user?.roles && Array.isArray(auth.user.roles)) {
    auth.user.roles.forEach((role) => {
      if (rolesToCheck.includes(role)) {
        isManager.value = true;
        console.log("Đăng nhập bằng role", role);
      }
    });
  } else {
    isManager.value = false;
    console.log("user login bằng role khác");
  }
};
//

const createCheckin = async () => {
  handleUpdateImage();
  closeCamera();
};

const handleUpdateImage = async () => {
  await checkUserRoles();
  if (isManager) {
    try {
      isLoading.value = true;
      const res = await uploadImage(dataImage.value?.at(-1));
      const addAttachmentRequest = [
        {
          srcId: res?.id,
          srcName: res?.fileName,
          srcPath: res?.filePath,
        },
      ];
      await createWorkEfforts(
        auth.user.id,
        "user",
        "",
        isActive.value,
        "TIMEKEEPING",
        attributes,
        addAttachmentRequest
      );
      const attributesV2 = {
        storeId: route.query.storeId,
      };
      await getListWorkEfforts(
        auth.user.id,
        "",
        "TIMEKEEPING",
        0,
        10,
        attributesV2
      );

      isLoading.value = false;
      handleToogleModal();
      useNuxtApp().$toast.success("Chấm công hoàn tất");
    } catch (error) {
      isLoading.value = false;
      useNuxtApp().$toast.error("Thất bại");
    }
  } else {
    try {
      isLoading.value = true;
      const res = await uploadImage(dataImage.value?.at(-1));
      const addAttachmentRequest = [
        {
          srcId: res?.id,
          srcName: res?.fileName,
          srcPath: res?.filePath,
        },
      ];
      await createWorkEfforts(
        auth.user.id,
        "user",
        "",
        isActive.value,
        "TIMEKEEPING",
        attributes,
        addAttachmentRequest
      );
      await getListWorkEfforts(createdBy, "", source, 0, 10, attributes);

      isLoading.value = false;
      handleToogleModal();
      useNuxtApp().$toast.success("Chấm công hoàn tất");
    } catch (error) {
      isLoading.value = false;
      useNuxtApp().$toast.error("Thất bại");
    }
  }
};

const videoElement = ref(null);
let capturedPhoto = null;

const handleOpenCamera = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    openCamera();
  } else {
    closeCamera();
  }
};

// Function to open the camera with the current facing mode
function openCamera() {
  navigator.mediaDevices
    .getUserMedia({
      video: {
        facingMode: facingMode.value, // Use current facing mode
      },
    })
    .then((stream) => {
      if (videoElement.value) {
        videoElement.value.srcObject = stream;
      }
    })
    .catch((err) => {
      console.error("Error accessing the camera:", err);
    });
}

function closeCamera() {
  const stream = videoElement?.value?.srcObject;
  if (stream) {
    const tracks = stream.getTracks();
    tracks.forEach((track) => track.stop());
  }
}

const handleTakePhoto = async () => {
  if (!videoElement.value) return;
  const canvas = document.createElement("canvas");
  canvas.width = videoElement.value.videoWidth;
  canvas.height = videoElement.value.videoHeight;

  const ctx = canvas.getContext("2d");
  ctx.drawImage(videoElement.value, 0, 0, canvas.width, canvas.height);
  canvas.toBlob((blob) => {
    dataImage.value.push(blob);
    const res = URL.createObjectURL(blob);
    previewImages.value.push(res);
  }, "image/png");
  handleOpenCamera();
};

// Function to toggle the camera between front and rear
const handleChangeCamera = () => {
  facingMode.value =
    facingMode.value === "environment" ? "user" : "environment";
  closeCamera(); // Close the current camera stream
  openCamera(); // Reopen the camera with the new facing mode
};

onMounted(async () => {
  handleOpenCamera();
});

// Function to change the active state
function setActive(status) {
  isActive.value = status;
  console.log(isActive.value);
}
</script>

<style scoped>
.custom-file-upload {
  @apply inline-block cursor-pointer;
}

.custom-file-upload input[type="file"] {
  @apply hidden;
}

.upload-button {
  @apply bg-[#0D47A1] text-white py-2 px-4 rounded cursor-pointer;
}

video {
  width: auto;
  height: 77svh;
  object-fit: cover;
}

.img-preview {
  width: auto;
  height: 77svh;
  object-fit: cover;
}
</style>
