<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <h2 class="text-lg font-bold text-center">Tạo topic mới</h2>
      <!-- Phần nội dung -->
      <div class="mb-2" v-if="dataAppId.length">
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-80 font-semibold"
            >Ch<PERSON><PERSON> kênh trao đổi</label
          >
          <select
            id="template"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="appId"
          >
            <option
              v-for="app in dataAppId"
              :key="app?.apps[0]?.id"
              :value="app?.apps[0]?.id"
            >
              {{ app?.name }}
            </option>
          </select>
        </div>
      </div>
      <div v-else class="text-sm text-red-500 mb-2">
        <PERSON><PERSON> chức chưa cấu hình appId
      </div>
      <!-- Search  -->
      <div v-if="!customerData" class="flex items-center gap-2 relative">
        <input
          class="w-full py-1 px-2 mb-2 text-base rounded outline-none bg-secondary pr-8"
          type="text"
          placeholder="Số điện thoại"
          v-model="keyword"
          @input="searchCustomer"
        />
        <div
          v-if="listCustomer.length"
          class="absolute border top-8 w-full bg-white max-h-[80px] overflow-y-auto"
        >
          <div
            v-for="customer in listCustomer"
            class="flex items-center gap-1 cursor-pointer"
          >
            <!-- từng khách hàng -->
            <div
              @click="handleSelectedCustomer(customer)"
              class="flex items-center hover:bg-blue-50 w-full p-2"
            >
              <div class="flex items-center gap-1">
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                    />
                  </svg>
                </span>
                <span>{{ customer?.name }}</span>
              </div>
              <div class="flex items-center">
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                    />
                  </svg>
                </span>
                <span>{{ customer?.phone }}</span>
              </div>
            </div>
          </div>
        </div>
        <div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"
            />
          </svg>
        </div>
      </div>
      <div v-else class="mb-2 relative">
        <div class="flex items-center p-1 bg-blue-50">
          <div class="flex items-center gap-1">
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                />
              </svg>
            </span>
            <span>{{ customerData?.name }}</span>
          </div>
          <div class="flex items-center">
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                />
              </svg>
            </span>
            <span>{{ customerData?.phone }}</span>
          </div>
        </div>
        <div
          @click="closeCustomer"
          class="absolute top-0 right-0 text-red-500 cursor-pointer"
        >
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </span>
        </div>
      </div>
      <!-- phần chủ đề trao đổi-->
      <textarea
        rows="2"
        id="note"
        class="py-1 px-2 w-full text-base rounded outline-none border bg-secondary"
        placeholder="Nhập chủ đề ...."
        v-model="message"
      ></textarea>
      <div class="flex justify-end">
        <button
          @click="confirm"
          class="text-white bg-primary px-2 py-1 rounded"
          :disabled="!dataAppId.length || !appId"
          :class="{
            'opacity-50 cursor-not-allowed':
              !dataAppId.length || !appId || !appId,
          }"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from "vue";

const props = defineProps(["title", "message", "isHideButton"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const message = ref();
const customerData = ref();

const confirm = () => {
  emit("confirm", message.value, appId.value, customerData.value);
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const dataAppId = ref([]);
const appId = ref(null);
// search thông tin khách hàng
const { fetchListCustomer } = useCustomer();
const keyword = ref();
let debounceTimer = null;
const listCustomer = ref([]);
const searchCustomer = async () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer); // Hủy bỏ debounce trước đó nếu chưa hết thời gian
  }

  debounceTimer = setTimeout(async () => {
    const request = {
      keyword: keyword.value,
      currentPage: 1,
      pageSize: 10,
    };

    try {
      const response = await fetchListCustomer(request);
      listCustomer.value = response.content;
    } catch (error) {
      console.error(error);
    }
  }, 600);
};
const handleSelectedCustomer = async (customer) => {
  customerData.value = customer;
};
const closeCustomer = async () => {
  customerData.value = null;
  keyword.value = "";
  listCustomer.value = [];
};
onMounted(() => {
  const storedAppId = JSON.parse(localStorage.getItem("appId") || "[]");
  if (storedAppId.length > 0) {
    dataAppId.value = storedAppId;
    appId.value = dataAppId.value[0]?.apps[0]?.id;
  }
});
</script>

<style scoped></style>
