<template>
  <div class="pb-2">
    <div class="text-primary font-semibold rounded-t">
      <span class="mx-2 font-bold text-md"><PERSON><PERSON><PERSON> hàng</span>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="">Số lượng hàng mua</div>
      <div>{{ totalProductBuy }}</div>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="">Tổng tiền hàng</div>
      <div>{{ formatCurrency(totalPriceProductBuy || 0) }}</div>
    </div>
    <div class="mx-2">
      <div class="flex items-center justify-between">
        <div class="">Chiết khấu</div>
        <div class="flex items-center bg-secondary rounded px-2 py-1 w-[50%]">
          <select
            v-model="discountType"
            @change="handleDiscountTypeChange"
            class="focus:outline-none bg-secondary"
          >
            <option value="MONEY">₫</option>
            <option value="PERCENT">%</option>
          </select>
          <input
            v-model="discountValue"
            @blur="handleBlur"
            type="text"
            class="w-full focus:outline-none text-right bg-secondary"
          />
        </div>
      </div>
    </div>

    <div class="flex items-center justify-between mx-2 border-t my-2">
      <div class="font-bold">Tổng tiền mua</div>
      <div class="font-semibold text-red-600">
        {{ formatCurrency(orderDetail?.remainTotal || 0) }}
      </div>
    </div>
    <div class="flex items-center justify-between mx-2">
      <div class="font-semibold">Khách trả thêm</div>
      <div class="font-semibold">
        {{
          orderDetail?.activeOrderItemProfiles?.length > 0
            ? formatCurrency(
                orderDetail?.remainTotal -
                  (+totalPriceProductReturn - +feeReturnOrder) || 0
              )
            : formatCurrency(0)
        }}
      </div>
    </div>
    <div
      v-if="
        orderDetail?.remainTotal -
          (+totalPriceProductReturn - +feeReturnOrder) <
          0 && orderDetail.activeOrderItemProfiles?.length > 0
      "
      class="text-red-600 mx-2 mt-1"
    >
      Lưu ý: Tổng tiền đổi hàng phải lớn hơn tiền trả hàng
    </div>
  </div>
</template>
<script setup>
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const totalProductBuy = computed(() => {
  return orderStore.orderDetail?.activeOrderItemProfiles.reduce(
    (total, item) => {
      return total + (item?.orderLineItem.quantity || 0);
    },
    0
  );
});
const totalPriceProductBuy = computed(() => {
  return orderStore.orderDetail?.activeOrderItemProfiles.reduce(
    (total, item) => {
      return (
        total +
        (item?.orderLineItem.quantity || 0) *
          item?.orderLineItem?.realPriceSell?.amount
      );
    },
    0
  );
});
//
const returnStore = returnOrderStore();
const totalPriceProductReturn = computed(
  () => returnStore.totalPriceProductReturn
);
const feeReturnOrder = computed(() => returnStore.feeReturnOrder);
//
const discountType = ref("MONEY");
const discountValue = ref();
const {
  updateDiscount,
  getOrderPromotion,
  updateMemberDiscount,
  updateInfoCampaignPromotion,
} = useOrder();
const handleDiscountTypeChange = async () => {
  // Lưu giá trị hiện tại trước khi thay đổi

  // discountType.value = newType;
  discountValue.value = 0;
  const data = {
    type: discountType.value,
    amount: +discountValue.value,
  };
  await updateDiscount(orderDetail.value?.id, "", data);
  await orderStore.getOrderById(orderDetail.value?.id);
};
const handleBlur = async () => {
  if (+discountValue.value > 0) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderDisCount.value = true;
    } else {
      // discountValue.value = newVal;
      const data = {
        type: discountType.value,
        amount: +discountValue.value,
      };
      await updateDiscount(orderDetail.value?.id, "", data);
      await orderStore.getOrderById(orderDetail.value?.id);
    }
  }
};
</script>
