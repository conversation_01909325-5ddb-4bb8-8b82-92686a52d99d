<template>
  <td class="py-1 border-b border-r border-dashed">
    <input
      v-model="name"
      type="text"
      @change="handleChangeProduct"
      class="w-full outline-none px-1 truncate"
    />
    <!-- <textarea name="" id="" v-model="name" class="w-full outline-none px-1" cols="4"></textarea> -->
  </td>
  <!-- đơn vị -->
  <td class="py-1 border-b border-r border-dashed text-center">
    <input
      v-model="unit"
      type="text"
      @change="handleChangeProduct"
      class="w-full outline-none px-1 text-center"
    />
  </td>
  <!-- số lượng -->
  <td class="px-2 py-1 text-center border-b border-r border-dashed">
    <input
      v-model="quantity"
      type="text"
      @change="handleChangeProduct"
      class="outline-none p-1 w-full text-center"
    />
  </td>

  <!-- đơn giá -->
  <td class="px-2 py-1 text-center border-b border-r border-dashed">
    <div class="flex items-center justify-center">
      <CurrencyInput
        class="w-full outline-none p-1 rounded text-center"
        v-model="price"
        :options="{
          currency: 'VND',
          currencyDisplay: 'hidden',
          hideCurrencySymbolOnFocus: false,
          hideGroupingSeparatorOnFocus: false,
          hideNegligibleDecimalDigitsOnFocus: false,
        }"
        @change="handleChangeProduct"
      />
    </div>
  </td>
  <!-- VAT -->
  <td class="text-center border-b border-r border-dashed py-1">
    <!-- <input
      v-model="vat"
      type="text"
      @change="handleChangeProduct"
      class="outline-none p-1 w-full text-center"
    /> -->
    <select
      v-model="vat"
      class="outline-none p-1 w-full"
      @change="handleChangeProduct"
    >
      <option v-for="vat in dataLisTVat" :key="vat.id" :value="vat.id">
        {{ vat?.id }}
      </option>
    </select>
  </td>
  <!-- Thành tiền -->
  <td
    class="px-2 py-1 text-center border-b border-r border-dashed select-none pointer-events-none cursor-none"
  >
    <CurrencyInput
      class="w-full outline-none p-1 text-center"
      :key="count"
      v-model="totalPrice"
      :options="{
        currency: 'VND',
        currencyDisplay: 'hidden',
        hideCurrencySymbolOnFocus: false,
        hideGroupingSeparatorOnFocus: false,
        hideNegligibleDecimalDigitsOnFocus: false,
      }"
    />
  </td>
</template>

<script setup lang="ts">
const props = defineProps(["product", "dataItemInvoice"]);
const emit = defineEmits(["updateInvoice"]);
const name = ref();
const unit = ref();
const quantity = ref("");
const price = ref();
const totalPrice = ref();
const vat = ref();
onMounted(() => {
  data.id = props.product?.id;
  name.value = props.product?.productName;
  unit.value = props.product?.unitType || "";
  quantity.value = props.product?.quantity;
  price.value = props.product?.amount;
  totalPrice.value =
    props.product?.totalAmount +
    (props.product?.totalAmount * (props.product?.vatRate || 0)) / 100;
  vat.value = props.product?.vatRate || 0;
});
const data = reactive({
  id: "",
  productName: name.value,
  unitType: unit.value,
  quantity: quantity.value,
  amount: price.value,
  vatRate: vat.value,
});
const count = ref();
const handleChangeProduct = () => {
  data.productName = String(name.value);
  data.unitType = String(unit.value);
  data.quantity = String(quantity.value);
  data.amount = String(price.value);
  const resVat = dataLisTVat.value.find(
    (item: any) => String(item.id) === String(vat.value)
  );

  data.vatRate = String(resVat?.name);
  if (vat.value === 0 || vat.value === "KCT") {
    totalPrice.value =
      price.value * props.product?.quantity +
      (price.value * props.product?.quantity * 0) / 100;
    count.value++;
  } else {
    totalPrice.value =
      price.value * +quantity.value +
      (price.value * +quantity.value * (+vat.value || 0)) / 100;
    count.value++;
  }
  emit("updateInvoice", data);
};
const dataLisTVat = ref([
  { id: 0, name: "ZERO_PERCENT" },
  { id: 5, name: "FIVE_PERCENT" },
  { id: 8, name: "EIGHT_PERCENT" },
  { id: 10, name: "TEN_PERCENT" },
  { id: "KCT", name: "KCT" },
]);
</script>
