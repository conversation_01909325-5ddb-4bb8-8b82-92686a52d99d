<template>
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 w-full h-screen"
  >
    <div
      class="bg-white w-full max-w-lg shadow-xl transform transition-all duration-300 ease-in-out h-screen"
    >
      <div
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        aria-hidden="true"
      ></div>
      <span
        class="hidden sm:inline-block sm:align-middle sm:h-screen"
        aria-hidden="true"
        >&#8203;</span
      >
      <div
        class="relative inline-block bg-white shadow w-full m-auto lg:w-[40%] h-full md:rounded-[0.5rem] md:h-full md:top-0"
      >
        <div
          class="flex justify-between items-center w-full p-2 rounded-t border-b bg-primary-light"
        >
          <div class="flex gap-1 items-center justify-between w-full">
            <div class="w-[32px] h-[32px]"></div>
            <div class="text-base text-white lg:text-lg">Tạo checkin</div>
            <button
              type="button"
              class="cursor-pointer text-gray-400 bg-transparent rounded-lg text-sm inline-flex items-center"
              @click="handleToogleModal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 16 16"
              >
                <g fill="white">
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
                  />
                  <path
                    d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
        <div class="text-left pt-6 pb-[3.25rem] h-full overflow-auto md:h-full">
          <div
            class="flex flex-col w-full justify-between"
            :class="previewImages.length > 0 ? 'lg:h-full h-auto' : 'h-full'"
          >
            <div class="mx-3 flex flex-col gap-2 mb-2 h-full md:h-[80%]">
              <SearchCompany @company="company"></SearchCompany>

              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold"
                  >Tên cửa hàng
                  <span class="text-red-500 text-xs">*</span></span
                >
                <input
                  class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
                  type="text"
                  placeholder="Tên cửa hàng"
                  v-model="nameStore"
                />
              </div>
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold">Vị trí Checkin </span>
                <input
                  class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
                  type="text"
                  placeholder="Vị trí Checkin"
                  v-model="location"
                />
              </div>
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold">Mô tả</span>
                <div>
                  <div class="flex flex-col m-0 md:p-4 bg-white rounded">
                    <div class="grid grid-cols-1 gap-2">
                      <div class="flex justify-between gap-2">
                        <textarea
                          rows="8"
                          id="note"
                          v-model="description"
                          class="py-1 px-2 w-full h-[100px] text-base rounded outline-none border bg-secondary"
                          type="text"
                          placeholder="Mô tả"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Image upload section -->
              <div class="flex flex-col">
                <span class="mb-4 text-sm font-bold">Upload Images</span>
                <label class="custom-file-upload">
                  <input
                    type="file"
                    @change="handleImageUpload"
                    accept="image/*"
                    multiple
                    class="hidden"
                  />
                  <span
                    class="bg-primary-light text-white py-2 px-4 rounded cursor-pointer"
                    >Choose Files</span
                  >
                </label>
                <div
                  v-if="previewImages.length"
                  class="mt-2 grid grid-cols-3 gap-2"
                >
                  <div
                    v-for="(image, index) in previewImages"
                    :key="index"
                    class="relative border border-primary rounded-md"
                  >
                    <img
                      :src="image"
                      alt="Image Preview"
                      class="w-[80px] h-[80px] object-contain mx-auto"
                      loading="lazy"
                    />
                    <span
                      @click="removeImage(index)"
                      class="absolute top-0 right-0 p-1 text-red-700 cursor-pointer"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="md:h-full flex flex-col justify-end">
              <div
                class="flex text-black gap-4 flex-col w-full justify-between"
              ></div>
              <div class="flex justify-center py-[10px]">
                <div
                  @click="createCheckin"
                  class="bg-primary-light w-[70%] text-center text-white py-1 rounded-md font-semibold border-2 border-[#3F51B5] cursor-pointer"
                >
                  Tạo mới
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
const isClose = ref(false);
const emit = defineEmits(["isClose"]);
const nameStore = ref("");
const location = ref("");
const dataCompany = ref("");
const previewImages = ref([]);
const dataImage = ref([]);
const description = ref("");
const dataResponse = ref([]);
const {
  addOpportunity,
  uploadImage,
  AddAttachmentForWorkEffort,
  getListTodos,
  getListWorkEffortTypes,
  getListTodoByid,
  getListOpportunitys,
  handleImage,
  UpdateWorkEffortDescriptions,
  createCompany,
  getListWorkEfforts,
} = useCheckin();
const auth = useCookie("auth").value;
const checkinStore = useCheckinStore();
const handleToogleModal = () => {
  isClose.value = false;
  emit("isClose", isClose.value);
};
const handleContentUpdate = (content) => {};
const company = (company) => {
  dataCompany.value = company;
};

const handleImageUpload = (event) => {
  const files = event.target.files;
  Array.from(files).forEach((file) => {
    dataImage.value.push(file);
  });
  Array.from(files).forEach((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImages.value.push(e.target.result);
    };
    reader.readAsDataURL(file);
  });
};

const removeImage = (index) => {
  previewImages.value.splice(index, 1);
};
const handleLocationId = (value) => {
  const item = value.find((item) => item.workEffortTypeId === "TAKE_ADDRESS");
  return item?.id;
};
const handleImageId = (value) => {
  const item = value.find((item) => item.workEffortTypeId === "TAKE_PHOTO");
  return item?.id;
};
const createCheckin = async () => {
  if (dataCompany.value.id) {
    const datarequest = {
      name: nameStore.value,
      description: description.value,
      parentId: null,
      priorityName: "MEDIUM",
      referName: "",
      referPhone: dataCompany.value?.phone,
      referEmail: "",
      targetId: "",
      extSource: "pos",
      ownerId: dataCompany.value.id,
      workEffortTypeId: "SALE_POINT_MARKETING",
      forms: [
        {
          key: "123",
          value: "123",
        },
      ],
    };
    const res = await addOpportunity(auth.user.id, datarequest);

    await checkinStore.handleUpdateProcessStatus(res.id, "DEAL", "");

    const resTodo = await getListTodoByid(res.id);
    const locationId = handleLocationId(resTodo);
    const imageId = handleImageId(resTodo);
    // update vị trí và ảnh
    await Promise.all([
      UpdateWorkEffortDescriptions(auth.user.id, locationId, location.value),
      handleUpdateImage(imageId, "create"),
    ]);

    await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);

    handleToogleModal();
    // emit("isClose", isClose.value);
  } else {
    const data = {
      name: nameStore.value,
      phone: dataCompany.value,
      address: location.value,
      email: "",
    };
    const response = await createCompany(data, auth.user.id);
    const datarequest = {
      name: nameStore.value,
      description: description.value,
      parentId: null,
      priorityName: "MEDIUM",
      referName: "",
      referPhone: response.phone,
      referEmail: "",
      targetId: "",
      extSource: "pos",
      ownerId: response.id,
      workEffortTypeId: "SALE_POINT_MARKETING",
    };
    const res = await addOpportunity(datarequest, auth.user.id);
    const resTodo = await getListTodoByid(res.id);
    const locationId = handleLocationId(resTodo);
    const imageId = handleImageId(resTodo);
    // update vị trí và ảnh
    await Promise.all([
      UpdateWorkEffortDescriptions(auth.user.id, locationId, location.value),
      handleUpdateImage(imageId, "create"),
    ]);

    handleToogleModal();
  }
};
const handleUpdateImage = async (id, type) => {
  dataImage.value.forEach(async (image) => {
    const res = await uploadImage(image);
    const validImgArr = [
      {
        // fileType: res?.fileType,
        // name: res?.fileName?.split("/")[0] + "-" + res?.fileName,
        // srcConfigPathId: res?.configPathID,
        srcId: res?.id,
        srcName: res?.fileName,
        srcPath: res?.filePath,
        // type: "RESULT",
      },
    ];
    await handleAddAttachmentForWorkEffort(validImgArr, id, type);
  });
};
const handleAddAttachmentForWorkEffort = async (arr, id, typeFuntion) => {
  try {
    if (arr.length > 0) {
      await AddAttachmentForWorkEffort(auth.user.id, id, arr);
    }
  } catch (error) {
    throw error;
  }
};
</script>

<style scoped>
.custom-file-upload {
  @apply inline-block cursor-pointer;
}

.custom-file-upload input[type="file"] {
  @apply hidden;
}

.upload-button {
  @apply bg-blue-500 text-white py-2 px-4 rounded cursor-pointer;
}
</style>
