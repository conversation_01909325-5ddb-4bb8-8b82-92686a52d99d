import { defineStore } from "pinia";
import { ref } from "vue";

export const useTimeKeepingStore = defineStore("timeKeeping", () => {
  const dataTimeKeeping = ref<any>([]);
  const timeKeeping = ref<any>();
  const setDataTimeKeeping = (newData: any[]) => {
    dataTimeKeeping.value = newData;
  };
  const setTimeKeeping = (data: any) => {
    timeKeeping.value = data;
  };
  const addTimeKeping = async (data: any) => {
    if (!Array.isArray(dataTimeKeeping.value)) {
      dataTimeKeeping.value = [];
    }
    dataTimeKeeping.value = [...dataTimeKeeping.value, data];
  };
  return {
    dataTimeKeeping,
    setDataTimeKeeping,
    addTimeKeping,
    setTimeKeeping,
    timeKeeping,
  };
});
