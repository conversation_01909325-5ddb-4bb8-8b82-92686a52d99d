<template>
  <div class="mx-auto max-w-md p-1">
    <div class="mb-1 rounded-lg bg-white p-2">
      <!-- v-if voucher?.numberOfTimeUsed === voucher?.usageLimitPerVoucher-->
      <div id="voucher-list">
        <div
          @click="ToogleAddVoucher"
          class="voucher-item relative overflow-hidden rounded-lg border"
          :class="[
            voucher.voucherType === 'PERCENT'
              ? 'hover:border-orange-300'
              : 'hover:border-blue-400',
            (voucher.numberOfTimeUsed >= voucher.usageLimitPerVoucher &&
              !isCheckBox) ||
            !orderStore.orderDetail
              ? 'bg-gray-100 border-gray-300 cursor-not-allowed opacity-50'
              : 'cursor-pointer',
          ]"
        >
          <div class="flex">
            <div
              class="relative flex w-1/4 flex-col items-center justify-center py-4 text-white"
              :class="
                voucher.voucherType === 'PERCENT'
                  ? 'bg-orange-400'
                  : ' bg-blue-500'
              "
            >
              <svg
                v-if="voucher.voucherType === 'PERCENT'"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6 w-8 h-8"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m8.99 14.993 6-6m6 3.001c0 1.268-.63 2.39-1.593 3.069a3.746 3.746 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043 3.745 3.745 0 0 1-3.068 1.593c-1.268 0-2.39-.63-3.068-1.593a3.745 3.745 0 0 1-3.296-1.043 3.746 3.746 0 0 1-1.043-3.297 3.746 3.746 0 0 1-1.593-3.068c0-1.268.63-2.39 1.593-3.068a3.746 3.746 0 0 1 1.043-3.297 3.745 3.745 0 0 1 3.296-1.042 3.745 3.745 0 0 1 3.068-1.594c1.268 0 2.39.63 3.068 1.593a3.745 3.745 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.297 3.746 3.746 0 0 1 1.593 3.068ZM9.74 9.743h.008v.007H9.74v-.007Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm4.125 4.5h.008v.008h-.008v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                />
              </svg>
              <svg
                v-if="voucher.voucherType === 'MONEY'"
                class="mb-1 h-8 w-8"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div class="text-lg font-bold">
                {{
                  voucher.voucherType === "PERCENT"
                    ? `${voucher?.discountPercent}%`
                    : `${formatCurrency(voucher?.discountAmount)}`
                }}
              </div>
              <div class="text-xs">GIẢM</div>
              <div
                class="absolute top-0 right-0 flex h-full w-2 flex-col justify-between"
              >
                <div class="flex h-full flex-col justify-between py-1 mx-1">
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                  <div class="h-2 w-2 rounded-full bg-gray-100"></div>
                </div>
              </div>
            </div>
            <div class="w-3/4 p-3">
              <div
                class="font-bold flex gap-1"
                :class="
                  voucher.voucherType === 'PERCENT'
                    ? 'text-orange-400'
                    : ' text-blue-500'
                "
              >
                {{
                  voucher.voucherType === "PERCENT"
                    ? `Giảm ${voucher?.discountPercent}% `
                    : `Giảm ${formatCurrency(voucher?.discountAmount)} `
                }}

                <div v-if="voucher?.maximumDiscount">
                  tối đa
                  <span>{{ formatCurrency(voucher?.maximumDiscount) }}</span>
                </div>
              </div>
              <div class="mt-1 text-xs text-gray-500">
                {{
                  ` Hạn sử dụng: ${formatTimestampV2(campaignAction?.toDate)}`
                }}
              </div>
              <div class="mt-1 text-xs text-gray-500">
                {{
                  ` Sử dụng: ${voucher?.numberOfTimeUsed || 0}/${
                    voucher?.usageLimitPerVoucher
                  }`
                }}
              </div>
              <div class="mt-2 flex items-center justify-between">
                <span
                  class="rounded px-2 py-1 text-xs"
                  :class="
                    voucher.voucherType === 'PERCENT'
                      ? 'text-orange-500 bg-orange-100'
                      : ' text-blue-500 bg-blue-100'
                  "
                  >{{ ` Mã: ${voucher?.voucherCode}` }}</span
                >
              </div>
            </div>
          </div>
          <!-- btn check box -->
          <div class="absolute top-1/2 right-2 transform -translate-y-1/2">
            <label class="inline-flex items-center cursor-pointer">
              <input
                v-model="isCheckBox"
                type="checkbox"
                class="sr-only peer"
                disabled
              />
              <div
                class="w-6 h-6 rounded-full border-2 relative flex items-center justify-center transition"
                :class="
                  voucher.voucherType === 'PERCENT'
                    ? 'peer-checked:bg-orange-500 peer-checked:border-orange-500'
                    : ' peer-checked:bg-blue-500 peer-checked:border-bluebg-blue-500'
                "
              >
                <svg
                  class="h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </label>
          </div>
          <!--  -->
        </div>
      </div>
    </div>
  </div>
  <!-- Spinner và dialog xử lý -->
  <ConfirmDialog
    v-if="isOpenPopup"
    :title="'Thông báo'"
    :message="'Voucher cần gán khách hàng trước khi áp dụng'"
    @confirm="handleAddCustomerToVoucher"
    @cancel="toogleOpenPopup"
  />

  <LoadingSpinner v-if="isLoading" class="mt-2" />
</template>

<script setup lang="ts">
const props = defineProps({
  voucher: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  order: {
    type: Object,
    required: false,
  },
  customer: {
    type: Object,
    required: false,
  },
  campaignAction: {
    type: Object,
    required: false,
  },
});
const emits = defineEmits(["toggle", "updateScope"]);

const orderStore = useOrderStore();
const { addVoucher, removeVoucher } = useOrder();
const { addCustomerToVoucher } = useCampaign();
const isLoading = ref(false);
const isCheckBox = ref(false);
const isUpdate = ref(false);
watch(isCheckBox, async (newVal) => {
  if (newVal) {
    if (isUpdate.value) {
      isUpdate.value = false;
      return;
    }

    const response = await addVoucher(
      orderStore.orderDetail.id,
      props.voucher.voucherCode
    );

    if (response?.status === 1) {
      await orderStore.updateOrder(orderStore.orderDetail.id);
      isUpdate.value = false;
    } else {
      isUpdate.value = true;
      isCheckBox.value = false;
    }
    if (props.voucher.scope === "config") {
      toogleOpenPopup();
    }
    emits("updateScope");
  } else {
    if (isUpdate.value) {
      isUpdate.value = false;
      return;
    }
    const response = await removeVoucher(
      orderStore.orderDetail.id,
      props.voucher.voucherCode
    );

    if (response?.status === 1) {
      await orderStore.updateOrder(orderStore.orderDetail.id);
      isUpdate.value = false;
    } else {
      isUpdate.value = true;
      isCheckBox.value = true;
    }
    if (props.voucher.scope === "config") {
      toogleOpenPopup();
    }
    emits("updateScope");
  }
});
onMounted(() => {
  if (props.order?.order?.discountApplications) {
    const isExist = props.order?.order?.discountApplications?.find(
      (voucher: any) => voucher.voucherCode === props.voucher?.voucherCode
    );
    if (isExist) {
      isUpdate.value = true;
      isCheckBox.value = true;
    }
  }
});
const isOpenPopup = ref();
const ToogleAddVoucher = () => {
  if (
    props.order?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  const isDisabled =
    (props.voucher.numberOfTimeUsed >= props.voucher.usageLimitPerVoucher &&
      !isCheckBox.value) ||
    !orderStore.orderDetail;
  if (props.voucher.scope === "config") {
    toogleOpenPopup();
    return;
  }
  if (isDisabled) return;
  isCheckBox.value = !isCheckBox.value;
};
const auth = useCookie("auth") as any;
const handleAddCustomerToVoucher = async () => {
  if (!props.customer?.id) {
    useNuxtApp().$toast.warning("Vui lòng thêm khách hàng vào đơn");
    return;
  }
  await addCustomerToVoucher(
    props.voucher?.voucherCode,
    props.customer?.id,
    auth.value?.user?.id
  );
  isCheckBox.value = !isCheckBox.value;
};
const toogleOpenPopup = () => {
  isOpenPopup.value = !isOpenPopup.value;
};
</script>
