<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <h2 class="text-lg font-bold mb-2 text-center">{{ title }}</h2>
      <p class="mb-6">{{ message }}</p>
      <div class="flex justify-end space-x-4">
        <button
          v-if="!isHideButton"
          @click="cancel"
          class="px-2 py-1 bg-gray-300 rounded"
        >
          Đóng
        </button>
        <button
          @click="confirm"
          class="px-2 py-1 bg-primary text-white rounded"
        >
          <PERSON><PERSON>ng ý
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["title", "message", "isHideButton"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);

const confirm = () => {
  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
</script>

<style scoped></style>
