<template>
  <div class="">
    <div class="flex items-center mb-4 flex-wrap gap-1">
      <flatpickr
        v-model="dateRange"
        :config="datePickerConfig"
        class="w-[250px] text-center focus:outline-none bg-[#F5F5F5] rounded max-h-[34px] py-[7px] mb-1"
        placeholder="Chọn khoảng thời gian"
      />
      <InputField
        type="select"
        :defaultText="'Nhân viên'"
        :options="dataSaleEmployee?.reportDTOS"
        v-model="selectedIdEmployee"
      />
      <button
        class="bg-primary text-white px-4 py-[5px] rounded"
        @click="search"
      >
        Thống kê
      </button>
    </div>
    <div class="flex flex-wrap">
      <div v-for="(item, index) in timePeriods" :key="index">
        <button
          :class="{
            'bg-primary text-white': selectedItem === index,
            'text-primary': selectedItem !== index,
          }"
          class="text-blue-600 px-4 py-1 rounded-md text-nowrap"
          @click="handleClick(index)"
        >
          {{ item.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import flatpickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
const emit = defineEmits([
  "update-dates",
  "update-employee",
  "update-valueDate",
  "search",
]);
const isOptions = ref(true);
const selectedIdEmployee = ref("");
const route = useRoute();
const { dataSaleEmployee } = defineProps(["dataSaleEmployee"]);

const formatDate = (date: Date) => {
  return date.toISOString().split("T")[0];
};

const selectedItem = ref(0);
const dateFrom = ref(formatDate(new Date()));
const dateTo = ref(formatDate(new Date()));
const dateRange = ref([new Date(dateFrom.value), new Date(dateTo.value)]);
const valueDate = ref(0);
const calculateDateRanges = () => {
  const today = new Date();

  const todayFormatted = formatDate(today);

  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + 1);
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);

  const lastWeekStart = new Date(startOfWeek);
  lastWeekStart.setDate(startOfWeek.getDate() - 7);
  const lastWeekEnd = new Date(lastWeekStart);
  lastWeekEnd.setDate(lastWeekStart.getDate() + 6);

  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 2);
  const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1);

  const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 2);
  const lastMonthEnd = new Date(today.getFullYear(), today.getMonth());

  const startOfYear = new Date(today.getFullYear(), 0, 2);
  const endOfYear = new Date(today.getFullYear(), 11, 32);

  return [
    { name: "Hôm nay", range: [todayFormatted, todayFormatted] },
    {
      name: "Tuần trước",
      range: [formatDate(lastWeekStart), formatDate(lastWeekEnd)],
    },
    {
      name: "Tuần này",
      range: [formatDate(startOfWeek), formatDate(endOfWeek)],
    },
    {
      name: "Tháng trước",
      range: [formatDate(lastMonthStart), formatDate(lastMonthEnd)],
    },
    {
      name: "Tháng này",
      range: [formatDate(startOfMonth), formatDate(endOfMonth)],
    },
    {
      name: "Năm nay",
      range: [formatDate(startOfYear), formatDate(endOfYear)],
    },
  ];
};

const timePeriods = ref(calculateDateRanges());

const handleClick = (index: number) => {
  selectedItem.value = index;
  const selectedPeriod = timePeriods.value[index];
  dateFrom.value = selectedPeriod.range[0];
  dateTo.value = selectedPeriod.range[1];
  dateRange.value = [new Date(dateFrom.value), new Date(dateTo.value)];
  valueDate.value = index;
  emit("update-valueDate", { value: valueDate.value });

  console.log("da click", index);
};

if (route.params.reportItem === "overview") {
  isOptions.value = false;
} else if (route.params.reportItem === "employees") {
  // Handle specific route condition
}
const datePickerConfig = {
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: async (selectedDates: any) => {
    if (selectedDates.length === 2) {
      dateFrom.value = formatDate(selectedDates[0]);
      dateTo.value = formatDate(selectedDates[1]);
      emit("update-dates", {
        from: selectedDates[0].getTime(),
        to: selectedDates[1].getTime(),
      });
    }
  },
};
watch(selectedIdEmployee, () => {
  emit("update-employee", { id: selectedIdEmployee.value });
});
const search = () => {
  emit("search");
};
</script>
<style scoped></style>
