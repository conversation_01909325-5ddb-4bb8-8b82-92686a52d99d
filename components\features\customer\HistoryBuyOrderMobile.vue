<template>
  <div
    class="mx-2 border rounded-lg max-h-[500px] overflow-y-auto bg-white pt-2"
  >
    <span class="text-lg font-semibold text-primary p-2">L<PERSON>ch sử mua hàng</span>
    <div v-if="listOrder?.length > 0" v-for="order in listOrder">
      <QuickOrder :dataOrder="order" :isManagerCustomer="true"></QuickOrder>
    </div>
    <div v-else class="flex items-center justify-center py-1 mx-8 my-4 text-sm">
      Chưa có lịch sử mua hàng
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  listOrder: Array<Record<string, any>>;
}>();
</script>
