<template>
  <div>
    <div class="px-2 py-1 bg-white rounded text-sm">
      <span class="text-sm font-semibold text-primary"><PERSON><PERSON><PERSON><PERSON> h<PERSON>ng</span>
      <div class="p-3 mt-1 border flex flex-col relative bg-primary/10">
        <div
          class="flex justify-end cursor-pointer absolute top-[5px] right-[5px]"
        >
          <svg
            class="w-[20px]"
            fill="#E91E63"
            xmlns="http://www.w3.org/2000/svg"
            height="1em"
            viewBox="0 0 512 512"
            @click="clearCustomer()"
          >
            <path
              d=" M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1
                        0 0 512zM175 175c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9
                        0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6
                        0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0z"
            ></path>
          </svg>
        </div>
        <div class="absolute right-2 bottom-2 flex items-center">
          <div class="text-red-600"></div>
        </div>
        <div class="text-sm space-y-1.5">
          <div class="flex items-center gap-2">
            <div class="space-y-1.5">
              <div class="flex gap-2 items-center">
                <span
                  ><svg
                    class="w-[20px]"
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="currentColor"
                      d="M10 14q-1.25 0-2.125-.875T7 11q0-.55.175-1.037t.525-.888q-.1-.25-.15-.525T7.5 8q0-.95.513-1.687T9.35 5.225q.5-.575 1.175-.9T12 4q.8 0 1.475.325t1.175.9q.825.35 1.338 1.088T16.5 8q0 .275-.05.55t-.15.525q.35.4.525.888T17 11q0 1.25-.875 2.125T14 14zm-6 8v-2.8q0-.85.438-1.562T5.6 16.55q1.55-.775 3.15-1.162T12 15q1.65 0 3.25.388t3.15 1.162q.725.375 1.163 1.088T20 19.2V22z"
                    ></path>
                  </svg>
                </span>
                <div>
                  <span class="capitalize">{{ customer.name }}</span>
                </div>
                <div>
                  | <span class="">{{ customer.phone }}</span>
                </div>
              </div>
            </div>
            <div class="font-bold text-[#3F51B5] flex gap-1 items-start">
              <span
                class="border-b border-b-[#3F51B5] cursor-pointer pb-1.2"
                @click="toggleModalEditCustomer()"
              >
                <svg
                  xmlns=" http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="M5 19h1.425L16.2 9.225L14.775 7.8L5 17.575zm-2 2v-4.25L16.2 3.575q.3-.275.663-.425t.762-.15q.4 0 .775.15t.65.45L20.425 5q.3.275.438.65T21 6.4q0 .4-.137.763t-.438.662L7.25 21zM19 6.4L17.6 5zm-3.525 2.125l-.7-.725L16.2 9.225z"
                  ></path>
                </svg>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="isModalEditCustomer" class="">
    <ModalEditCustomer
      @closeModalEditCustomer="closeModalEditCustomer"
      @updateUserData="updateUserData"
      :customer="customer"
    />
  </div>
  <div v-if="isModalManageAddressShip" class="">
    <ModalManageAddressShip
      @closeModalManageAddressShip="handlePopup"
      :customer="customer"
    ></ModalManageAddressShip>
  </div>
</template>

<script setup>
// Lazy load heavy components
const ModalEditCustomer = defineAsyncComponent(() =>
  import("~/components/ui/feedback/ModalEditCustomer.vue")
);
const ModalManageAddressShip = defineAsyncComponent(() =>
  import("~/components/ui/feedback/ModalManageAddressShip.vue")
);
const isModalEditCustomer = ref(false);
const isModalManageAddressShip = ref(false);
const orderStore = useOrderStore();
const isNotDraft = computed(() => orderStore.isNotDraft);
const customer = computed(() => orderStore.customerInOrder);
const orderDetail = computed(() => orderStore.orderDetail);
const emits = defineEmits("setValueShippingAddress");
const clearCustomer = () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  const value = "at-counter";
  emits("setValueShippingAddress", value);
  orderStore.clearCustomer();
};
const toggleModalManageAddressShip = () => {
  isModalManageAddressShip.value = true;
};
const handlePopup = (value) => {
  isModalManageAddressShip.value = value;
};
const toggleModalEditCustomer = () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  isModalEditCustomer.value = true;
};
const closeModalEditCustomer = (value) => {
  isModalEditCustomer.value = false;
};
</script>

<style scoped></style>
