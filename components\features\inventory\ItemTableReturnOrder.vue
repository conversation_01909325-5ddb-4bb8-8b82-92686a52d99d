<template>
  <!-- x -->
  <td class="text-center">
    <input
      type="checkbox"
      id="choose-me"
      v-model="isChecked"
      class="w-[14px] h-[14px] accent-primary rounded-full cursor-pointer"
    />
  </td>
  <!-- hình ảnh -->
  <td class="text-center" :class="{ 'opacity-50': !isChecked }">
    <div class="flex items-center justify-center gap-1">
      <NuxtImg
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="w-8 h-8 md:w-12 md:h-12 rounded-lg"
        loading="lazy"
        preload
      />
    </div>
  </td>
  <!-- Tên sản phẩm -->
  <td
    class="p-2 max-w-[50px] truncate text-sm"
    :class="{ 'opacity-50': !isChecked }"
  >
    {{ product?.orderLineItem?.orderItemName }}
  </td>
  <!-- Đ<PERSON>n giá -->
  <td
    class="text-left text-primary font-semibold"
    :class="{ 'opacity-50': !isChecked }"
  >
    {{ formatCurrency(product?.orderLineItem?.realPriceSell?.amount) }}
  </td>

  <!-- Số lượng sản phẩm -->
  <td class="p-2" :class="{ 'opacity-50': !isChecked }">
    <div class="flex items-center space-x-2">
      <button
        @click="handleDecreaseQuantity"
        class="border px-2 rounded cursor-pointer"
        :disabled="!isChecked"
      >
        -
      </button>
      <span>{{ product?.orderLineItem?.currentQuantity }}</span>
      <button
        @click="handleIncreaseQuantity"
        class="border px-2 rounded cursor-pointer"
        :disabled="!isChecked"
      >
        +
      </button>
      <div>{{ `/ ${product?.orderLineItem?.quantity} ` }}</div>
    </div>
  </td>
  <!-- Thành tiền -->
  <td
    class="p-4 text-center text-primary cursor-pointer font-semibold"
    :class="{ 'opacity-50': !isChecked }"
  >
    <div :class="'text-primary '">
      {{
        formatCurrency(
          product?.orderLineItem?.realPriceSell?.amount *
            product?.orderLineItem?.currentQuantity
        )
      }}
    </div>
  </td>
</template>
<script setup lang="ts">
const props = defineProps(["product"]);
const isChecked = ref<Boolean>(false);
const returnStore = returnOrderStore();

const handleIncreaseQuantity = async () => {
  returnStore.handleIncreaseQuantity(props.product);
};
const handleDecreaseQuantity = async () => {
  returnStore.handleDecreaseQuantity(props.product);
};
const image = ref();
const { getImageProducrUrl } = usePortal();

watch(
  () => isChecked.value,
  async (newVal, oldVal) => {
    if (newVal) {
      returnStore.addProductToOrderChooseReturn(props.product);
    } else {
      returnStore.removeProductFromOrderChooseReturn(props.product);
    }
  }
);

watch(
  () => props.product,
  async (newId) => {
    if (newId) {
      try {
        image.value = await getImageProducrUrl(
          newId?.orderLineItem?.variant?.id,
          "PRODUCT"
        );
      } catch (error) {
        throw error;
      }
    }
  },
  { immediate: true }
);
// onMounted(async () => {
//   if (props.product) {
//     image.value = await getImageProduct(props.product?.variant?.id);
//   }
// });
</script>
