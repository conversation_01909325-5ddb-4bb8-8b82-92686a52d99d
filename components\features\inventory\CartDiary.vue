<template>
  <div class="p-2 md:p-4 md:h-full-custom2">
    <div class="w-full rounded-md" v-if="products?.length">
      <CartItemDiary
        v-for="(item, index) in products"
        :item="item"
        :key="item.id"
      ></CartItemDiary>
      {{ item }}
    </div>
    <div v-else class="lg:h-full overflow-auto">
      <div class="my-3">
        <div class="flex items-center justify-center">
          <img src="@/assets/images/cartEmpty.svg" class="w-32" loading="lazy"/>
        </div>
        <div class="mt-2 text-sm font-semibold text-center text-textBlack">
          Bạn chưa có sản phẩm nào trong giỏ hàng!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { products } = defineProps({
  products: Array,
});
</script>

<style scoped>
.h-full-custom2 {
  height: calc(100vh - 20rem);
}
</style>
