<template>
  <div class="space-y-2">
    <!-- Mobile Header -->
    <div class="block md:hidden">
      <div class="flex items-center justify-between mb-3">
        <h2 class="text-base font-bold text-gray-900"><PERSON><PERSON> toán tiền mặt</h2>
        <button
          @click="handleClickBack"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-5 h-5 text-gray-600"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Payment Summary -->
    <div
      class="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-3 border border-primary/20"
    >
      <div class="flex items-center justify-between">
        <div>
          <p class="text-xs font-medium text-gray-600 mb-1">
            T<PERSON>ng cần thanh toán
          </p>
          <p class="text-xl font-bold text-primary">
            {{
              paymentAmount > 0
                ? formatCurrency(paymentAmount)
                : formatCurrency(orderDetails?.data?.remainTotal)
            }}
          </p>
        </div>
      </div>
    </div>

    <!-- Amount Input -->
    <div class="space-y-3">
      <div>
        <label class="block text-xs font-medium text-gray-700 mb-1">
          Số tiền khách đưa
        </label>
        <input
          type="text"
          inputmode="numeric"
          placeholder="Nhập số tiền nhận từ khách"
          class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-base font-semibold"
          v-model="formattedReceived"
          @input="updateReceived(formattedReceived)"
        />
      </div>

      <!-- Quick Amount Buttons -->
      <div>
        <p class="text-xs font-medium text-gray-700 mb-2">Số tiền gợi ý:</p>
        <div class="grid grid-cols-3 gap-1">
          <button
            v-for="suggestedAmount in suggest"
            :key="suggestedAmount"
            @click="handleRecommend(suggestedAmount)"
            class="bg-gray-100 hover:bg-primary hover:text-white border border-gray-200 hover:border-primary px-2 py-1 rounded text-xs font-medium transition-all duration-200"
          >
            {{ formatCurrency(suggestedAmount) }}
          </button>
        </div>
      </div>
    </div>

    <!-- Change Amount -->
    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-xs font-medium text-green-700 mb-1">
            Tiền thừa trả khách
          </p>
          <p class="text-xl font-bold text-green-600">
            {{ formatCurrency(changeAmount) }}
          </p>
        </div>
        <div class="text-green-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-6 h-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
      </div>
    </div>

    <!-- Payment Button -->
    <div class="flex justify-end">
      <button
        @click="handlePaymentManual"
        :disabled="
          isLoading ||
          received <
            (paymentAmount > 0
              ? paymentAmount
              : orderDetails?.data?.remainTotal)
        "
        class="bg-primary mt-2 hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg font-bold text-sm transition-all duration-200 shadow-lg hover:shadow-xl flex items-center gap-2"
      >
        <div
          v-if="isLoading"
          class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
        ></div>
        <svg
          v-else
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-5 h-5"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        {{ isLoading ? "Đang xử lý..." : "Xác nhận thanh toán" }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Auth } from "~/types/Auth";

const props = defineProps([
  "payment",
  "paymentMethod",
  "orderDetails",
  "paymentAmount",
]);
const emits = defineEmits(["backQr", "createManualSuccess"]);
const received = ref<number>(0);
const changeAmount = ref<number>(0);
const app = useNuxtApp();
const orderStore = useOrderStore();
const { fetchOrderDetails, updateStatusApproved } = useOrder();

const handleClickBack = () => {
  emits("backQr");
};
const handleRecommend = (value: any) => {
  received.value = value;
  formattedReceived.value = formatCurrency(received.value);
  updateReceived(formattedReceived.value);
  console.log("");
};
const updateReceived = (value: any) => {
  formattedReceived.value = value;
  suggestAmounts(value);
};
const formattedReceived = computed({
  get() {
    return formatCurrencyV2(received.value);
  },
  set(value: string) {
    received.value = +value.replace(/[^\d]/g, "");
  },
});
const suggest = ref();
function suggestAmounts(amount: number) {
  const suggestionThresholds = [10000, 20000, 50000, 100000, 200000, 500000];
  const suggestions: number[] = [];

  // Đảm bảo số chính xác được gợi ý đầu tiên
  if (amount > 0) {
    suggestions.push(amount);
  }

  // Làm tròn đến bội số gần nhất của 5000
  const roundedToNext5000 = Math.ceil(amount / 5000) * 5000;
  if (roundedToNext5000 !== amount && roundedToNext5000 > 0) {
    suggestions.push(roundedToNext5000);
  }

  // Tạo gợi ý dựa trên các mệnh giá phổ biến
  suggestionThresholds.forEach((menhGia) => {
    if (amount <= menhGia * 10) {
      // Giới hạn gợi ý cho các trường hợp lớn
      const sum = Math.ceil(amount / menhGia) * menhGia;
      if (!suggestions.includes(sum)) {
        suggestions.push(sum);
      }
    }
  });

  // Loại bỏ trùng lặp và sắp xếp tăng dần
  suggest.value = Array.from(new Set(suggestions)).sort((a, b) => a - b);
}

const calculateChange = () => {
  changeAmount.value =
    received.value -
    (props.paymentAmount > 0
      ? props.paymentAmount
      : props.orderDetails?.data?.remainTotal);
  if (changeAmount.value < 0) {
    changeAmount.value = 0;
  }
};

watch(
  () => props.paymentAmount,
  (newValue) => {
    if (newValue > 0) {
      suggestAmounts(newValue);
    }
  },
  { immediate: true }
);
watch(
  () => props.orderDetails?.data?.remainTotal,
  (newValue) => {
    if (newValue > 0) {
      suggestAmounts(newValue);
    }
  },
  { immediate: true }
);

watch(received, calculateChange);
const { createPaymentOrder, cancelPayment, paymentsByOrders } = usePayment();
const auth = useCookie("auth").value as unknown as Auth;
const isLoading = ref(false);
const handlePaymentManual = async () => {
  ///
  if (
    received.value <
    (props.paymentAmount > 0
      ? props.paymentAmount
      : props.orderDetails?.data?.remainTotal)
  ) {
    app.$toast.warning("Vui lòng nhập số tiền lớn hơn số tiền cần thanh toán");
  } else {
    isLoading.value = true;
    const host = window.location.origin;

    const data = {
      orderId: props.orderDetails?.data?.id,
      paymentMethod: "manual",
      appliedAmount:
        props.paymentAmount > 0
          ? props.paymentAmount
          : props.orderDetails?.data?.remainTotal,
      payDate: Date.now(),
      source: "ORDER_SOURCE",
      returnUrl: `${host}/thanh-toan`,
      paymentType: "ONLINE",
      createBy: auth?.user?.id,
    };
    try {
      const response = await createPaymentOrder(data);
      if (response) {
        orderStore.paymentAmount = 0;
        localStorage.removeItem("paymentAmount");
        await updateStatusApproved(props.orderDetails?.data?.id);
        emits("createManualSuccess");
      }
    } catch (error) {
      throw error;
    } finally {
      isLoading.value = false;
    }
  }
};
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/payment`) {
    received.value = 0;
  }
  next();
});
</script>
