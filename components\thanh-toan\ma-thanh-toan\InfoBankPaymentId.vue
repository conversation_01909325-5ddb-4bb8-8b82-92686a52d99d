<template>
  <div>
    <transition name="modal-fade">
      <div class="w-full transition-all duration-300 transform">
        <!-- Tabs -->
        <div class="relative flex mt-3 border-b">
          <button
            @click="handleChangeTab('qr')"
            class="flex-1 py-2 text-center relative transition-all duration-300"
          >
            QR Chuyển <PERSON>ho<PERSON>n
          </button>
          <button
            @click="handleChangeTab('account')"
            class="flex-1 py-2 text-center relative transition-all duration-300"
          >
            Tài khoản nhận
          </button>

          <!-- Hiệu ứng thanh gạch dưới -->
          <div
            class="absolute bottom-0 h-1 bg-primary transition-all duration-300"
            :class="tab === 'qr' ? 'left-0 w-1/2' : 'left-1/2 w-1/2'"
          ></div>
        </div>

        <!-- Nội dung tab với animation -->
        <div class="relative mt-4 h-auto w-full">
          <transition name="slide-fade" mode="out-in">
            <div
              v-if="tab === 'account'"
              key="account"
              class="mx-2 space-y-2 mb-2"
            >
              <div>
                <p class="text-gray-500 text-sm">Ngân hàng nhận</p>
                <div class="flex items-center border p-2 rounded-lg">
                  <!-- <img
                      src="/bank-icon.png"
                      class="w-5 h-5 mr-2"
                      alt="Bank Icon"
                    /> -->
                  <p class="text-gray-700">{{ dataUserBank?.description }}</p>
                </div>
              </div>
              <div>
                <p class="text-gray-500 text-sm">Số tài khoản nhận</p>
                <div
                  class="flex justify-between items-center border p-2 rounded-lg"
                >
                  <p class="text-gray-700">{{ dataUserBank?.gwPartnerCode }}</p>
                  <button @click="copyToClipboard(dataUserBank?.gwPartnerCode)">
                    <Icon
                      name="heroicons:clipboard"
                      class="w-5 h-5 text-gray-500"
                    />
                  </button>
                </div>
              </div>

              <div>
                <p class="text-gray-500 text-sm">Số tiền</p>
                <div
                  class="flex justify-between items-center border p-2 rounded-lg"
                >
                  <p class="text-gray-700">
                    {{
                      formatCurrency(
                        dataPayment?.totalAmount || dataQrCode?.totalAmount
                      )
                    }}
                  </p>
                  <button
                    @click="
                      copyToClipboard(
                        dataPayment?.totalAmount || dataQrCode?.totalAmount
                      )
                    "
                  >
                    <Icon
                      name="heroicons:clipboard"
                      class="w-5 h-5 text-gray-500"
                    />
                  </button>
                </div>
              </div>

              <div>
                <p class="text-gray-500 text-sm">Nội dung</p>
                <div
                  class="flex justify-between items-center border p-2 rounded-lg"
                >
                  <p class="text-gray-700">{{ `inv${dataPayment?.id}inv` }}</p>
                  <button @click="copyToClipboard(`inv${dataPayment?.id}inv`)">
                    <Icon
                      name="heroicons:clipboard"
                      class="w-5 h-5 text-gray-500"
                    />
                  </button>
                </div>
              </div>
            </div>
            <div v-else key="qr" class="w-full">
              <div class="flex items-center justify-center">
                <img
                  :src="dataQrCode?.qrCodeUrl || dataQrCode?.qrCode"
                  alt="QR Code"
                  class="w-[60%] h-[60%]"
                  loading="lazy"
                />
              </div>
              <div
                @click="downloadQrCode"
                class="flex items-center justify-center gap-1 text-primary font-semibold"
              >
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
                    />
                  </svg>
                </span>
                <div class="">Tải mã QR</div>
              </div>
              <div class="mx-2">
                <div>1. Tải mã QR về điện thoại</div>
                <div>2. Mở ứng dụng và chọn quét mã QR</div>
                <div>3. Chọn hình QR đã lưu để thực hiện chuyển khoản</div>
              </div>
            </div>
          </transition>
        </div>
        <!-- Nút mở app -->
        <div class="mx-2">
          <div class="font-semibold">Bằng ứng dụng ngân hàng</div>
        </div>
      </div>
    </transition>
    <ModalPaymentReturn v-if="isOpenModal" @cancel="toogleCloseModal">
    </ModalPaymentReturn>
    <SelectedBank
      :dataBank="dataBank"
      :dataUserBank="dataUserBank"
      :dataPayment="dataPayment"
      :dataQrCode="dataQrCode"
      @cancel="toogleCancelSelectedBank"
      @handleSelectedBank="handleSelectedBank"
    ></SelectedBank>
  </div>
</template>

<script setup>
const tab = ref("qr");
const props = defineProps(["dataQrCode", "dataPayment"]);
const handleChangeTab = (tabValue) => {
  tab.value = tabValue;
};
const emits = defineEmits(["closeModal"]);
const closeModal = () => {
  emits("closeModal");
};
const copyToClipboard = async (value) => {
  try {
    await navigator.clipboard?.writeText(value);
    useNuxtApp().$toast.success("Lưu vào bộ nhớ thành công");
  } catch (error) {
    console.error("Lỗi khi sao chép:", error);
  }
};
const isSelectedBank = ref(false);
const toogleCancelSelectedBank = () => {
  isSelectedBank.value = !isSelectedBank.value;
};
const selectedBank = ref();
const handleSelectedBank = (bank) => {
  toogleCancelSelectedBank();
  selectedBank.value = bank;
};
const handleRemoveSelectedBank = () => {
  toogleCancelSelectedBank();
  selectedBank.value = null;
};
const value = ref();
const {
  createPaymentOrder,
  cancelPayment,
  paymentsByOrders,
  getAndroidBank,
  getIosBank,
  getConfigPayment,
} = usePayment();

const isOpenModal = ref(false);
const toogleCloseModal = () => {
  isOpenModal.value = !isOpenModal.value;
};
const downloadQrCode = async () => {
  const userAgent = navigator.userAgent || navigator.vendor;
  const isZalo = /Zalo/i.test(userAgent); // Kiểm tra nếu "Zalo" xuất hiện trong user agent

  if (isZalo) {
    isOpenModal.value = true;
    return;
  }

  //
  const qrCodeUrl = props.dataQrCode?.qrCodeUrl || props.dataQrCode?.qrCode;

  if (!qrCodeUrl) {
    console.error("Không có dữ liệu QR Code");
    useNuxtApp().$toast.success("Không có dữ liệu QR Code");
    return;
  }

  try {
    const response = await fetch(qrCodeUrl, { mode: "cors" });
    const blob = await response.blob();

    const img = new Image();
    img.src = URL.createObjectURL(blob);
    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Tạo URL ảnh từ canvas
      const dataUrl = canvas.toDataURL("image/png");

      // Mở ảnh trong tab mới để người dùng lưu
      const link = document.createElement("a");
      link.href = dataUrl;
      link.download = "qrcode.png";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };
  } catch (error) {
    console.error("Lỗi khi tải ảnh QR Code:", error);
    useNuxtApp().$toast.error("Không thể tải ảnh QR Code");
  }
};

const dataUserBank = ref();
const handleGetConfigDetail = async () => {
  try {
    const response = await getConfigPayment(
      props.dataQrCode?.gatewayConfigId || props.dataQrCode?.gwConfigId
    );
    dataUserBank.value = response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetConfigDetail();
});
const handleOpenApp = () => {
  if (selectedBank.value) {
    window.location.href = selectedBank.value?.deeplink;
  } else {
    useNuxtApp().$toast.warning("Vui lòng chọn ngân hàng để thanh toán");
  }
};
</script>

<style>
/* Hiệu ứng modal */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Hiệu ứng đổi tab */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
