/**
 * Tab-Isolated Context Management
 * 
 * This composable provides tab-isolated storage for orgId and storeId
 * to prevent cross-tab interference while maintaining existing functionality.
 */

import { ref, computed, watch } from 'vue';

interface TabContext {
  orgId: string;
  storeId: string;
  tabId: string;
}

interface TabContextOptions {
  fallbackToUrl?: boolean;
  syncWithUrl?: boolean;
  persistOnRefresh?: boolean;
}

// Global reactive state for current tab context
const currentTabContext = ref<TabContext>({
  orgId: 'N/A',
  storeId: 'N/A',
  tabId: ''
});

// Generate unique tab ID
const generateTabId = (): string => {
  return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Get session storage key for current tab
const getTabStorageKey = (key: string): string => {
  return `${currentTabContext.value.tabId}_${key}`;
};

export const useTabContext = (options: TabContextOptions = {}) => {
  const {
    fallbackToUrl = true,
    syncWithUrl = true,
    persistOnRefresh = true
  } = options;

  const route = useRoute();
  const router = useRouter();

  // Initialize tab context
  const initializeTabContext = (): void => {
    // Check if we already have a tab ID in session storage
    let tabId = sessionStorage.getItem('tabId');
    
    if (!tabId) {
      // Generate new tab ID for new tab
      tabId = generateTabId();
      sessionStorage.setItem('tabId', tabId);
    }

    currentTabContext.value.tabId = tabId;

    // Load context from session storage first
    const storedOrgId = sessionStorage.getItem(getTabStorageKey('orgId'));
    const storedStoreId = sessionStorage.getItem(getTabStorageKey('storeId'));

    // Fallback to URL parameters if enabled
    if (fallbackToUrl) {
      const urlOrgId = route.query.orgId as string;
      const urlStoreId = route.query.storeId as string;

      currentTabContext.value.orgId = storedOrgId || urlOrgId || 'N/A';
      currentTabContext.value.storeId = storedStoreId || urlStoreId || 'N/A';
    } else {
      currentTabContext.value.orgId = storedOrgId || 'N/A';
      currentTabContext.value.storeId = storedStoreId || 'N/A';
    }

    // Save to session storage
    saveToSessionStorage();
  };

  // Save context to session storage
  const saveToSessionStorage = (): void => {
    sessionStorage.setItem(getTabStorageKey('orgId'), currentTabContext.value.orgId);
    sessionStorage.setItem(getTabStorageKey('storeId'), currentTabContext.value.storeId);
  };

  // Update URL with current context
  const syncUrlWithContext = async (): Promise<void> => {
    if (!syncWithUrl) return;

    const currentQuery = { ...route.query };
    let shouldUpdate = false;

    if (currentTabContext.value.orgId !== 'N/A' && currentQuery.orgId !== currentTabContext.value.orgId) {
      currentQuery.orgId = currentTabContext.value.orgId;
      shouldUpdate = true;
    }

    if (currentTabContext.value.storeId !== 'N/A' && currentQuery.storeId !== currentTabContext.value.storeId) {
      currentQuery.storeId = currentTabContext.value.storeId;
      shouldUpdate = true;
    }

    if (shouldUpdate) {
      await router.replace({
        path: route.path,
        query: currentQuery
      });
    }
  };

  // Set organization ID
  const setOrgId = async (orgId: string): Promise<void> => {
    currentTabContext.value.orgId = orgId;
    saveToSessionStorage();
    
    if (syncWithUrl) {
      await syncUrlWithContext();
    }

    // Update SDK if available
    const { $sdk } = useNuxtApp();
    if ($sdk && $sdk.setOrgId) {
      $sdk.setOrgId(orgId);
    }
  };

  // Set store ID
  const setStoreId = async (storeId: string): Promise<void> => {
    currentTabContext.value.storeId = storeId;
    saveToSessionStorage();
    
    if (syncWithUrl) {
      await syncUrlWithContext();
    }

    // Update SDK if available
    const { $sdk } = useNuxtApp();
    if ($sdk && $sdk.setStoreId) {
      $sdk.setStoreId(storeId);
    }
  };

  // Set both org and store ID
  const setContext = async (orgId: string, storeId: string): Promise<void> => {
    currentTabContext.value.orgId = orgId;
    currentTabContext.value.storeId = storeId;
    saveToSessionStorage();
    
    if (syncWithUrl) {
      await syncUrlWithContext();
    }

    // Update SDK if available
    const { $sdk } = useNuxtApp();
    if ($sdk) {
      if ($sdk.setOrgId) $sdk.setOrgId(orgId);
      if ($sdk.setStoreId) $sdk.setStoreId(storeId);
    }
  };

  // Clear context
  const clearContext = (): void => {
    currentTabContext.value.orgId = 'N/A';
    currentTabContext.value.storeId = 'N/A';
    
    // Clear from session storage
    sessionStorage.removeItem(getTabStorageKey('orgId'));
    sessionStorage.removeItem(getTabStorageKey('storeId'));
  };

  // Get current context
  const getContext = (): TabContext => {
    return { ...currentTabContext.value };
  };

  // Computed properties for reactive access
  const orgId = computed(() => currentTabContext.value.orgId);
  const storeId = computed(() => currentTabContext.value.storeId);
  const tabId = computed(() => currentTabContext.value.tabId);

  // Check if context is valid
  const isContextValid = computed(() => {
    return currentTabContext.value.orgId !== 'N/A' && currentTabContext.value.storeId !== 'N/A';
  });

  const isOrgValid = computed(() => {
    return currentTabContext.value.orgId !== 'N/A';
  });

  const isStoreValid = computed(() => {
    return currentTabContext.value.storeId !== 'N/A';
  });

  // Watch for URL changes and sync context
  if (process.client) {
    watch(() => route.query, (newQuery) => {
      if (fallbackToUrl) {
        const urlOrgId = newQuery.orgId as string;
        const urlStoreId = newQuery.storeId as string;

        if (urlOrgId && urlOrgId !== currentTabContext.value.orgId) {
          currentTabContext.value.orgId = urlOrgId;
          saveToSessionStorage();
        }

        if (urlStoreId && urlStoreId !== currentTabContext.value.storeId) {
          currentTabContext.value.storeId = urlStoreId;
          saveToSessionStorage();
        }
      }
    }, { immediate: false });
  }

  // Initialize on client side
  if (process.client) {
    initializeTabContext();
  }

  return {
    // State
    orgId,
    storeId,
    tabId,
    
    // Computed
    isContextValid,
    isOrgValid,
    isStoreValid,
    
    // Methods
    setOrgId,
    setStoreId,
    setContext,
    clearContext,
    getContext,
    initializeTabContext,
    syncUrlWithContext
  };
};

// Export for global access
export { currentTabContext };
