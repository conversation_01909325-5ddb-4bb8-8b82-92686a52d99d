# Build image using node:20-alpine
FROM node:20-alpine AS builder

WORKDIR /app

# Copy source code vào container
COPY . .

# Cài đặt các phụ thuộc
RUN npm config set legacy-peer-deps true
RUN npm install

# Sử dụng biến môi trường để xác định chế độ build
ARG ENV
ENV NODE_ENV=$ENV

# Build ứng dụng với môi trường tương ứng
RUN npm run build:$NODE_ENV

# Chạy ứng dụng với môi trường tương ứng
CMD ["sh", "-c", "npm run start:$NODE_ENV"]
