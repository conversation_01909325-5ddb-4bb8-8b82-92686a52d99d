import { defineStore } from "pinia";
import { ref } from "vue";

export const usePaymentStore = defineStore("payment", () => {
  const dataPaymentOrder = ref();
  const { paymentsByOrders } = usePayment();
  const { getCustomerWallet } = useCustomer();
  const getDataPaymentOrder = async (orderId: string) => {
    try {
      const response = await paymentsByOrders([orderId]);
      dataPaymentOrder.value = response;
    } catch (error) {
      throw error;
    }
  };
  const customerWallet = ref();
  const handleGetCustomerWallet = async (customerId: string, type: string) => {
    customerWallet.value = null;
    try {
      const response = await getCustomerWallet(customerId, type);
      if (response?.errorCode) {
        customerWallet.value = null;
      } else {
        customerWallet.value = response?.details?.data;
      }
    } catch (error) {
      throw error;
    }
  };
  return {
    dataPaymentOrder,
    getDataPaymentOrder,
    handleGetCustomerWallet,
    customerWallet,
  };
});
