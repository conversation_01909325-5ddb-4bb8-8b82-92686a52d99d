<template>
  <div class="bg-white p-2 rounded">
    <div class="text-lg font-semibold text-primary mb-2">
      Thông tin sản phẩm theo lo<PERSON>i
    </div>

    <table v-if="product?.featureTypes" class="w-full">
      <thead>
        <tr class="bg-secondary font-semibold">
          <th class="p-2 text-center font-semibold">Id</th>
          <th class="p-2 text-center font-semibold">Loại</th>
          <th class="p-2 text-center font-semibold">Loại thuộc tính</th>
          <th class="p-2 text-center font-semibold">Giá trị</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="option in product?.featureTypes"
          class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white text-sm"
        >
          <td class="p-2 text-center">{{ option?.id }}</td>
          <td class="p-2 text-center">{{ option?.name }}</td>
          <td class="p-2 text-center"><PERSON><PERSON><PERSON><PERSON> thể</td>
          <td class="p-2 text-center">
            <div class="flex flex-wrap gap-2 items-center justify-center">
              <span
                v-for="(value, index) in option?.values"
                :key="index"
                class="px-2 py-1 bg-secondary rounded whitespace-nowrap"
              >
                {{ value }}
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
defineProps(["product"]);
</script>
