<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
  >
    <div
      class="bg-white rounded-lg shadow-lg px-2 py-4 max-w-sm md:max-w-4xl w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold"><PERSON><PERSON> mụ<PERSON> sản phẩm</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- phần danh mục -->
      <div
        class="max-h-[400px] md:h-[400px] h-[250px] overflow-y-auto grid grid-cols-12"
      >
        <div class="col-span-2">
          <div
            v-for="category in dataCategory"
            @click="selectCategory(category)"
            :key="category?.id"
            :class="{
              ' border-b-primary border-b-2':
                selectedCategory?.id === category?.id,
              'cursor-pointer border-b p-2 ': true,
            }"
          >
            {{ category?.title }}
          </div>
        </div>
        <div class="col-span-10 overflow-y-auto border-l p-2 scroll-smooth">
          <div class="flex flex-wrap gap-4 h-[350px]">
            <div v-if="isLoading" v-for="item in 6">
              <LoadingProduct></LoadingProduct>
            </div>
            <div v-else v-for="product in dataProduct" :key="product.id">
              <ProductCategoryCard
                :product="product"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const selectedCategory = ref(); // Thêm biến để lưu trữ category đã chọn

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const handleCofirm = () => {
  emit("confirm");
};

const { getCategory, getProduct } = useProduct();
const dataCategory = ref<any>([]);
const dataProduct = ref<any>([]);

const selectCategory = async (category: any) => {
  isLoading.value = true;
  selectedCategory.value = category; // Cập nhật category đã chọn
  const dataQuery = {
    keyword: "",
    category: category?.id,
  };
  const res = await getProduct(dataQuery);
  dataProduct.value = res.data;
  isLoading.value = false;
};
const isLoading = ref(false);
onMounted(async () => {
  const response = await getCategory("", 1);
  dataCategory.value = response;
  console.log("dataCategory", dataCategory.value);
  if (dataCategory.value.length > 0) {
    selectCategory(dataCategory.value[0]);
  }
});
</script>
