<template>
  <div
    v-if="totalItems > 0"
    class="flex-shrink-0 bg-white border-t border-gray-200 px-4 py-3"
    :style="{ minHeight: minHeight }"
  >
    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
      <!-- Items info -->
      <div class="text-sm text-gray-700 order-2 sm:order-1 hidden sm:block">
        <span v-if="showItemsInfo">
          Hiển thị <span class="font-medium">{{ startItem }}</span> đến
          <span class="font-medium">{{ endItem }}</span> trong tổng số
          <span class="font-medium">{{ totalItems }}</span> {{ itemLabel }}
        </span>
        <span v-else> {{ totalItems }} {{ itemLabel }} </span>
      </div>

      <!-- Pagination controls -->
      <div
        class="flex items-center gap-2 order-1 sm:order-2 w-full sm:w-auto justify-center sm:justify-end"
      >
        <!-- Previous button -->
        <button
          @click="handlePrevious"
          :disabled="currentPage <= 1"
          class="inline-flex items-center px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white transition-colors duration-200"
          :class="buttonClass"
        >
          <svg
            v-if="showIcons"
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
          {{ previousLabel }}
        </button>

        <!-- Page info -->
        <div class="flex items-center">
          <span v-if="showPageNumbers" class="px-3 py-2 text-sm text-gray-700">
            Trang {{ currentPage }} / {{ totalPages }}
          </span>

          <!-- Enhanced page numbers (optional) -->
          <div
            v-if="showEnhancedPagination"
            class="flex items-center gap-1 mx-2"
          >
            <!-- First page -->
            <button
              v-if="showFirstPage"
              @click="handlePageClick(1)"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 bg-white transition-colors duration-200"
              :class="
                currentPage === 1 ? 'bg-primary text-white border-primary' : ''
              "
            >
              1
            </button>

            <!-- Start ellipsis -->
            <span v-if="showStartEllipsis" class="px-2 text-gray-400">...</span>

            <!-- Visible pages -->
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="handlePageClick(page)"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 bg-white transition-colors duration-200"
              :class="
                currentPage === page
                  ? 'bg-primary text-white border-primary'
                  : ''
              "
            >
              {{ page }}
            </button>

            <!-- End ellipsis -->
            <span v-if="showEndEllipsis" class="px-2 text-gray-400">...</span>

            <!-- Last page -->
            <button
              v-if="showLastPage"
              @click="handlePageClick(totalPages)"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 bg-white transition-colors duration-200"
              :class="
                currentPage === totalPages
                  ? 'bg-primary text-white border-primary'
                  : ''
              "
            >
              {{ totalPages }}
            </button>
          </div>
        </div>

        <!-- Next button -->
        <button
          @click="handleNext"
          :disabled="currentPage >= totalPages"
          class="inline-flex items-center px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white transition-colors duration-200"
          :class="buttonClass"
        >
          {{ nextLabel }}
          <svg
            v-if="showIcons"
            class="w-4 h-4 ml-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile simple pagination (alternative layout) -->
    <div v-if="mobileSimple" class="sm:hidden">
      <div class="flex items-center justify-center gap-2 mt-4">
        <button
          @click="handlePrevious"
          :disabled="currentPage <= 1"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
        >
          {{ previousLabel }}
        </button>
        <span class="px-3 py-2 text-sm text-gray-700">
          {{ currentPage }} / {{ totalPages }}
        </span>
        <button
          @click="handleNext"
          :disabled="currentPage >= totalPages"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
        >
          {{ nextLabel }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage?: number;
  itemLabel?: string;
  previousLabel?: string;
  nextLabel?: string;
  showItemsInfo?: boolean;
  showPageNumbers?: boolean;
  showIcons?: boolean;
  showEnhancedPagination?: boolean;
  maxVisiblePages?: number;
  minHeight?: string;
  buttonClass?: string;
  mobileSimple?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  itemsPerPage: 10,
  itemLabel: "mục",
  previousLabel: "Trước",
  nextLabel: "Sau",
  showItemsInfo: true,
  showPageNumbers: true,
  showIcons: false,
  showEnhancedPagination: false,
  maxVisiblePages: 5,
  minHeight: "60px",
  buttonClass: "",
  mobileSimple: false,
});

const emit = defineEmits<{
  "page-change": [page: number];
  previous: [];
  next: [];
}>();

// Computed properties
const startItem = computed(() => {
  if (props.totalItems === 0) return 0;
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  if (props.totalItems === 0) return 0;
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems);
});

// Enhanced pagination logic
const visiblePages = computed(() => {
  if (!props.showEnhancedPagination) return [];

  const pages: number[] = [];
  const maxVisible = props.maxVisiblePages;
  const current = props.currentPage;
  const total = props.totalPages;

  if (total <= maxVisible) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(1, current - half);
    let end = Math.min(total, current + half);

    if (end - start + 1 < maxVisible) {
      if (start === 1) {
        end = Math.min(total, start + maxVisible - 1);
      } else {
        start = Math.max(1, end - maxVisible + 1);
      }
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
  }

  return pages;
});

const showFirstPage = computed(() => {
  return (
    props.showEnhancedPagination &&
    props.totalPages > props.maxVisiblePages &&
    !visiblePages.value.includes(1)
  );
});

const showLastPage = computed(() => {
  return (
    props.showEnhancedPagination &&
    props.totalPages > props.maxVisiblePages &&
    !visiblePages.value.includes(props.totalPages)
  );
});

const showStartEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2;
});

const showEndEllipsis = computed(() => {
  return (
    showLastPage.value &&
    visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1
  );
});

// Event handlers
const handlePrevious = () => {
  if (props.currentPage > 1) {
    const newPage = props.currentPage - 1;
    emit("page-change", newPage);
    emit("previous");
  }
};

const handleNext = () => {
  if (props.currentPage < props.totalPages) {
    const newPage = props.currentPage + 1;
    emit("page-change", newPage);
    emit("next");
  }
};

const handlePageClick = (page: number) => {
  if (page !== props.currentPage && page >= 1 && page <= props.totalPages) {
    emit("page-change", page);
  }
};
</script>

<style scoped>
/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Hover effects */
button:not(:disabled):hover {
  transform: translateY(-1px);
}

button:not(:disabled):active {
  transform: translateY(0);
}

/* Mobile responsive */
@media (max-width: 640px) {
  .flex-col {
    gap: 0.5rem;
  }
}
</style>
