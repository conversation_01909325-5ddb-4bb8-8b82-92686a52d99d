<!-- components/Modal.vue -->
<template>
  <div
    v-if="isOpen"
    class="fixed z-[999999999] inset-0 flex items-center justify-center bg-black bg-opacity-50"
    @click="close"
  >
    <div
      class="bg-white rounded-lg px-8 py-4 shadow-lg w-full md:w-[40vw]"
      @click.stop
    >
      <div class="flex justify-between">
        <h2 class="text-lg font-bold textShadow"><PERSON><PERSON> chọn sản phẩm</h2>
        <div class="flex justify-end">
          <button
            @click="close"
            class="text-lg font-bold textShadow text-primary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-6 h-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div v-if="isLoading">
        <svg
          class="animate-spin h-10 w-10 text-primary mx-auto"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
          ></path>
        </svg>
      </div>

      <div class="p-3" v-else>
        <div v-for="(option, index) in options" :key="index" class="mt-4">
          <h3 class="mb-2 text-primary font-semibold">{{ option.name }}</h3>
          <div class="flex gap-2">
            <div
              v-for="(value, valueIndex) in option.valuesFull"
              :key="valueIndex"
              class="flex gap-2"
            >
              <button
                class="px-4 py-1.5 text-sm rounded border border-primary text-primary hover:bg-primary hover:text-white disabled:hover:bg-gray-300 disabled:hover:text-gray-500"
                :class="{
                  'bg-primary text-white':
                    selectedOptions[option.id] === value.id,
                  'bg-gray-300 text-gray-500 cursor-not-allowed':
                    !isValidOption(option.id, value.id),
                }"
                @click="selectOption(option.id, value.id)"
                :disabled="!isValidOption(option.id, value.id)"
              >
                {{ value.name }}
              </button>
            </div>
          </div>
        </div>

        <div v-if="selectedVariant" class="text-sm mt-3">
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Mã sản phẩm:</span>
            {{ selectedVariant.id }}
            <span class="text-black font-bold mr-2 ml-1"> - SKU:</span
            >{{ selectedVariant.sku }}
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Giá sản phẩm:</span>
            {{
              selectedVariant?.price !== null &&
              selectedVariant?.price !== undefined
                ? formatCurrency(selectedVariant?.price)
                : "Chưa có giá"
            }}
            - <span class="mx-1 text-black font-bold"> Tồn kho: </span>
            {{ inventory?.orderAble }}
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">VAT:</span>
            <span class="text-primary">{{ product?.vat || 0 }}%</span>
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Giá sau VAT:</span>
            <span class="text-primary font-semibold">
              {{
                selectedVariant?.price !== null &&
                selectedVariant?.price !== undefined
                  ? formatCurrency(
                      calculatePriceAfterVAT(selectedVariant.price)
                    )
                  : "Chưa có giá"
              }}
            </span>
          </div>
        </div>
        <div v-if="product && !selectedVariant" class="text-sm mt-3">
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Mã sản phẩm:</span>
            {{ product.id }}
            <span class="text-black font-bold mr-2 ml-1"> - SKU:</span
            >{{ product.sku }}
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Giá sản phẩm:</span>
            {{ formatCurrency(product.price) }}
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">VAT:</span>
            <span class="text-primary">{{ product?.vat || 0 }}%</span>
          </div>
          <div class="flex pb-1">
            <span class="text-black font-bold mr-2">Giá sau VAT:</span>
            <span class="text-primary font-semibold">
              {{ formatCurrency(calculatePriceAfterVAT(product.price)) }}
            </span>
          </div>
        </div>
      </div>
      <div class="w-full flex justify-end">
        <button
          @click="handleAddOrderLineItems()"
          class="mt-4 bg-primary text-white py-2 px-4 rounded hover:bg-blue-900"
        >
          Xác nhận
        </button>
      </div>
    </div>
    <div v-if="isAddLoading">
      <LoadingSpinner />
    </div>
    <ConfirmDialog
      v-if="isEditOrder"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh sản phẩm`"
      @confirm="confirm"
      @cancel="cancel"
    ></ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
const { getProductById } = useProduct();
const orderStore = useOrderStore();
const props = defineProps(["isOpen", "productId"]);
const selectedOptions = ref<{ [key: string]: string }>({});
const product = ref({}) as any;
const isLoading = ref(false);
const isAddLoading = ref(false);
const emit = defineEmits(["close"]);
const isEditOrder = ref(false);
const orderDetail = computed(() => orderStore.orderDetail);

const handleAddOrderLineItems = async () => {
  // ngay chỗ này

  if (selectedVariant) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrder.value = true;
    } else {
      isAddLoading.value = true;
      const variantWithVat = {
        ...selectedVariant.value,
        vat: product.value?.vat || 0,
      };
      await orderStore.addProductToOrder(variantWithVat);
      isAddLoading.value = false;
      emit("close");
    }
  }
};
const confirm = async () => {
  if (selectedVariant) {
    isAddLoading.value = true;
    const variantWithVat = {
      ...selectedVariant.value,
      vat: product.value?.vat || 0,
    };
    await orderStore.addProductToOrder(variantWithVat);
    isEditOrder.value = false;
    isAddLoading.value = false;
    emit("close");
  }
};
const cancel = () => {
  isEditOrder.value = false;
};
const close = () => {
  emit("close");
};

// Danh sách option
const options = ref([]) as any;

// Mối quan hệ giữa các option
const relationship = ref({}) as any;

// Danh sách biến thể
const variants = ref([]) as any;

// Biến lưu trữ ID của các option đã chọn

// Tính toán biến thể đã chọn
const selectedVariant = computed(() => {
  const selectedOptionIds = Object.values(selectedOptions.value);
  return variants.value.find((variant: any) => {
    return variant.optionsIds?.every((id: string) =>
      selectedOptionIds.includes(id)
    );
  });
});

// Xử lý sự kiện chọn option
const selectOption = (optionId: string, valueId: string) => {
  selectedOptions.value[optionId] = valueId;
};

const isValidOption = (currentOptionId: string, valueId: string) => {
  const previousOptionId = Object.entries(selectedOptions.value).find(
    ([key, _]) => key !== currentOptionId
  )?.[1];

  if (!previousOptionId) {
    // Đây là option đầu tiên được chọn, luôn hợp lệ
    return true;
  }

  const relatedOptionIds = relationship.value[previousOptionId];
  return relatedOptionIds.includes(valueId);
};

// Hàm tính giá sau VAT
const calculatePriceAfterVAT = (price: number) => {
  if (price === null || price === undefined) {
    return 0;
  }

  const vatRate = product.value?.vat || 0;
  const vatAmount = (price * vatRate) / 100;
  return price + vatAmount;
};

// Hàm fetch dữ liệu sản phẩm
const fetchProductData = async () => {
  if (!props.productId) return;

  isLoading.value = true;
  try {
    const response = await getProductById(props.productId);
    product.value = response;
    const optionsData = response;
    options.value = optionsData.featureTypes;
    relationship.value = optionsData.optionsRelationship;
    variants.value = optionsData.variants;

    // Reset selected options khi chuyển sản phẩm
    selectedOptions.value = {};
  } catch (error) {
    console.error("Error fetching product data:", error);
  } finally {
    isLoading.value = false;
  }
};

// Fetch dữ liệu từ API khi component được mount
onMounted(async () => {
  await fetchProductData();
});

// Watch để cập nhật dữ liệu khi productId thay đổi
watch(
  () => props.productId,
  async (newProductId, oldProductId) => {
    if (newProductId && newProductId !== oldProductId) {
      await fetchProductData();
    }
  }
);
const { getInventoryV2 } = useWarehouse();
const warehouseId = useCookie("warehouseId");
const inventory = ref();
const fetchInventoryForVariant = async () => {
  if (!selectedVariant.value || !warehouseId.value) return;

  const data = [
    {
      productId: product.value?.id,
      variantId: selectedVariant.value?.id,
      sku: selectedVariant.value?.sku,
    },
  ];
  const res = await getInventoryV2(warehouseId.value as string, data);
  inventory.value = res[0];
};
watch(selectedVariant, async (newVariant) => {
  if (newVariant) {
    await fetchInventoryForVariant();
  } else {
    inventory.value = null;
  }
});
</script>
