<template>
  <div v-if="isModalEditCustomer" class="fixed z-10 inset-0">
    <div
      class="flex items-center justify-center min-h-screen bg-gray-500 bg-opacity-75"
    >
      <ModalEditCustomer
        :customer="selectedCustomer"
        @closeModalEditCustomer="toggleModalEditCustomer"
      />
    </div>
  </div>
  <div v-if="isModalManageAddressShip" class="fixed z-10 inset-0">
    <div
      class="flex items-center justify-center min-h-screen bg-gray-500 bg-opacity-75"
    >
      <ModalManageAddressShip></ModalManageAddressShip>
    </div>
  </div>
  <!-- Loading Skeleton Rows -->
  <tr v-if="loading" v-for="n in itemsPerPage" :key="n" class="animate-pulse">
    <td v-for="index in headers.length + 1" :key="index" class="px-6 py-4">
      <div class="h-4 bg-gray-200 rounded-md"></div>
    </td>
  </tr>

  <!-- Customer Data Rows -->
  <tr
    v-else
    class="hover:bg-gray-50 transition-colors duration-200 border-b border-gray-100 border-dashed cursor-pointer"
    v-for="(customer, index) in listCustomerResponse"
    :key="customer.id"
  >
    <CustomerItem :customer="customer" :index="index"></CustomerItem>
  </tr>
</template>
<script setup lang="ts">
const currentSettingCustomerId = ref<string | null>(null);
const isModalEditCustomer = ref(false);
const selectedCustomer = ref();
const isModalManageAddressShip = ref(false);
const router = useRouter();

const toggleModalManageAddressShip = () => {
  isModalManageAddressShip.value = !isModalManageAddressShip.value;
};
const handShowSetting = (customerId: string) => {
  if (currentSettingCustomerId.value === customerId) {
    currentSettingCustomerId.value = null;
  } else {
    currentSettingCustomerId.value = customerId;
  }
};

const toggleModalEditCustomer = (customer = null) => {
  isModalEditCustomer.value = !isModalEditCustomer.value;
  selectedCustomer.value = customer;
};

const props = defineProps([
  "listCustomerResponse",
  "loading",
  "currentPage",
  "itemsPerPage",
  "headers",
]);
///
</script>
<style scope></style>
