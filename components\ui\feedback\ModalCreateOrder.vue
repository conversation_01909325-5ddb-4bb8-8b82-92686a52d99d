<template>
  <div>
    <div
      class="fixed inset-0 z-50 flex h-screen items-center justify-center bg-black bg-opacity-50 overflow-y-auto"
      @click.self="handleClosePopup"
    >
      <div
        class="relative inline-block bg-white rounded shadow w-full m-auto xl:w-[700px] lg:w-[50%] max-h-[450px]"
      >
        <div
          @click="handleClosePopup"
          class="absolute top-2 right-2 cursor-pointer text-red-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>

        <div
          class="mt-[15px] text-left mx-[15px] flex justify-center items-center"
        >
          <span class="text-lg font-bold text-red-600"
            >Sản phẩm đã hết hàng!</span
          >
        </div>

        <div class="overflow-y-auto max-h-[340px] px-4">
          <OrderItemsInventory></OrderItemsInventory>
        </div>

        <div class="flex items-center justify-center gap-3 m-3">
          <div
            @click="handleCreateOrderWithInventory"
            class="flex-1 text-primary rounded px-4 py-2 text-sm border-primary border cursor-pointer text-center"
          >
            Loại bỏ các sản phẩm hết
          </div>
          <div
            @click="handleContinueCreateOrder"
            class="flex-1 bg-primary text-white py-2 rounded px-2 cursor-pointer text-center"
          >
            Tiếp tục tạo đơn
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const emits = defineEmits(["closePopup"]);
const orderStore = useOrderStore();
const products = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);
const handleClosePopup = () => {
  localStorage.setItem("paymentAmount", "0");
  orderStore.isAlert = false;
};
const handleCreateOrderWithInventory = () => {
  orderStore.handleCreateOrderWithInventory();
  // console.log("tao don voi order trong");
};
const handleContinueCreateOrder = () => {
  // console.log("tao don order");
  orderStore.handleContinueCreateOrder();
};
</script>
