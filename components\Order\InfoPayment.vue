<template>
  <div class="px-2 bg-white rounded flex flex-col gap-1 text-sm">
    <div class="flex items-center gap-1">
      <h3 class="text-sm font-semibold text-primary">Thông tin thanh toán</h3>
    </div>
    <div></div>
    <!-- <div class="flex items-center justify-between">
      <div>Tổng thành tiền</div>
      <div class="font-semibold text-primary">
        {{ formatCurrency(provisional || 0) }}
      </div>
    </div> -->
    <div>
      <div class="flex items-center justify-between">
        <div>Chiết khấu</div>

        <div
          class="flex items-center bg-secondary rounded px-2 py-1 max-w-[130px]"
        >
          <select
            v-model="discountType"
            class="focus:outline-none bg-secondary"
            @change="handleDiscountTypeChange"
            :disabled="
              !orderStore.orderDetail ||
              orderStore?.orderDetail?.status === 'CANCELLED' ||
              orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
                'INVOICE_PUBLISHED'
            "
          >
            <option value="MONEY">₫</option>
            <option value="PERCENT">%</option>
          </select>
          <input
            type="text"
            class="w-full focus:outline-none text-right bg-secondary"
            v-model="discountValue"
            @blur="handleBlur"
            :disabled="
              !orderStore.orderDetail ||
              orderStore?.orderDetail?.status === 'CANCELLED' ||
              orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
                'INVOICE_PUBLISHED'
            "
          />
          <!-- <CurrencyInput
            :disabled="
              !orderStore.orderDetail ||
              orderStore?.orderDetail?.status === 'CANCELLED'
            "
            class="w-full focus:outline-none text-right bg-secondary"
            v-model:modelValue="discountValue"
            :options="{
              currency: 'VND',
              currencyDisplay: 'hidden',
              hideCurrencySymbolOnFocus: false,
              hideGroupingSeparatorOnFocus: false,
              hideNegligibleDecimalDigitsOnFocus: false,
            }"
            @change="handleBlur"
          /> -->
        </div>
      </div>
      <!-- hạng thành viên -->
      <div
        v-if="customer && campaignLevel && customer?.memberLevel"
        class="flex items-center justify-between"
      >
        <div class="flex gap-1">
          <div
            @click="handleToogleMemberLevel"
            class="flex items-center gap-2 cursor-pointer"
          >
            <input
              type="checkbox"
              id="memberLevel"
              class="w-4 h-4 accent-primary rounded-full cursor-pointer"
              v-model="isMemberLevel"
              @click.stop="handleToogleMemberLevel"
              :disabled="
                !customer?.memberLevel ||
                orderStore?.orderDetail?.status === 'CANCELLED' ||
                orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
                  'INVOICE_PUBLISHED'
              "
            />
            <span>Hạng thành viên</span>
          </div>
          <span
            v-if="customer?.memberLevel"
            class="border text-xs p-[2px] bg-secondary rounded"
            >{{ customer?.memberLevel }}</span
          >
        </div>

        <div v-if="memberLevel" class="text-primary">
          {{
            ` - ${formatCurrency(memberLevel?.value.amount)} ${
              memberLevel?.percent ? `(${memberLevel?.percent})%` : " "
            }`
          }}
        </div>
      </div>
      <div
        v-for="voucher in vouchers"
        class="flex items-center justify-between"
      >
        <div class="flex items-center gap-1">
          <div
            @click="handleRemoveVoucher(voucher)"
            class="text-red-500 cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
          </div>
          <div>{{ voucher.voucherCode || voucher.title }}</div>
        </div>
        <div class="text-primary">
          {{
            ` - ${formatCurrency(voucher.value.amount)} ${
              voucher.percent ? `(${voucher.percent})%` : " "
            }`
          }}
        </div>
      </div>
      <div class="flex items-center gap-3 mt-2">
        <div class="block w-40">Mã giảm giá</div>
        <div
          class="block w-full items-center outline-none px-2 py-1 rounded relative bg-secondary"
        >
          <input
            type="text"
            class="outline-none bg-secondary"
            v-model="voucherCode"
            @input="convertToUpperCase"
            :disabled="
              !orderStore.orderDetail ||
              orderStore?.orderDetail?.status === 'CANCELLED' ||
              orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
                'INVOICE_PUBLISHED'
            "
          />
          <div
            @click="handleAddVoucher"
            class="absolute right-0 top-0 bg-primary text-white px-1 md:px-2 py-1 z-1 rounded-r cursor-pointer"
          >
            Áp dụng
          </div>
        </div>
      </div>
      <div
        @click="handleOpenVoucherPopup"
        class="flex items-center mt-1 justify-end text-xs text-primary cursor-pointer"
      >
        Xem thêm mã giảm giá
      </div>
    </div>

    <!--  -->
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Tổng số tiền</div>
      <div class="text-primary">
        {{ formatCurrency(provisional || 0) }}
      </div>
    </div>

    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Giảm giá</div>
      <div class="text-primary">
        {{
          formatCurrency(
            provisional - orderDetail?.order?.discountTotalPrice?.amount || 0
          )
        }}
      </div>
    </div>
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Sau giảm</div>
      <div class="text-primary">
        {{
          formatCurrency(orderDetail?.order?.discountTotalPrice?.amount || 0)
        }}
      </div>
    </div>
    <div
      v-if="orderDetail?.order?.totalVAT"
      class="flex items-center justify-between py-1"
    >
      <div class="space-x-1">
        <span class="text-primary">VAT</span>
        <span
          v-if="orderDetail?.order?.totalVAT"
          ref="tooltipTargetRef"
          v-tippy.click="tooltipContent"
          class="text-xs text-end cursor-pointer"
          >{{ `(xem chi tiết)` }}</span
        >
      </div>
      <div class="flex items-center gap-2">
        <div class="text-primary">
          {{ `${formatCurrency(orderDetail?.order?.totalVAT?.amount || 0)}` }}
        </div>
      </div>
    </div>

    <div
      v-if="
        orderStore.shippingFee > 0 ||
        orderDetail?.order?.totalShippingPrice?.amount > 0
      "
      class="flex items-center justify-between"
    >
      <div class="text-primary">Phí vận chuyển</div>
      <div class="text-primary">
        {{
          formatCurrency(
            orderStore.shippingFee ||
              orderDetail?.order?.totalShippingPrice?.amount
          )
        }}
      </div>
    </div>
    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Đã thanh toán</div>
      <div class="text-primary">
        {{
          formatCurrency(
            orderDetail?.order?.currentTotalPrice?.amount - totalPrice || 0
          )
        }}
      </div>
    </div>
    <!--  -->
    <div class="border-b mx-2 pt-1"></div>

    <div class="flex items-center justify-between py-1">
      <div class="text-primary">Phải thu</div>
      <div class="font-bold text-red-500">
        {{
          formatCurrency(
            orderStore.shippingFee + totalPrice || orderStore.shippingFee + 0
          )
        }}
      </div>
    </div>
    <VoucherMessage
      v-if="isOpenVoucherPopup"
      @confirm="handleOpenVoucherPopup"
      @cancel="handleOpenVoucherPopup"
    ></VoucherMessage>
    <ConfirmDialog
      v-if="isEditOrderDisCount"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu sản phẩm`"
      @confirm="confirmDiscount"
      @cancel="cancelDiscount"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderTypeDisCount"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu đơn`"
      @confirm="confirmTypeDiscount"
      @cancel="cancelTypeDiscount"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderAddVoucher"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn thêm mã giảm giá`"
      @confirm="confirmAddVoucher"
      @cancel="cancelAddVoucher"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderMemberLevel"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh giảm giá hạng thành viên`"
      @confirm="confirmMemberLevel"
      @cancel="cancelMemberLevel"
    ></ConfirmDialog>
    <ConfirmDialog
      v-if="isEditOrderRemoveVoucher"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh voucher`"
      @confirm="confirmRemoveVoucher"
      @cancel="cancelRemoveVoucher"
    ></ConfirmDialog>
  </div>
</template>
<script setup lang="ts">
// Lazy load heavy components
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);
const VoucherMessage = defineAsyncComponent(
  () => import("~/components/dialog/VoucherMessage.vue")
);
const orderStore = useOrderStore();
const totalPrice = computed(() => orderStore.orderDetail?.remainTotal);
const provisional = computed(
  () => orderStore.orderDetail?.order?.currentSubtotalPrice?.amount
);
const vouchers = computed(() =>
  orderStore.orderDetail?.order?.discountApplications?.filter(
    (item: any) => item.type === "VOUCHER"
  )
);
const memberLevel = computed(() =>
  orderStore.orderDetail?.order?.discountApplications?.find(
    (item: any) => item.type === "MEMBER"
  )
);
const voucherCode = ref();
const { addVoucher, removeVoucher, removeMemberDiscount } = useOrder();
const customer = computed(() => orderStore.customerInOrder);
const orderDetail = computed(() => orderStore.orderDetail);
const isOpenVoucherPopup = ref(false);
const isEditOrderAddVoucher = ref(false);
const handleAddVoucher = async () => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (voucherCode.value) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderAddVoucher.value = true;
    } else {
      await addVoucher(orderDetail.value.id, voucherCode.value);
      voucherCode.value = "";
      await orderStore.getOrderById(orderDetail.value.id);
    }
  }
};
const confirmAddVoucher = async () => {
  if (voucherCode.value) {
    await addVoucher(orderDetail.value.id, voucherCode.value);
    voucherCode.value = "";
    await orderStore.getOrderById(orderDetail.value.id);
  }
  cancelAddVoucher();
};
const cancelAddVoucher = () => {
  isEditOrderAddVoucher.value = false;
};
const convertToUpperCase = () => {
  voucherCode.value = voucherCode.value.toUpperCase();
};
////
const isEditOrderRemoveVoucher = ref(false);
const voucherDraft = ref();
const handleRemoveVoucher = async (item: any) => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderRemoveVoucher.value = true;
    voucherDraft.value = item;
  } else {
    await removeVoucher(orderDetail.value.id, item.voucherCode);
    await orderStore.updateOrder(orderDetail.value.id);
  }
};
const confirmRemoveVoucher = async () => {
  await removeVoucher(orderDetail.value.id, voucherDraft.value.voucherCode);
  await orderStore.updateOrder(orderDetail.value.id);
  cancelRemoveVoucher();
};
const cancelRemoveVoucher = () => {
  isEditOrderRemoveVoucher.value = false;
};
const discountType = ref("MONEY");
const discountValue = ref<number>(0); // Giá trị mặc định

// onMounted(() => {
//   const discountApplication =
//     orderDetail.value?.order?.discountApplications?.find(
//       (item: any) => item?.type === "INPUT"
//     );
//   discountValue.value = discountApplication?.value?.amount || 0; // Nếu không tồn tại, mặc định là 0
// });

const {
  updateDiscount,
  getOrderPromotion,
  updateMemberDiscount,
  updateInfoCampaignPromotion,
} = useOrder();
/////
const isEditOrderTypeDisCount = ref(false);
const discountInputKey = ref(0);

const confirmTypeDiscount = async () => {
  discountValue.value = 0;
  discountInputKey.value++;
  const data = {
    type: discountType.value,
    amount: +discountValue.value,
  };
  await updateDiscount(orderDetail.value?.id, "", data);
  await orderStore.updateOrder(orderDetail.value?.id);
  isEditOrderTypeDisCount.value = false;
};
const cancelTypeDiscount = () => {
  // Chuyển đổi loại discount
  if (discountType.value === "MONEY") {
    discountType.value = "PERCENT";
  } else {
    discountType.value = "MONEY"; // Nếu không phải MONEY thì đặt lại là MONEY
  }

  isEditOrderTypeDisCount.value = false;
};
const handleDiscountTypeChange = async () => {
  // Lưu giá trị hiện tại trước khi thay đổi

  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderTypeDisCount.value = true;
  } else {
    discountValue.value = 0;
    discountInputKey.value++;
    const data = {
      type: discountType.value,
      amount: +discountValue.value,
    };
    await updateDiscount(orderDetail.value?.id, "", data);
    await orderStore.updateOrder(orderDetail.value?.id);
  }
};

/////
const isEditOrderDisCount = ref(false);
const confirmDiscount = async () => {
  const data = {
    type: discountType.value,
    amount: +discountValue.value,
  };

  await updateDiscount(orderDetail.value?.id, "", data);
  await orderStore.updateOrder(orderDetail.value?.id);
  cancelDiscount();
};
const cancelDiscount = () => {
  isEditOrderDisCount.value = false;
};
const handleBlur = async () => {
  if (!discountValue.value) {
    discountValue.value = 0;
  }
  if (+discountValue.value >= 0) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderDisCount.value = true;
    } else {
      // discountValue.value = newVal;
      const data = {
        type: discountType.value,
        amount: +discountValue.value,
      };
      await updateDiscount(orderDetail.value?.id, "", data);
      await orderStore.updateOrder(orderDetail.value?.id);
    }
  }
};

const handleOpenVoucherPopup = () => {
  isOpenVoucherPopup.value = !isOpenVoucherPopup.value;
};
//
const campaignLevel = computed(() => orderStore.campaignLevel);
const isMemberLevel = ref(false);

watch(
  () => orderDetail.value?.order?.discountApplications,
  (newVal) => {
    const response = newVal?.find((item: any) => item.type === "MEMBER");
    if (response) {
      isMemberLevel.value = true;
    } else {
      isMemberLevel.value = false;
    }
  },
  { immediate: true }
);
/////
const isEditOrderMemberLevel = ref(false);
const handleToogleMemberLevel = async (event: any) => {
  event.stopPropagation();
  if (customer.value?.memberLevel) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderMemberLevel.value = true;
    } else {
      isMemberLevel.value = !isMemberLevel.value;
      if (isMemberLevel.value) {
        const campaignAction = campaignLevel.value?.campaignActions.find(
          (action: any) => action.type === "PROMOTION_ORDER_MEMBER"
        );
        if (customer.value?.memberLevel) {
          const response = await getOrderPromotion(customer.value?.memberLevel);
          const data = {
            type: response.data.discountType,
            amount: response.data.discount,
            campaignId: response.data.campaignId,
            campaignActionId: response.data.campaignId,
            campaignActionType: "PROMOTION_ORDER_MEMBER",
          };
          if (response?.data) {
            await Promise.all([
              updateMemberDiscount(orderDetail.value?.id, data),
            ]);
            await orderStore.updateOrder(orderDetail.value.id);
          }
        }
      } else {
        await removeMemberDiscount(orderDetail.value?.id);
        await orderStore.updateOrder(orderDetail.value.id);
      }
    }
  }
};
const confirmMemberLevel = async () => {
  if (customer.value?.memberLevel) {
    // isMemberLevel.value = !isMemberLevel.value;
    if (isMemberLevel.value) {
      const campaignAction = campaignLevel.value?.campaignActions.find(
        (action: any) => action.type === "PROMOTION_ORDER_MEMBER"
      );
      if (customer.value?.memberLevel) {
        const response = await getOrderPromotion(customer.value?.memberLevel);
        const data = {
          type: response.data.discountType,
          amount: response.data.discount,
          campaignId: response.data.campaignId,
          campaignActionId: response.data.campaignId,
          campaignActionType: "PROMOTION_ORDER_MEMBER",
        };
        if (response?.data) {
          await Promise.all([
            updateMemberDiscount(orderDetail.value?.id, data),
          ]);
          await orderStore.updateOrder(orderDetail.value.id);
        }
      }
    } else {
      await removeMemberDiscount(orderDetail.value?.id);
      await orderStore.updateOrder(orderDetail.value.id);
    }
  }
  cancelMemberLevel();
};
const cancelMemberLevel = () => {
  isEditOrderMemberLevel.value = false;
};

const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/sale`) {
    const discountApplication =
      orderDetail.value?.order?.discountApplications?.find(
        (item: any) => item?.type === "INPUT"
      );
    if (discountType?.value === "PERCENT") {
      discountValue.value = discountApplication?.percent;
    } else {
      discountValue.value = discountApplication?.value?.amount || 0; // Nếu không tồn tại, mặc định là 0
    }
  }
  next();
});
watch(
  () => orderDetail.value,
  async (newVal, oldVal) => {
    if (newVal) {
      const discountApplication =
        orderDetail.value?.order?.discountApplications?.find(
          (item: any) => item?.type === "INPUT"
        );
      //
      if (discountApplication) {
        if (
          discountApplication?.percent !== null &&
          discountApplication?.percent !== undefined
        ) {
          discountType.value = "PERCENT";
          discountValue.value = discountApplication.percent;
        } else {
          discountType.value = "MONEY";
          discountValue.value = discountApplication?.value?.amount || 0; // Nếu không tồn tại, mặc định là 0
        }
      }
    } else {
      discountType.value = "MONEY";
      discountValue.value = 0;
    }
  }
);
const tooltipTargetRef = ref<any | null>(null);

const tooltipContent = computed(() => {
  const items = orderDetail.value?.activeOrderItemProfiles || [];
  const itemsHtml = items
    .map((item: any) => {
      return `
        <div class="grid grid-cols-[3fr_1fr_1fr] gap-2 text-sm text-gray-800 mb-1">
          <div class="overflow-hidden text-ellipsis whitespace-nowrap">
            ${item?.orderLineItem?.variant?.id} - ${
        item?.orderLineItem?.variant?.sku || ""
      }
          </div>
          <div class="text-center whitespace-nowrap">
            ${item?.orderLineItem?.vatRate?.amount || 0}%
          </div>
          <div class="text-right whitespace-nowrap">
            ${formatCurrency(item?.orderLineItem?.totalVAT?.amount || 0)}
          </div>
        </div>
      `;
    })
    .join("");

  return `
    <div class="bg-white p-2 rounded-lg text-sm max-w-xs space-y-1">
      <div class="font-semibold text-primary mb-1">Chi tiết VAT</div>

      <div class="grid grid-cols-[3fr_1fr_1fr] font-semibold text-gray-700 border-b pb-2">
        <div>ID - SKU</div>
        <div class="text-center">VAT</div>
        <div class="text-center ">Tiền VAT</div>
      </div>

      <div class="pt-2 space-y-1">
        ${itemsHtml}
      </div>

      <hr class="border-t border-gray-300 my-2" />

      <div class="flex items-center justify-between">
        <span class="font-semibold text-gray-600">Tổng VAT</span>
        <span class="ml-4 block font-medium text-primary">
          ${formatCurrency(orderDetail.value?.order?.totalVAT?.amount || 0)}
        </span>
      </div>
    </div>
  `;
});

watch(
  () => orderDetail.value,
  (newVal) => {
    if (newVal && tooltipTargetRef.value?._tippy) {
      tooltipTargetRef.value._tippy.setContent(tooltipContent.value);
    }
  },
  { deep: true } // quan trọng để bắt thay đổi lồng trong object
);
</script>
