<template>
  <div class="px-2 h-full-custom2">
    <div
      class="grid grid-cols-1 gap-2 max-h-screen-150 overflow-y-auto"
      v-if="productCheck?.length > 0"
    >
      <div class="block md:hidden">
        <div class="text-lg text-primary font-semibold">Danh sách sản phẩm</div>
        <OrderItem
          v-if="route.query.orderId"
          v-for="product in products"
          :key="product.id"
          :product="product.orderLineItem"
        ></OrderItem>
        <div class="border-b pt-1"></div>
        <CardOrderDiary
          v-for="product in dataListProductDiary"
          :key="product.id"
          :product="product.orderLineItem"
        ></CardOrderDiary>
      </div>
      <div class="md:block hidden">
        <TableProduct :isPageSale="true"></TableProduct>
      </div>
    </div>

    <div v-else class="lg:h-full h-[210px] overflow-auto hidden md:block">
      <div class="my-3">
        <div class="flex items-center justify-center">
          <img
            src="@/assets/images/cartEmpty.svg"
            class="w-32"
            loading="lazy"
          />
        </div>
        <div class="mt-2 text-sm font-semibold text-center text-textBlack">
          Bạn chưa có sản phẩm nào trong giỏ hàng!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const OrderItem = defineAsyncComponent(() =>
  import("~/components/features/order/OrderItem.vue")
);
const CardOrderDiary = defineAsyncComponent(() =>
  import("~/components/features/order/CardOrderDiary.vue")
);
const TableProduct = defineAsyncComponent(() =>
  import("~/components/features/sales/TableProduct.vue")
);
const route = useRoute();
const orderStore = useOrderStore();

const products = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);

const orderDetail = computed(
  () => orderStore.orderDetail?.order?.customAttribute?.facilityId
);
const dataListProductDiary = computed(() => orderStore.dataListProductDiary);
const productCheck = computed(() => orderStore.orderDetail?.orderItemProfiles);
</script>
