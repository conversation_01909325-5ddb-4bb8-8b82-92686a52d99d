<template>
  <div class="relative h-screen overflow-y-auto">
    <div class="w-full relative md:m-auto md:w-auto">
      <div class="">
        <div class="sticky top-0">
          <div
            class="h-full w-full p-3 md:flex md:justify-center md:bg-transparent"
          >
            <div
              @click="handleCreateCheckin"
              class="cursor-pointer text-primary-light flex justify-center items-center font-bold rounded-md py-[10px] px-[15px] border border-[#3f51b5] hover:bg-[#6e81ed3b] bg-white md:w-[300px]"
            >
              <div>Tạo checkin</div>
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="red"
                    d="M12 21.325q-.35 0-.7-.125t-.625-.375Q9.05 19.325 7.8 17.9t-2.087-2.762q-.838-1.338-1.275-2.575T4 10.2q0-3.75 2.413-5.975T12 2q3.175 0 5.588 2.225T20 10.2q0 1.125-.437 2.363t-1.275 2.575Q17.45 16.475 16.2 17.9t-2.875 2.925q-.275.25-.625.375t-.7.125M12 12q.825 0 1.413-.587T14 10q0-.825-.587-1.412T12 8q-.825 0-1.412.588T10 10q0 .825.588 1.413T12 12"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div
          ref="scrollContainer"
          class="w-full px-2 md:w-auto md:h-full h-[calc(100vh-120px)] overflow-y-auto md:flex md:flex-col md:items-center md:justify-center"
        >
          <div v-for="item in checkinStore.dataCheckin">
            <CardCheckin :dataCheckin="item" :dataPerson="dataPerson" />
          </div>
        </div>
      </div>
      <ModelCheckin v-if="isCloseModel" @isClose="isClose" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInfiniteScroll } from "@vueuse/core";
import type { Auth } from "~/types/Auth";

const auth = useCookie("auth").value as unknown as Auth;

useHead({
  title: "DMS",
  meta: [
    {
      name: "description",
      content: "Dms",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALE_ADMIN", "SALES"],
  name: "Dms",
});

const isCloseModel = ref(false);
const checkinStore = useCheckinStore();
const { getListWorkEfforts, dataPerson } = useCheckin();
const scrollContainer = ref(null);
const pageNumber = ref(1);
let hasMoreData = ref(true);

const handleCreateCheckin = () => {
  isCloseModel.value = true;
};

const isClose = (value: boolean) => {
  isCloseModel.value = value;
};

const getListWorkEffortsV2 = async (
  performerId: string,
  workEffortTypeId: string,
  source: string,
  pageNumber: number,
  pageSize: number
) => {
  if (!hasMoreData.value) return;

  const $sdk = useNuxtApp().$sdk;
  const sorts = { key: "createdStamp", asc: false };

  try {
    const response = await $sdk.crm.getWorkEfforts(
      performerId,
      workEffortTypeId,
      source,
      pageNumber,
      pageSize,
      sorts,
      {}  
    );

    if (response.data.length === 0) {
      hasMoreData.value = false;
    } else {
      response.data.forEach((item: any) => {
        checkinStore.addCheckin(item);
      });
    }
  } catch (error) {
    hasMoreData.value = false;

    console.error("Error fetching work efforts:", error);
  }
};

onMounted(async () => {
  await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);
});

useInfiniteScroll(
  scrollContainer,
  async () => {
    if (hasMoreData.value) {
      await getListWorkEffortsV2(
        auth.user.id,
        "SALE_POINT_MARKETING",
        "",
        ++pageNumber.value,
        10
      );
    }
  },
  {
    distance: 130,
  }
);
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/dms`) {
    await getListWorkEfforts(auth.user.id, "SALE_POINT_MARKETING", "", 0, 10);
  }
  next();
});
</script>
