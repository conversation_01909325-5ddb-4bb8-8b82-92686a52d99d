<template>
  <Teleport to="body">
    <Transition name="fade">
      <div
        v-if="isOpen"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-20 z-50"
        @click.self="closeModal"
      >
        <Transition name="scale">
          <div
            v-if="isOpen"
            ref="modalRef"
            class="bg-white w-full max-w-md p-6 rounded-lg shadow-lg"
          >
            <h2 class="text-lg font-semibold mb-4">X<PERSON>c nhận thanh toán</h2>

            <!-- Chọn nhân viên -->
            <div class="mb-4" v-if="!isEmployee">
              <label class="block text-sm font-medium mb-1"
                >Nhân viên xác nhận</label
              >
              <select
                v-model="selectedEmployee"
                class="w-full border rounded px-3 py-2 outline-none"
              >
                <option
                  v-for="employee in employees"
                  :key="employee.id"
                  :value="employee.id"
                >
                  {{ employee.name }}
                </option>
              </select>
            </div>

            <!-- Nhập mã giao dịch -->
            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Mã giao dịch</label>
              <input
                ref="inputRef"
                v-model="transactionCode"
                class="w-full border rounded px-3 py-2 outline-none"
                placeholder="Nhập mã giao dịch"
              />
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Ghi chú</label>
              <input
                v-model="note"
                class="w-full border rounded px-3 py-2 outline-none"
                placeholder="Nhập ghi chú"
              />
            </div>
            <!-- Nút xác nhận & đóng -->
            <div class="flex justify-end space-x-2">
              <button class="px-4 py-2 border rounded" @click="closeModal">
                Đóng
              </button>
              <button
                class="px-4 py-2 bg-primary text-white rounded shadow-md transition"
                @click="confirmPayment"
              >
                Xác nhận
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch, computed, nextTick } from "vue";
import { useMyEmployeeStore } from "@/stores/employee";
import { useAuthStore } from "@/stores/auth";
import { storeToRefs } from "pinia";

const employeeStore = useMyEmployeeStore();
const authStore = useAuthStore();
const { employees } = storeToRefs(employeeStore);
const { user } = storeToRefs(authStore);

const props = defineProps<{ isOpen: boolean; isEmployee: boolean }>();
const emit = defineEmits(["update:isOpen", "confirm"]);

const selectedEmployee = ref("");
const transactionCode = ref("");
const note = ref("");
const inputRef = ref<HTMLInputElement | null>(null);

const userId = computed(() => user.value?.id ?? "");

onMounted(async () => {
  if (props.isOpen) {
    await nextTick();
    inputRef.value?.focus();

    if (user.value && employees.value.length) {
      const matchingEmployee = employees.value.find(
        (emp) => emp.id === userId.value
      );

      if (matchingEmployee) {
        selectedEmployee.value = matchingEmployee.id;
      }
    }
  }
});
const closeModal = () => emit("update:isOpen", false);

const confirmPayment = () => {
  if (props.isEmployee) {
    emit("confirm", {
      employee: selectedEmployee.value,
      transactionCode: transactionCode.value,
      note: note.value,
    });
    closeModal();
    return;
  }
  if (selectedEmployee.value) {
    emit("confirm", {
      employee: selectedEmployee.value,
      transactionCode: transactionCode.value,
      note: note.value,
    });
    closeModal();
  }
};
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.scale-enter-active {
  transition: transform 0.2s ease-out;
}
.scale-enter-from {
  transform: scale(0.9);
}
</style>
