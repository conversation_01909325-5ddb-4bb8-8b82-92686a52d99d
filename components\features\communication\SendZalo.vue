<template>
  <div class="">
    <div class="flex items-center gap-1">
      <div
        @click="handleOpenZnsPopUp('SEND_ORDER_RATE')"
        class="border border-primary text-primary p-1 rounded cursor-pointer"
        :class="
          orderDetail?.status === 'CANCELLED'
            ? 'opacity-50 pointer-events-none'
            : ''
        "
        v-tippy="'Gửi đánh giá'"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
            />
          </svg>
        </span>
      </div>
      <img
        @click="handleOpenZnsPopUp('SEND_ORDER_INFO')"
        src="~/assets/images/zalo.jpg"
        alt="Zalo"
        class="zalo-icon cursor-pointer w-[25.6px] h-[25.6px] border rounded border-primary"
        v-tippy="'Gửi đơn hàng'"
        loading="lazy"
      />
      <div
        v-if="settingOrg?.isExportInvoice"
        @click="toogleExportInvoice"
        class="border border-primary text-primary p-1 rounded cursor-pointer"
        :class="
          orderDetail?.activeOrderItemProfiles.length >= 0 &&
          orderDetail?.remainTotal === 0
            ? ''
            : 'opacity-50 pointer-events-none'
        "
        v-tippy="'Xuất hóa đơn'"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
          />
        </svg>
      </div>
    </div>
  </div>
  <SendVoucher
    v-if="isOpenTopicPopup"
    @cancel="handleOpenTopicPopUp"
    @confirm="handleSendMessage"
  ></SendVoucher>
  <ZaloZns
    v-if="isOpenZnsPopup"
    @cancel="handleOpenZnsPopUp"
    @confirm="handleSendZns"
    :orderDetail="orderDetail"
    :typeSendZns="typeSendZns"
  ></ZaloZns>
  <ExportInvoicePopup
    v-if="isOpenExportInvoice"
    :order="orderDetail"
    @confirm="toogleExportInvoice"
    @cancel="toogleExportInvoice"
  ></ExportInvoicePopup>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
</template>
<script setup lang="ts">
import "tippy.js/dist/tippy.css";
const ZaloZns = defineAsyncComponent(
  () => import("~/components/features/communication/ZaloZns.vue")
);
const SendVoucher = defineAsyncComponent(
  () => import("~/components/ui/feedback/SendVoucher.vue")
);
const isOpenTooltip = ref(false);
const props = defineProps(["orderDetail", "payment"]);
const { shareOrder, sendMessage } = useComhub();
const handleOpenTooltip = () => {
  console.log("danh sách khách hàng là ");
  console.log("orderDetail", props.orderDetail);
  isOpenTooltip.value = !isOpenTooltip.value;
};
// gửi zns
const isOpenZnsPopup = ref(false);
const typeSendZns = ref();

const handleOpenZnsPopUp = (type: string) => {
  if (props.orderDetail?.order?.ownerPartyId) {
    isOpenZnsPopup.value = !isOpenZnsPopup.value;
    typeSendZns.value = type;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const isLoading = ref(false);

const handleSendZns = async (data: any) => {
  try {
    isLoading.value = true;
    const response = await shareOrder(
      data?.templateData,
      data?.app?.apps[0]?.id,
      data?.templateType
    );
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    throw error;
  }
  // call api ở đây
  isOpenTooltip.value = false;
  isOpenZnsPopup.value = false;
};
////
const isOpenTopicPopup = ref(false);
const handleOpenTopicPopUp = () => {
  if (props.orderDetail?.order?.ownerPartyId) {
    isOpenTopicPopup.value = !isOpenTopicPopup.value;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const handleSendMessage = async (message: string) => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  const zaloId = appId.find((app: any) => app.name === "ZNS");
  if (!zaloId) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  if (!props.orderDetail?.order?.ownerPartyId) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    isOpenTopicPopup.value = false;
    return;
  }

  try {
    await sendMessage(zaloId?.id, message, "TEXT", "SYSTEM", [
      props.orderDetail?.order?.ownerPartyId,
    ]);
  } catch (error) {
    console.error("Error sending message:", error);
  }
  isOpenTooltip.value = false;
  isOpenTopicPopup.value = false;
};
const isOpenExportInvoice = ref(false);
const auth = useCookie("auth") as any;
const { data } = await useFetch("/data/setting.json");
const settingOrg = computed(() => {
  const dataSettingOrg = data.value as any;
  return dataSettingOrg?.find((org: any) => org?.storeId === orgId.value);
});
const { storeId, orgId } = useTabContext();
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
</script>
