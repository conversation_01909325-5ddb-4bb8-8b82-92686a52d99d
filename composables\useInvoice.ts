import type { VatInvoiceRequestDTO } from "../types/Invoice";
export default function useOrder() {
  const { $sdk } = useNuxtApp();
  const requestUnpublishVatInvoice = async (
    VatInvoiceRequest: VatInvoiceRequestDTO,
    byUser: string
  ) => {
    try {
      const response = await $sdk.payment.requestUnpublishVatInvoice(
        VatInvoiceRequest,
        byUser
      );
      if (response?.code === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const requestPublishVatInvoice = async (
    VatInvoiceRequest: VatInvoiceRequestDTO,
    byUser: string
  ) => {
    try {
      const response = await $sdk.payment.requestPublishVatInvoice(
        VatInvoiceRequest,
        byUser
      );
      if (response?.code === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInvoicesOfOrder = async (orderId: string) => {
    try {
      const response = await $sdk.payment.getInvoicesOfOrder(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const viewPublishedInvoice = async (invoiceId: string) => {
    try {
      const response = await $sdk.payment.viewPublishedInvoice(invoiceId);
      if (response?.code === 1) {
        useNuxtApp().$toast.success(`${response?.message}`);
      } else {
        useNuxtApp().$toast.warning(`${response?.message}`);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateInvoiceItem = async (
    invoiceId: string,
    updateInvoiceItemDTO: any,
    byUser: string
  ) => {
    try {
      const response = await $sdk.payment.updateInvoiceItem(
        invoiceId,
        updateInvoiceItemDTO,
        byUser
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInvoiceItemOfInvoie = async (invoiceId: string) => {
    try {
      const response = await $sdk.payment.getInvoiceItemOfInvoie(invoiceId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    requestUnpublishVatInvoice,
    requestPublishVatInvoice,
    getInvoicesOfOrder,
    viewPublishedInvoice,
    updateInvoiceItem,
    getInvoiceItemOfInvoie,
  };
}
