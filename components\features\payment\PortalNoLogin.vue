<template>
  <div class="p-4 rounded-lg">
    <div v-if="!isHide" class="flex flex-col gap-2">
      <div
        v-for="item in filteredPaymentMethods"
        :key="item.code"
        class="border rounded-md p-4 flex gap-2 items-center cursor-pointer"
        :class="{ 'border-blue-600': selectedPaymentMethod === item.code }"
        :v-model="selectedPaymentMethod"
        @click="selectPaymentMethod(item)"
      >
        <img
          v-if="item.image"
          class="w-6 h-6 object-contain mr-2"
          :src="item.image"
          alt=""
          loading="lazy"
        />
        <span class="font-semibold text-base">{{ item.name }}</span>
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-between gap-2">
        <div class="flex items-center gap-2">
          <div @click="handleClickBack" class="hover:cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
              />
            </svg>
          </div>
          <div class="font-bold">
            <span class="text-primary">{{ itemName }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="dataPayment && dataPayment.qrCodeUrl && !isManual"
        class="font-bold mt-4 text-nowrap"
      >
        Vui lòng quét mã để thanh toán
      </div>

      <div v-if="loading" class="mt-4">
        <LoadingSpinner />
      </div>
      <div v-else>
        <div
          v-if="dataPayment && dataPayment.qrCodeUrl && !isManual"
          class="mx-2 mb-5 flex items-center justify-center"
        >
          <img
            :src="dataPayment.qrCodeUrl"
            alt="QR Code"
            class="w-56 h-56 md:w-72 md:h-72 object-contain"
            loading="lazy"
          />
        </div>
        <div v-else class="mx-2 w-full h-56 md:h-72"></div>
      </div>
      <div v-if="(selectedPaymentMethod = 'transfer')">
        <div v-for="bank in dataBank">
          <ItemBank :bank="bank" :isAndroid="isAndroid"></ItemBank>
        </div>
      </div>
    </div>
    <PaymentMessage
      v-if="isAlert"
      :title="'Xác nhận'"
      :message="`Nhấn đồng ý để hủy giao dịch đang chờ`"
      :data="dataCancel"
      @confirm="confirmCancel"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import type { Auth } from "~/types/Auth";

const props = defineProps(["orderDetails", "dataPaymentMethod"]);
const emits = defineEmits(["changeLoading"]);
const app = useNuxtApp();

const {
  createPaymentOrder,
  getInvoiceDetail,
  cancelPayment,
  paymentsByOrders,
  getAndroidBank,
  getIosBank,
} = usePayment();
const selectedPaymentMethod = ref("");
const route = useRoute();
//   const auth = useCookie("auth").value as unknown as Auth;
const auth = useCookie("auth").value as unknown as Auth;
const { storeId,orgId } = useTabContext();

const dataPayment = ref();
const loading = ref<Boolean>(false);
const isManual = ref<Boolean>(false);
const received = ref<number>(0);
const changeAmount = ref<number>(0);
const isHide = ref<boolean>(false);
const itemName = ref<string>();
const itemCode = ref<string>();
const isCasso = ref<Boolean>(false);
const isAlert = ref<Boolean>(false);
const dataCancel = ref<any>();
const filteredPaymentMethods = computed(() => {
  return props.dataPaymentMethod.filter((item: any) => item.code !== "manual");
});

let orderId: any = route.query.orderId
  ? route.query.orderId
  : (route.query.partnerCode as any);
const paymentData = ref<any>();
const { fetchOrderDetails, updateStatusApproved } = useOrder();

const formattedReceived = computed({
  get() {
    return formatCurrency(received.value);
  },
  set(value: string) {
    received.value = +value.replace(/[^\d]/g, "");
  },
});
const handleClickBack = async () => {
  isHide.value = false;
  selectedPaymentMethod.value = "";
  dataPayment.value = {};
  isCasso.value = false;
  const data = await cancelPayment(paymentData.value, "");
};
const selectPaymentMethod = async (item: any) => {
  itemName.value = item.name;
  itemCode.value = item.code;

  if (item.code === "manual") {
    selectedPaymentMethod.value = item.code;
    isManual.value = true;
  } else {
    selectedPaymentMethod.value = item.code;
    const cancelpayment = await paymentsByOrders([orderId]);

    if (cancelpayment.length > 0) {
      cancelpayment.map(async (item: any) => {
        if (item.statusCode === "1") {
          isAlert.value = true;
          dataCancel.value = item;
        }
      });
    }
    if (isAlert.value) {
    } else {
      emits("changeLoading");
      if (item.code === "transfer") {
        isCasso.value = true;
        isHide.value = true;
      }
      isManual.value = false;
      const host = window.location.origin;
      const data = {
        orderId: orderId,
        paymentMethod: item.code,
        appliedAmount: 30000,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: "",
      };
      const payment = await createPaymentOrder(data);
      if (item.code !== "transfer") {
        window.location.href = payment?.data;
      }
      dataPayment.value = payment;
      const paymentId = await paymentsByOrders([orderId]);
      const lastPaymentId = paymentId[paymentId.length - 1];
      paymentData.value = lastPaymentId?.paymentId;
      loading.value = false;
      emits("changeLoading");
    }
  }
};
//
const confirmCancel = async () => {
  emits("changeLoading");
  await cancelPayment(dataCancel.value?.paymentId, "");
  loading.value = true;
  if (itemCode.value === "transfer") {
    isHide.value = true;
  }
  isManual.value = false;
  const host = window.location.origin;
  const data = {
    orderId: orderId,
    paymentMethod: itemCode.value,
    appliedAmount: props.orderDetails.data.remainTotal,
    payDate: Date.now(),
    source: "ORDER_SOURCE",
    returnUrl: `${host}/thanh-toan`,
    paymentType: "ONLINE",
    createBy: "",
  };
  const payment = await createPaymentOrder(data);
  if (itemCode.value !== "transfer") {
    window.location.href = payment?.data;
  }
  dataPayment.value = payment;
  console.log("dataPayment.value = ", dataPayment.value);
  const paymentId = await paymentsByOrders([orderId]);
  const lastPaymentId = paymentId[paymentId.length - 1];
  paymentData.value = lastPaymentId?.paymentId;
  loading.value = false;
  isAlert.value = false;
  emits("changeLoading");
};
const handleCancel = async () => {
  isAlert.value = false;
};
//
const updateReceived = (value: string) => {
  formattedReceived.value = value;
};

const handleClickPayment = async () => {
  if (received.value < props.orderDetails.data.remainTotal) {
    app.$toast.warning("Vui lòng nhập số tiền lớn hơn số tiền cần thanh toán");
  } else {
    const host = window.location.origin;
    const cancelpayment = await paymentsByOrders([orderId]);
    if (cancelpayment.length > 0) {
      const firstId = cancelpayment[cancelpayment.length - 1];
      await cancelPayment(firstId?.paymentId, "");
    }

    const data = {
      orderId: orderId,
      paymentMethod: "manual",
      // appliedAmount: props.orderDetails.data.remainTotal,
      payDate: Date.now(),
      source: "ORDER_SOURCE",
      returnUrl: `${host}/payment/success`,
      paymentType: "ONLINE",
      createBy: auth?.user?.id,
    };
    const payment = await createPaymentOrder(data);
    if (payment) {
      app.$toast.success("Thanh toán thành công");
      await updateStatusApproved(orderId);
      navigateTo(`/diaries?orgId=${orgId.value}&storeId=${storeId.value}`);
    } else {
      app.$toast.error("Thanh toán thất bại");
    }
  }
};
const calculateChange = () => {
  changeAmount.value = received.value - props.orderDetails.data.remainTotal;
  if (changeAmount.value < 0) {
    changeAmount.value = 0;
  }
};

watch(
  () => (route.query.orderId ? route.query.orderId : route.query.partnerCode),
  (newId, oldId) => {
    if (newId !== oldId) {
      orderId = newId;
      isHide.value = false;
      selectedPaymentMethod.value = "";
      dataPayment.value = {};
      isCasso.value = false;
    }
  }
);
watch(received, calculateChange);
//
// Detect platform
const value = ref();
const detectPlatform = () => {
  const userAgent = navigator?.userAgent;
  console.log(navigator.userAgent);

  if (/iPad|iPhone|iPod/.test(userAgent)) {
    return "iOS";
  }

  if (/android/i.test(userAgent)) {
    return "Android";
  }

  if (/windows/i.test(userAgent)) {
    return "Windows";
  }

  return "unknown";
};
// Use platform detection
const dataBank = ref();
const isAndroid = ref(false);
const handleDevice = async () => {
  const platform = detectPlatform();

  if (platform === "Android") {
    console.log("Thiết bị đang sử dụng Android");
    isAndroid.value = true;
    dataBank.value = await getAndroidBank();
    value.value = "Android";
  } else if (platform === "iOS") {
    value.value = "iOS";
    isAndroid.value = false;
    dataBank.value = await getIosBank();
    console.log("Thiết bị đang sử dụng iOS");
  } else if (platform === "Windows") {
    value.value = "Windows";
    dataBank.value = null;
    console.log("Thiết bị đang sử dụng Windows");
  } else {
    value.value = "#";
    dataBank.value = null;
    console.log("Thiết bị không được hỗ trợ");
  }
};
onMounted(() => {
  handleDevice();
});
</script>
