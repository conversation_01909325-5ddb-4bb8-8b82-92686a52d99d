<template>
  <!-- Mo<PERSON> Backdrop with Blur Effect -->
  <Transition
    name="modal"
    enter-active-class="transition-all duration-300 ease-out"
    leave-active-class="transition-all duration-200 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[100] p-4 text-sm"
    >
      <!-- Modal Container -->
      <Transition
        name="modal-content"
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-200 ease-in"
        enter-from-class="opacity-0 scale-95 translate-y-4"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-95 translate-y-4"
      >
        <div
          class="bg-white w-full max-w-3xl shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-300 ease-in-out max-h-[90vh] flex flex-col"
        >
          <!-- Header -->
          <div class="bg-primary px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <button
                @click="handleCloseModal"
                class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 group"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 transform group-hover:-translate-x-1 transition-transform duration-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                <span class="text-sm font-medium">Trở lại</span>
              </button>
            </div>

            <h2
              class="text-xl font-bold text-white flex items-center space-x-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
              <span>Quản lý địa chỉ giao hàng</span>
            </h2>

            <button
              @click="handleShowForm"
              class="bg-white text-primary px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center space-x-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <span>Thêm địa chỉ</span>
            </button>
          </div>

          <!-- Content -->
          <div class="flex-1 overflow-y-auto">
            <div class="p-6">
              <!-- Address List -->
              <div
                v-if="orderStore.dataShippingAddress?.length"
                class="space-y-4"
              >
                <div
                  class="text-sm text-gray-600 mb-4 flex items-center space-x-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span
                    >Tổng cộng {{ orderStore.dataShippingAddress.length }} địa
                    chỉ</span
                  >
                </div>

                <!-- Use ShippingAddress component directly for each address -->
                <div class="space-y-3">
                  <ShippingAddress
                    v-for="shippingAddress in orderStore.dataShippingAddress"
                    :key="shippingAddress.id"
                    :shippingAddress="shippingAddress"
                    :customer="customer"
                    :isPageSale="isPageSale"
                    class="transform transition-all duration-200 hover:scale-[1.01]"
                  />
                </div>
              </div>

              <!-- Empty State -->
              <div v-else class="text-center py-12">
                <div
                  class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">
                  Chưa có địa chỉ giao hàng
                </h3>
                <p class="text-gray-600 mb-6">
                  Bạn chưa có địa chỉ nhận hàng nào. Hãy thêm địa chỉ đầu tiên
                  để tiếp tục.
                </p>
                <button
                  @click="handleShowForm"
                  class="bg-primary text-white px-6 py-2 rounded-lg font-semibold hover:bg-primary/90 transition-colors duration-200 flex items-center space-x-2 mx-auto"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  <span>Thêm địa chỉ đầu tiên</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>

  <!-- Create Address Modal -->
  <ModalCreateShippingAddress
    v-if="isShowForm"
    @closeModalCreateShippingAddress="closeModalCreateShippingAddress"
    :customer="customer"
    :isPageSale="true"
  />
</template>

<script setup>
// Props & Emits
const { customer, isPageSale } = defineProps(["customer", "isPageSale"]);
const emits = defineEmits(["closeModalManageAddressShip"]);

// Composables
const orderStore = useOrderStore();

// Reactive Data
const isShowForm = ref(false);
const isLoading = ref(false);

// Methods
const handleShowForm = () => {
  isShowForm.value = true;
};

const handleCloseModal = () => {
  emits("closeModalManageAddressShip", false);
};

const closeModalCreateShippingAddress = (value) => {
  isShowForm.value = value;
  // Refresh address list after creating new address
  if (!value) {
    refreshAddressList();
  }
};

const refreshAddressList = async () => {
  if (customer?.id) {
    isLoading.value = true;
    try {
      await orderStore.getDataShippingAddress(customer.id);
    } catch (error) {
      console.error("Error refreshing address list:", error);
      useNuxtApp().$toast?.error("Không thể tải danh sách địa chỉ");
    } finally {
      isLoading.value = false;
    }
  }
};

// Lifecycle
onMounted(() => {
  refreshAddressList();
});
</script>

<style scoped>
/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes for transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(1rem);
}

/* Address card hover effects */
.group:hover .bg-gray-50 {
  background-color: #f8fafc;
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
