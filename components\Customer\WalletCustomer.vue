<template>
  <div
    class="col-span-3 md:col-span-2 bg-white border rounded-lg p-2 text-sm overflow-y-auto max-h-[300px]"
  >
    <div class="flex flex-row justify-between items-center">
      <h2 class="text-lg font-semibold text-primary mb-2">
        Thông tin ví khách hàng
      </h2>
    </div>

    <div v-if="!wallet" class="flex items-center justify-center mx-6 py-1 mt-4">
      Khách hàng chưa tạo ví
    </div>
    <div v-else>
      <div>
        <!-- mã ví + time -->

        <div class="flex items-center justify-between">
          <div>
            <span class="font-semibold">Mã ví: </span>
            <span class="">{{ wallet?.details?.data?.accountNumber }}</span>
          </div>
          <div>
            <span class="font-semibold">Ngày tạo: </span>
            <span>{{ formatTimestampV2(wallet?.details?.data?.created) }}</span>
          </div>
        </div>
        <div>
          <span class="font-semibold">Số dư: </span>
          <span class="text-primary font-bold">{{
            formatCurrency(wallet?.details?.data?.balance || 0)
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps(["wallet"]);
</script>
