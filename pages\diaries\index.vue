<template>
  <div class="h-[calc(100vh-56px)] flex flex-col mx-2">
    <!-- Header -->
    <div class="flex-shrink-0 mb-2">
      <SearchDiary class="mb-2 md:mb-0" @handleSearch="handleSearch" />
      <div v-if="isAlert" class="text-sm text-red-500 mt-2">
        Không tìm thấy đơn hàng
      </div>
    </div>
    <!-- Desktop Table Container -->
    <div class="hidden md:block flex-1 min-h-0">
      <div
        class="h-full flex flex-col bg-white rounded-lg border border-gray-200"
      >
        <div v-if="diaries?.length" class="flex-1 overflow-hidden">
          <div
            class="h-full overflow-y-auto"
            style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
          >
            <table class="table-auto w-full text-sm">
              <thead class="sticky top-0 z-10 bg-blue-100">
                <tr class="text-left font-semibold">
                  <th class="p-3 w-1/12 text-center">M<PERSON> đơn</th>
                  <th class="p-3 w-2/12">Khách hàng</th>
                  <th class="p-3 w-2/12">Nhật ký</th>
                  <th class="p-3 w-2/12">Nhân viên</th>
                  <th class="p-3 w-5/12 max-w-0">Sản phẩm quan tâm</th>
                  <th class="p-3 w-auto text-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="size-5 mx-auto"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                      />
                    </svg>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="diary in diaries"
                  :key="diary.id"
                  class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white border-b border-gray-100"
                >
                  <TableDiary
                    :diary="diary"
                    :data="data"
                    @handleLoading="toggleLoading"
                  />
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Desktop Empty State -->
        <div v-else class="flex items-center justify-center h-full">
          <div class="text-center py-12">
            <svg
              class="w-12 h-12 text-gray-400 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              Chưa có nhật ký nào
            </h3>
            <p class="text-gray-500">
              Hiện tại chưa có nhật ký bán hàng nào được tạo
            </p>
          </div>
        </div>

        <!-- Desktop Pagination -->
        <div
          v-if="diaries?.length"
          class="flex-shrink-0 p-4 bg-white border-t border-gray-200"
        >
          <DiaryPagination
            :current-page="pagination.currentPage"
            :total-pages="pagination.totalPages"
            :total-items="pagination.totalItems"
            :items-per-page="pagination.itemsPerPage"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- Mobile Cards Container -->
    <div class="md:hidden flex-1 min-h-0 overflow-hidden">
      <div class="h-full flex flex-col">
        <!-- Mobile Cards List -->
        <div
          class="flex-1 overflow-y-auto overflow-x-hidden py-2"
          style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
        >
          <div v-if="diaries.length > 0" class="space-y-3">
            <CardDiary
              v-for="diary in diaries"
              :key="diary.id"
              :diary="diary"
              :data="data"
              @handleLoading="toggleLoading"
            />
          </div>

          <!-- Mobile Empty State -->
          <div v-else class="flex items-center justify-center h-full">
            <div class="text-center py-12">
              <svg
                class="w-12 h-12 text-gray-400 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Chưa có nhật ký nào
              </h3>
              <p class="text-gray-500 mb-4">
                Hiện tại chưa có nhật ký bán hàng nào được tạo
              </p>
            </div>
          </div>
        </div>

        <!-- Mobile Pagination -->
        <div
          v-if="diaries.length > 0"
          class="flex-shrink-0 p-4 bg-white border-t border-gray-200"
          style="min-height: 70px"
        >
          <div class="flex items-center justify-center gap-2">
            <button
              @click="handlePageChange(pagination.currentPage - 1)"
              :disabled="pagination.currentPage <= 1"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
            >
              Trước
            </button>
            <span class="px-3 py-2 text-sm text-gray-700">
              {{ pagination.currentPage }} / {{ pagination.totalPages }}
            </span>
            <button
              @click="handlePageChange(pagination.currentPage + 1)"
              :disabled="pagination.currentPage >= pagination.totalPages"
              class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
            >
              Sau
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, defineAsyncComponent } from "vue";

// Async components
const CardDiary = defineAsyncComponent(() =>
  import("~/components/Diary/CardDiary.vue")
);
const TableDiary = defineAsyncComponent(() =>
  import("~/components/Diary/TableDiary.vue")
);
const SearchDiary = defineAsyncComponent(() =>
  import("~/components/Diary/SearchDiary.vue")
);
const LoadingSpinner = defineAsyncComponent(() =>
  import("~/components/common/LoadingSpinner.vue")
);

// Import Pagination component directly to avoid issues
import DiaryPagination from "~/components/common/Pagination.vue";

// SEO
useHead({
  title: "Nhật ký bán hàng",
  meta: [{ name: "description", content: "Bán hàng" }],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Nhật ký bán hàng",
});

// Stores and composables
const orderStore = useOrderStore();
const loading = computed(() => orderStore.loading);
const { fetchListSellOrder } = useOrder();

// State
const isLoading = ref(false);
const isAlert = ref(false);
const loadingNavigate = ref(false);
const diaries = ref([]);
const dataPagination = ref();

// Search options
const options = reactive({
  source: "diary",
  currentPage: 1,
  maxResult: 10,
  date_create_to: "",
  date_create_from: "",
  keyword: "",
});

// Pagination
const pagination = reactive({
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0,
  totalPages: 1,
});
// Methods
const loadDiaries = async (requestData) => {
  try {
    isLoading.value = true;
    isAlert.value = false;

    const response = await fetchListSellOrder({
      ...requestData,
      currentPage: pagination.currentPage,
    });
    console.log("response", response);
    dataPagination.value = response?.data;
    diaries.value = response.data?.data || [];

    // Update pagination info
    if (response.data?.total) {
      pagination.totalItems = response.data.total;
      pagination.totalPages = Math.ceil(
        response.data.total / pagination.itemsPerPage
      );
    } else {
      // Fallback calculation if total not provided
      pagination.totalItems = diaries.value.length;
      pagination.totalPages =
        diaries.value.length > 0
          ? Math.max(
              1,
              Math.ceil(diaries.value.length / pagination.itemsPerPage)
            )
          : 1;
    }

    if (diaries.value.length === 0) {
      isAlert.value = true;
    }
  } catch (error) {
    console.error("Error loading diaries:", error);
    isAlert.value = true;
    diaries.value = [];
  } finally {
    isLoading.value = false;
  }
};

// Event handlers
const handleSearch = async (data) => {
  // Reset to first page when searching
  pagination.currentPage = 1;
  options.currentPage = 1;
  options.date_create_to = data?.date_create_to;
  options.date_create_from = data?.date_create_from;
  options.keyword = data?.keyword;

  await loadDiaries(options);
};

const handlePageChange = async (page) => {
  if (
    page >= 1 &&
    page <= pagination.totalPages &&
    page !== pagination.currentPage
  ) {
    pagination.currentPage = page;
    options.currentPage = page;
    await loadDiaries(options);
  }
};

const toggleLoading = (state) => {
  loadingNavigate.value = state;
};

// Lifecycle
onMounted(async () => {
  await loadDiaries(options);
});

// Static data
const { data } = await useFetch("/data/setting.json");
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Table optimizations */
.table-auto {
  table-layout: auto;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }
}
</style>
