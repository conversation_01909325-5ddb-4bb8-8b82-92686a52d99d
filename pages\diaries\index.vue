<template>
  <div class="h-full mx-2">
    <div class="mt-2">
      <SearchDiary
        class="mb-2 md:mb-0"
        @handleSearch="handleSearch"
      ></SearchDiary>
      <div v-if="isAlert" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </div>
    </div>
    <LoadingDiary v-if="isLoading"></LoadingDiary>
    <div v-if="loading || loadingCreateOrder || loadingNavigate">
      <LoadingSpinner />
    </div>
    <div class="border-b-gray-200 relative">
      <div
        v-if="diaries?.length"
        class="flex flex-col gap-2 h-[90vh] overflow-y-scroll mb-4"
        ref="scrollContainer"
      >
        <div class="bg-white md:block hidden">
          <table class="table-auto w-full text-sm">
            <thead class="sticky top-0 z-2">
              <tr class="bg-blue-100 text-left font-semibold">
                <th class="p-2 w-1/12 text-center"><PERSON><PERSON> đơn</th>

                <th class="p-2 w-2/12"><PERSON>h<PERSON><PERSON> hàng</th>
                <th class="p-2 w-2/12">Nhật ký</th>
                <th class="p-2 w-2/12">Nhân viên</th>
                <th class="p-2 w-5/12 max-w-0">Sản phẩm quan tâm</th>
                <th class="p-2 w-full">
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="size-5"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                      />
                    </svg>
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="diary in diaries"
                :key="diary.id"
                class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
              >
                <TableDiary
                  :diary="diary"
                  :data="data"
                  @handleLoading="toggleLoading"
                ></TableDiary>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="block md:hidden">
          <CardDiary
            v-for="diary in diaries"
            :diary="diary"
            :data="data"
            @handleLoading="toggleLoading"
          />
        </div>
        <div class="mb-[40px]"></div>

        <LoadingScrollDiary v-if="isLoadingMore"></LoadingScrollDiary>
      </div>
      <div v-if="!diaries?.length">
        <div
          class="flex items-center justify-center font-bold text-primary bg-white h-screen"
        >
          Hiện tại chưa có nhật ký!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const CardDiary = defineAsyncComponent(() =>
  import("~/components/Diary/CardDiary.vue")
);
const TableDiary = defineAsyncComponent(() =>
  import("~/components/Diary/TableDiary.vue")
);
import { ref, reactive, onMounted } from "vue";
import { useInfiniteScroll } from "@vueuse/core";
const orderStore = useOrderStore();
const diaryStore = useDiariesStore();
const loading = computed(() => orderStore.loading);
// const diaries = computed(() => diaryStore.diaries);
useHead({
  title: "Nhật ký bán hàng",
  meta: [{ name: "description", content: "Bán hàng" }],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Nhật ký bán hàng",
});
// new loading scroll
const { fetchListSellOrder, fetchDataEmployees } = useOrder();
const isLoading = ref(true);
const isAlert = ref(false);
const options = reactive({
  // status: [1],
  source: "diary",
  currentPage: 1,
  maxResult: 10,
});
const isLoadingMore = ref(false);
const hasMore = ref(true);
const diaries = ref([]);
const handleGetDiary = async (data) => {
  diaries.value = [];
  try {
    const response = await fetchListSellOrder(data);
    diaries.value = response.data.data;
  } catch (error) {
    throw error;
  }
};
const addDiaries = async (data) => {
  if (!Array.isArray(diaries.value)) {
    diaries.value = [];
  }
  diaries.value = [...diaries.value, ...data];
};
onMounted(async () => {
  // await diaryStore.handleGetDataEmployee();
  await fetchDiaries();
});
const fetchDiaries = async () => {
  isAlert.value = false;
  isLoading.value = true;
  await handleGetDiary(options);
  if (diaries.value?.length === 0) {
    isAlert.value = true;
  } else {
    isAlert.value = false;
  }
  isLoading.value = false;
};
const scrollContainer = ref(null);

useInfiniteScroll(
  scrollContainer,
  async () => {
    if (!hasMore.value) return;

    isLoadingMore.value = true;
    options.currentPage += 1;
    await fetchDiariesV2();
  },
  {
    distance: 100,
  }
);
const isLoadingPage = ref(false);
const handleSearch = async (data) => {
  isLoadingPage.value = true;
  options.currentPage = 1;
  options.date_create_to = data?.date_create_to;
  options.date_create_from = data?.date_create_from;
  options.keyword = data?.keyword;
  await fetchDiaries();
  isLoadingPage.value = false;
};
const loadingNavigate = ref(false);
const toggleLoading = (state) => {
  loadingNavigate.value = state;
};
const fetchDiariesV2 = async () => {
  if (!hasMore.value) return;

  isLoadingMore.value = true;
  try {
    const res = await fetchListSellOrder(options);
    if (res.data?.data?.length) {
      addDiaries(res.data.data);
    } else {
      hasMore.value = false; // Không còn dữ liệu mới
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    hasMore.value = false; // Ngừng gọi API khi có lỗi
  } finally {
    isLoadingMore.value = false;
  }
};
const { data } = await useFetch("/data/setting.json");
</script>

<style scoped>
.h-full {
  height: 100%;
}
</style>
