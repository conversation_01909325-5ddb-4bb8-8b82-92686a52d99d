<template>
  <div class="h-full flex flex-col">
    <!-- Header với search -->
    <div class="flex-shrink-0 mx-2 mt-2">
      <SearchDiary
        class="mb-2 md:mb-0"
        @handleSearch="handleSearch"
      ></SearchDiary>
      <div v-if="isAlert" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </div>
    </div>

    <!-- Loading states -->
    <div v-if="loading || loadingCreateOrder || loadingNavigate">
      <LoadingSpinner />
    </div>

    <!-- Main content area -->
    <div class="flex-1 flex flex-col min-h-0 mx-2">
      <!-- Loading state -->
      <LoadingDiaryTable v-if="isLoading" :rows="10" />

      <!-- Error state -->
      <ErrorState
        v-else-if="hasError"
        title="Không thể tải nhật ký"
        message="Đ<PERSON> x<PERSON>y ra lỗi khi tải danh sách nhật ký. Vui lòng thử lại."
        help-text="Nếu lỗi vẫn tiếp tục, vui lòng liên hệ bộ phận hỗ trợ."
        @retry="handleRetry"
      />

      <!-- Content with data -->
      <div v-else-if="diaries?.length" class="flex-1 flex flex-col">
        <!-- Desktop Table -->
        <div
          class="bg-white md:block hidden flex-1 overflow-hidden rounded-lg border border-gray-200"
        >
          <div class="overflow-auto h-full">
            <table class="table-auto w-full text-sm">
              <thead class="sticky top-0 z-10 bg-blue-100">
                <tr class="text-left font-semibold">
                  <th class="p-3 w-1/12 text-center border-b border-gray-200">
                    Mã đơn
                  </th>
                  <th class="p-3 w-2/12 border-b border-gray-200">
                    Khách hàng
                  </th>
                  <th class="p-3 w-2/12 border-b border-gray-200">Nhật ký</th>
                  <th class="p-3 w-2/12 border-b border-gray-200">Nhân viên</th>
                  <th class="p-3 w-5/12 max-w-0 border-b border-gray-200">
                    Sản phẩm quan tâm
                  </th>
                  <th class="p-3 w-auto border-b border-gray-200">
                    <span class="flex justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-5"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                        />
                      </svg>
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="diary in diaries"
                  :key="diary.id"
                  class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white border-b border-gray-100 last:border-b-0"
                >
                  <TableDiary
                    :diary="diary"
                    :data="data"
                    @handleLoading="toggleLoading"
                  ></TableDiary>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Cards -->
        <div class="block md:hidden flex-1 overflow-auto">
          <div class="space-y-3 pb-4">
            <CardDiary
              v-for="diary in diaries"
              :key="diary.id"
              :diary="diary"
              :data="data"
              @handleLoading="toggleLoading"
            />
          </div>
        </div>

        <!-- Pagination -->
        <div class="flex-shrink-0 mt-4">
          <Pagination
            :current-page="pagination.currentPage"
            :total-pages="pagination.totalPages"
            :total-items="pagination.totalItems"
            :items-per-page="pagination.itemsPerPage"
            @page-change="handlePageChange"
            @next-page="handleNextPage"
            @pre-page="handlePrevPage"
          />
        </div>
      </div>

      <!-- Empty state -->
      <div
        v-else-if="!isLoading"
        class="flex-1 flex items-center justify-center"
      >
        <div class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <svg
              class="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Chưa có nhật ký
          </h3>
          <p class="text-gray-500">
            Hiện tại chưa có nhật ký bán hàng nào được tạo.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Async components với optimized loading
const CardDiary = defineAsyncComponent({
  loader: () => import("~/components/features/diary/CardDiary.vue"),
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-32 rounded" }),
  errorComponent: () => h("div", "Error loading component"),
  delay: 200,
  timeout: 3000,
});

const TableDiary = defineAsyncComponent({
  loader: () => import("~/components/features/diary/TableDiary.vue"),
  loadingComponent: () =>
    h("tr", { class: "animate-pulse" }, [
      h("td", { class: "p-3", colspan: 6 }, [
        h("div", { class: "h-4 bg-gray-200 rounded" }),
      ]),
    ]),
  errorComponent: () => h("tr", [h("td", { colspan: 6 }, "Error loading row")]),
  delay: 200,
  timeout: 3000,
});

const Pagination = defineAsyncComponent({
  loader: () => import("~/components/shared/common/Pagination.vue"),
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-12 rounded" }),
  errorComponent: () => h("div", "Error loading pagination"),
  delay: 200,
  timeout: 3000,
});

const LoadingDiaryTable = defineAsyncComponent(() =>
  import("~/components/ui/feedback/LoadingDiaryTable.vue")
);

const ErrorState = defineAsyncComponent(() =>
  import("~/components/ui/feedback/ErrorState.vue")
);

import { ref, reactive, onMounted, computed } from "vue";

// Stores và composables
const orderStore = useOrderStore();
const loading = computed(() => orderStore.loading);

// Page meta
useHead({
  title: "Nhật ký bán hàng",
  meta: [{ name: "description", content: "Bán hàng" }],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Nhật ký bán hàng",
});

// Composables
const { fetchListSellOrder } = useOrder();

// Reactive state
const isLoading = ref(true);
const isAlert = ref(false);
const hasError = ref(false);
const diaries = ref([]);
const loadingNavigate = ref(false);

// Pagination state
const pagination = reactive({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 10,
});

// Search options
const options = reactive({
  source: "diary",
  currentPage: 1,
  maxResult: 10,
  date_create_to: null,
  date_create_from: null,
  keyword: null,
});

// API functions
const fetchDiaries = async (resetPage = false) => {
  if (resetPage) {
    options.currentPage = 1;
    pagination.currentPage = 1;
  }

  isAlert.value = false;
  hasError.value = false;
  isLoading.value = true;

  try {
    const response = await fetchListSellOrder({
      ...options,
      currentPage: pagination.currentPage,
    });

    diaries.value = response.data?.data || [];

    // Cập nhật pagination info
    // Giả sử API trả về total items, nếu không thì tính toán dựa trên response
    const totalItems =
      response.data?.total || response.data?.pagination?.total || 0;
    pagination.totalItems = totalItems;
    pagination.totalPages = Math.ceil(totalItems / pagination.itemsPerPage);

    // Nếu không có total từ API, ước tính dựa trên số items trả về
    if (!totalItems && diaries.value.length > 0) {
      // Nếu số items trả về ít hơn maxResult, có thể đây là trang cuối
      if (diaries.value.length < pagination.itemsPerPage) {
        pagination.totalPages = pagination.currentPage;
        pagination.totalItems =
          (pagination.currentPage - 1) * pagination.itemsPerPage +
          diaries.value.length;
      } else {
        // Ước tính có ít nhất thêm 1 trang
        pagination.totalPages = pagination.currentPage + 1;
        pagination.totalItems =
          pagination.currentPage * pagination.itemsPerPage;
      }
    }

    if (diaries.value.length === 0) {
      isAlert.value = true;
    } else {
      isAlert.value = false;
    }
  } catch (error) {
    console.error("Error fetching diaries:", error);
    hasError.value = true;
    isAlert.value = false;
  } finally {
    isLoading.value = false;
  }
};

// Retry function
const handleRetry = async () => {
  await fetchDiaries();
};

// Event handlers
const handleSearch = async (data) => {
  options.date_create_to = data?.date_create_to;
  options.date_create_from = data?.date_create_from;
  options.keyword = data?.keyword;
  await fetchDiaries(true); // Reset to page 1
};

const handlePageChange = async (page) => {
  if (
    page >= 1 &&
    page <= pagination.totalPages &&
    page !== pagination.currentPage
  ) {
    pagination.currentPage = page;
    await fetchDiaries();
  }
};

const handleNextPage = async () => {
  if (pagination.currentPage < pagination.totalPages) {
    pagination.currentPage += 1;
    await fetchDiaries();
  }
};

const handlePrevPage = async () => {
  if (pagination.currentPage > 1) {
    pagination.currentPage -= 1;
    await fetchDiaries();
  }
};

const toggleLoading = (state) => {
  loadingNavigate.value = state;
};

// Lifecycle
onMounted(async () => {
  await fetchDiaries();
});

// Static data
const { data } = await useFetch("/data/setting.json");
</script>

<style scoped>
/* Layout optimizations */
.h-full {
  height: 100%;
}

/* Custom scrollbar styles for webkit browsers */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Table optimizations */
.table-auto {
  table-layout: auto;
}

/* Prevent layout shift */
.table-auto th,
.table-auto td {
  contain: layout style;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Loading state optimizations */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Prevent FOUC (Flash of Unstyled Content) */
.bg-white {
  background-color: white;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

/* CSS containment for performance */
.flex-1 {
  contain: layout;
}
</style>
