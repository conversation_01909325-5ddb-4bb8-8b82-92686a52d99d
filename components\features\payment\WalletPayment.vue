<template>
  <div class="py-2">
    <div class="block md:hidden">
      <div class="flex items-center justify-between">
        <div class="text-primary font-bold">Ví thanh toán</div>
        <div @click="handleClickBack" class="cursor-pointer mr-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </div>
      </div>
    </div>
    <div v-if="paymentWallet" class="flex flex-col">
      <div class="flex items-center gap-2">
        <div class="text-primary font-bold">Tổng thanh toán:</div>
        <div class="text-xl font-bold">
          {{
            paymentAmount > 0
              ? formatCurrency(paymentAmount)
              : formatCurrency(orderDetails?.data?.remainTotal)
          }}
        </div>
      </div>
      <div class="md:h-[250px]">
        <div class="flex items-center gap-2">
          <div class="text-primary font-bold">Số dư ví sau thanh toán:</div>
          <div class="text-xl font-bold text-red-500">
            {{
              formatCurrency(
                paymentWallet?.balance -
                  (paymentAmount > 0
                    ? paymentAmount
                    : orderDetails?.data?.remainTotal) || 0
              )
            }}
          </div>
        </div>
      </div>
      <div class="flex items-center justify-end mt-1">
        <button
          @click="handlePaymentWallet"
          class="flex items-center justify-center bg-primary px-2 py-1 font-bold text-white rounded cursor-pointer"
        >
          Thanh toán
        </button>
      </div>
    </div>
    <div v-else>
      <div class="md:h-[300px] flex items-center justify-center">
        <div
          class="bg-red-100 md:w-[600px] text-center md:h-[200px] rounded flex items-center justify-center"
        >
          <div class="text-red-500">
            Chưa có ví thanh toán vui lòng chọn phương thức thanh toán khác
          </div>
        </div>
      </div>
    </div>
  </div>
  <LoadingSpinner v-if="isLoading" />
</template>
<script setup lang="ts">
import type { Auth } from "~/types/Auth";

const props = defineProps([
  "payment",
  "paymentMethod",
  "orderDetails",
  "paymentAmount",
  "paymentWallet",
]);
const emits = defineEmits(["backQr", "createManualSuccess"]);
const handleClickBack = () => {
  emits("backQr");
};
const app = useNuxtApp();
const auth = useCookie("auth").value as unknown as Auth;
const { createPaymentOrder, cancelPayment, paymentsByOrders } = usePayment();
const orderStore = useOrderStore();
const isLoading = ref(false);

const handlePaymentWallet = async () => {
  // nếu có ví
  if (props.paymentWallet) {
    if (
      props.paymentWallet?.balance <
      (props.paymentAmount > 0
        ? props.paymentAmount
        : props.orderDetails?.data?.remainTotal)
    ) {
      app.$toast.warning("Ví không đủ tiền vui lòng chọn PTTT khác");
    } else {
      isLoading.value = true;
      const host = window.location.origin;

      const data = {
        orderId: props.orderDetails?.data?.id,
        paymentMethod: "wallet",
        appliedAmount:
          props.paymentAmount > 0
            ? props.paymentAmount
            : props.orderDetails?.data?.remainTotal,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: auth?.user?.id,
      };
      const response = await createPaymentOrder(data);
      isLoading.value = false;
      if (response) {
        orderStore.paymentAmount = 0;
        localStorage.removeItem("paymentAmount");
        emits("createManualSuccess");
      }
    }
  } else {
    app.$toast.warning("Khách hàng chưa có ví vui lòng mở ví");
  }
};
</script>
