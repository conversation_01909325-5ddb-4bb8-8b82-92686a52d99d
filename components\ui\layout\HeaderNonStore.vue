<template>
  <header
    class="bg-[#1565C0] flex justify-between items-center px-4 h-14 fixed top-0 z-30 shadow-lg transition-all duration-300 ease-in-out w-full"
  >
    <!-- Left Section - Empty for non-store pages -->
    <div class="flex items-center space-x-4">
      <!-- Logo and Store Info -->
      <div class="flex items-center space-x-3">
        <!-- Logo -->
        <div
          class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 120 120"
            fill="none"
            class="w-5 h-5"
          >
            <path
              d="M15.0498 56.1001V78.5501C15.0498 101 24.0498 110 46.4998 110H73.4498C95.8998 110 104.9 101 104.9 78.5501V56.1001"
              stroke="#FAFAFA"
              stroke-width="6"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M59.9999 60C69.1499 60 75.8999 52.55 74.9999 43.4L71.6999 10H48.3499L44.9999 43.4C44.0999 52.55 50.8499 60 59.9999 60Z"
              stroke="#FAFAFA"
              stroke-width="6"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M91.55 60C101.65 60 109.05 51.8 108.05 41.75L106.65 28C104.85 15 99.85 10 86.75 10H71.5L75 45.05C75.85 53.3 83.3 60 91.55 60Z"
              stroke="#FAFAFA"
              stroke-width="6"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M28.1999 60C36.4499 60 43.8999 53.3 44.6999 45.05L45.7999 34L48.1999 10H32.9499C19.8499 10 14.8499 15 13.0499 28L11.6999 41.75C10.6999 51.8 18.0999 60 28.1999 60Z"
              stroke="#FAFAFA"
              stroke-width="6"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M60 85C51.65 85 47.5 89.15 47.5 97.5V110H72.5V97.5C72.5 89.15 68.35 85 60 85Z"
              stroke="#FAFAFA"
              stroke-width="6"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </div>

        <!-- Store Name -->
        <div v-if="dataDetailStore" class="min-w-0">
          <h1 class="text-lg font-bold text-white truncate">
            {{ dataDetailStore.name }}
          </h1>
          <p class="text-xs text-white/80">Hệ thống quản lý</p>
        </div>
      </div>
    </div>

    <!-- Center Section - Empty -->
    <div class="flex-1"></div>

    <!-- Right Section - User Menu -->
    <div class="flex items-center space-x-3">
      <!-- User Menu -->
      <div class="relative">
        <button
          v-if="user"
          @click="toggleShowSubmenu"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors duration-200 group"
        >
          <div
            class="w-6 h-6 bg-white/30 rounded-full flex items-center justify-center shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              fill="currentColor"
              viewBox="0 0 256 256"
              class="text-white"
            >
              <path
                d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
              ></path>
            </svg>
          </div>
          <div class="hidden lg:block text-left">
            <p class="text-sm font-medium text-white">{{ userName }}</p>
            <p class="text-xs text-white/80">{{ userRole }}</p>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4 text-white/80 group-hover:text-white transition-colors duration-200"
            :class="{ 'rotate-180': submenu }"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M19.5 8.25l-7.5 7.5-7.5-7.5"
            />
          </svg>
        </button>

        <!-- User Dropdown -->
        <Transition name="dropdown">
          <div
            v-show="submenu"
            class="absolute right-0 top-full mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200/50 py-2 z-50"
          >
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
              <p class="text-xs text-gray-500">{{ user?.email }}</p>
            </div>
            <button
              @click="logout()"
              class="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                viewBox="0 0 256 256"
                class="text-gray-400"
              >
                <path
                  d="M141.66,133.66l-40,40a8,8,0,0,1-11.32-11.32L116.69,136H24a8,8,0,0,1,0-16h92.69L90.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,141.66,133.66ZM192,32H136a8,8,0,0,0,0,16h56V208H136a8,8,0,0,0,0,16h56a16,16,0,0,0,16-16V48A16,16,0,0,0,192,32Z"
                ></path>
              </svg>
              <span>Đăng xuất</span>
            </button>
          </div>
        </Transition>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useAuthStore } from "@/stores/auth";

// Composables
const { logout } = useAuth();
const { getDetailStore } = useStore();

// State
const { storeId, orgId } = useTabContext();

const submenu = ref<boolean>(false);
const dataDetailStore = ref();

// Props (keeping for potential future use)
defineProps({
  isShowSidebar: Boolean,
});

// Auth store
const authStore = useAuthStore();

// Computed
const user = computed(() => authStore?.user);
const userName = computed(() => user.value?.name || "");
const userRole = computed(() => {
  const roles = user.value?.roles || [];
  if (roles.includes("ORG_ADMIN")) return "Quản trị viên";
  if (roles.includes("SALE_ADMIN")) return "Quản lý bán hàng";
  if (roles.includes("SALE")) return "Nhân viên bán hàng";
  return "Nhân viên";
});

// Functions
const toggleShowSubmenu = () => {
  submenu.value = !submenu.value;
};

// Lifecycle
onMounted(async () => {
  if (orgId.value && orgId.value !== "N/A") {
    const response = await getDetailStore();
    dataDetailStore.value = response;
  }
});
</script>

<style scoped>
/* Dropdown animation */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Focus styles for accessibility */
button:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
button,
.transition-colors {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .truncate {
    max-width: 150px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/20 {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .dropdown-enter-active,
  .dropdown-leave-active {
    transition: none;
  }
}
</style>
