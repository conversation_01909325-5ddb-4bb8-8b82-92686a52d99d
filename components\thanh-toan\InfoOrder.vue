<template>
  <div v-if="dataOrder" class="space-y-1">
    <!-- Order ID -->
    <div class="bg-gray-50 p-1 rounded">
      <div class="flex justify-between items-center">
        <span class="font-medium text-gray-600 text-xs">M<PERSON> đơn hàng</span>
        <span class="font-bold text-primary text-sm">#{{ dataOrder.id }}</span>
      </div>
    </div>

    <!-- Customer Info -->
    <div v-if="!isLinkPaymentId">
      <div class="flex items-center gap-1 py-0.5">
        <svg
          class="w-3 h-3 text-gray-500 flex-shrink-0"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
        <span class="text-xs text-gray-900 font-medium">{{
          dataOrder?.order?.ownerName || "Khách lẻ"
        }}</span>
      </div>
      <div class="flex items-center gap-1 py-0.5">
        <svg
          class="w-3 h-3 text-gray-500 flex-shrink-0"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
          />
        </svg>
        <span class="text-xs text-gray-900">{{
          dataOrder?.order?.ownerPhone || "Không có"
        }}</span>
      </div>
    </div>

    <!-- Products List -->
    <div class="bg-gray-50 rounded p-1">
      <h4 class="font-medium text-gray-700 text-xs mb-1">Sản phẩm</h4>
      <div class="space-y-1">
        <div
          v-for="product in dataOrder.activeOrderItemProfiles"
          :key="product.id"
          class="bg-white rounded p-1"
        >
          <ProductSimpleCard :product="product.orderLineItem" />
        </div>
      </div>
    </div>

    <!-- Payment Summary -->
    <div
      v-if="!isLinkPaymentId"
      class="space-y-2 pt-2 p-1 border-t border-gray-200"
    >
      <div class="flex items-center justify-between py-1">
        <span class="font-medium text-gray-600 text-sm">Tổng tiền:</span>
        <span class="font-bold text-primary">{{ formattedTotalPrice }}</span>
      </div>

      <div class="flex items-center justify-between py-1">
        <span class="font-medium text-gray-600 text-sm">Đã thanh toán:</span>
        <span class="font-bold text-green-600">{{
          formatCurrency(dataOrder.totalAlreadyPaid)
        }}</span>
      </div>

      <div class="flex items-center justify-between py-1">
        <span class="font-medium text-gray-600 text-sm">Trạng thái:</span>
        <span :class="paymentStatusClass">{{
          dataOrder.financialStatusDescription
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

const props = defineProps({
  dataOrder: Object,
  isLinkPaymentId: Boolean,
});

const isOpen = ref<Boolean>(false);

const statusClass = computed(() =>
  getOrderStatusClass(props.dataOrder?.status, "background")
);
const paymentStatusClass = computed(() =>
  getPaymentStatusClass(props.dataOrder?.financialStatusDescription, "simple")
);
const formattedTotalPrice = computed(() =>
  formatCurrency(props.dataOrder?.order?.totalPrice?.amount)
);

// Import utilities
import {
  getOrderStatusClass,
  getPaymentStatusClass,
} from "~/utils/statusHelpers";

</script>
