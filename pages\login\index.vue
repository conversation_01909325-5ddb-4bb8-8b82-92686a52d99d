<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4"
  >
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -inset-10 opacity-30">
        <div
          class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"
        ></div>
      </div>
    </div>

    <!-- Main Container -->
    <div
      class="relative w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 items-center"
    >
      <!-- Left Side - Branding & Info -->
      <div class="hidden lg:flex flex-col justify-center space-y-8 px-8">
        <!-- Lo<PERSON> & Brand -->
        <div class="space-y-6">
          <div class="flex items-center space-x-3">
            <div
              class="w-12 h-12 bg-primary rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-7 h-7 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                ></path>
              </svg>
            </div>
            <h1 class="text-3xl font-bold text-primary">POS Online</h1>
          </div>

          <div class="space-y-4">
            <h2 class="text-4xl font-bold text-gray-900 leading-tight">
              Chào mừng trở lại!
            </h2>
            <p class="text-lg text-gray-600 leading-relaxed">
              Đăng nhập để tiếp tục quản lý cửa hàng của bạn với hệ thống POS
              hiện đại và tiện lợi.
            </p>
          </div>
        </div>

        <!-- Features -->
        <div class="space-y-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-green-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <span class="text-gray-700">Quản lý bán hàng thông minh</span>
          </div>
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-blue-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <span class="text-gray-700">Báo cáo chi tiết theo ngày</span>
          </div>
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-purple-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <span class="text-gray-700">Đồng bộ đa thiết bị</span>
          </div>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="w-full max-w-md mx-auto lg:mx-0">
        <div
          class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8"
        >
          <!-- Mobile Logo -->
          <div
            class="lg:hidden flex items-center justify-center space-x-3 mb-8"
          >
            <div
              class="w-10 h-10 bg-primary rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                ></path>
              </svg>
            </div>
            <h1 class="text-2xl font-bold text-primary">POS Online</h1>
          </div>

          <!-- Form Header -->
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Đăng nhập</h2>
            <p class="text-gray-600">Nhập thông tin để truy cập hệ thống</p>
          </div>

          <!-- Login Form -->
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Username Field -->
            <div class="space-y-2">
              <label
                for="username"
                class="block text-sm font-semibold text-gray-700"
              >
                Tài khoản
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <input
                  id="username"
                  v-model="username"
                  type="text"
                  name="username"
                  autocomplete="username"
                  :class="[
                    'block w-full pl-10 pr-3 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                    errors.username
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-300 bg-white hover:border-gray-400',
                  ]"
                  placeholder="Nhập tài khoản của bạn"
                />
              </div>
              <Transition name="slide-down">
                <span
                  v-if="errors.username"
                  class="text-sm text-red-600 flex items-center space-x-1"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ errors.username }}</span>
                </span>
              </Transition>
            </div>

            <!-- Password Field -->
            <div class="space-y-2">
              <label
                for="password"
                class="block text-sm font-semibold text-gray-700"
              >
                Mật khẩu
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <input
                  id="password"
                  v-model="password"
                  :type="hiddenPassword ? 'text' : 'password'"
                  name="password"
                  autocomplete="current-password"
                  :class="[
                    'block w-full pl-10 pr-12 py-3 border rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200',
                    errors.password
                      ? 'border-red-300 bg-red-50'
                      : 'border-gray-300 bg-white hover:border-gray-400',
                  ]"
                  placeholder="Nhập mật khẩu của bạn"
                />
                <button
                  v-if="password"
                  type="button"
                  @click="togglePasswordVisibility"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <svg
                    v-if="hiddenPassword"
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                      clip-rule="evenodd"
                    ></path>
                    <path
                      d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"
                    ></path>
                  </svg>
                  <svg
                    v-else
                    class="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path
                      fill-rule="evenodd"
                      d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </button>
              </div>
              <Transition name="slide-down">
                <span
                  v-if="errors.password"
                  class="text-sm text-red-600 flex items-center space-x-1"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ errors.password }}</span>
                </span>
              </Transition>
            </div>

            <!-- Submit Button -->
            <div class="space-y-4">
              <button
                type="submit"
                :disabled="loading"
                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <span class="flex items-center space-x-2">
                  <span>Đăng nhập</span>
                  <svg
                    class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </span>
              </button>

              <!-- Forgot Password -->
              <div class="text-center">
                <button
                  type="button"
                  @click="goToForgotPassword"
                  class="text-sm text-primary hover:text-primary/80 font-medium transition-colors duration-200 hover:underline"
                >
                  Quên mật khẩu?
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Đăng nhập",
  meta: [
    {
      name: "description",
      content: "Đăng nhập POS",
    },
  ],
});
definePageMeta({
  middleware: ["login"],
});
const router = useRouter();
const {
  username,
  password,
  hiddenPassword,
  errors,
  handleSubmit,
  togglePasswordVisibility,
  loading,
} = useAuth();

const goToForgotPassword = () => {
  router.push("/quen-mat-khau");
};
</script>

<style scoped>
/* Slide down animation for error messages */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

/* Enhanced focus styles */
.focus\:ring-primary:focus {
  --tw-ring-color: #3f51b5;
}

/* Smooth transitions for all interactive elements */
button,
input {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom gradient background animation */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient-shift 10s ease infinite;
}
</style>
