<template>
  <div class="my-1 text-gray-500">{{ connector?.description }}</div>
  <div class="flex flex-wrap gap-2 pb-1">
    <span v-for="tag in dataTag" class="bg-secondary px-2 py-1 rounded text-sm">{{
      tag?.title
    }}</span>
  </div>
</template>
<script setup lang="ts">
const props = defineProps(["connector"]);
const { getTags } = usePortal();
const dataTag = ref();
const handleGetTag = async () => {
  try {
    if (props.connector?._id) {
      const response = await getTags(props.connector?._id);
      dataTag.value = response;
    }
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetTag();
});
</script>
