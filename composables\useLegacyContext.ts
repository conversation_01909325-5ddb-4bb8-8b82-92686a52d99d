/**
 * Legacy Context Migration Helper
 * 
 * This composable provides backward compatibility for components
 * that still use cookie-based orgId and storeId access.
 * 
 * @deprecated Use useTabContext() instead for new components
 */

import { computed } from 'vue';

export const useLegacyContext = () => {
  const { orgId, storeId, setOrgId, setStoreId } = useTabContext();

  // Provide cookie-like interface for backward compatibility
  const orgIdCookie = computed({
    get: () => orgId.value,
    set: async (value: string) => {
      await setOrgId(value);
      // Also update actual cookie for SSR compatibility
      const cookie = useCookie("orgId") as Ref<string>;
      cookie.value = value;
    }
  });

  const storeIdCookie = computed({
    get: () => storeId.value,
    set: async (value: string) => {
      await setStoreId(value);
      // Also update actual cookie for SSR compatibility
      const cookie = useCookie("storeId") as Ref<string>;
      cookie.value = value;
    }
  });

  return {
    orgId: orgIdCookie,
    storeId: storeIdCookie,
    // Expose tab context methods for migration
    setOrgId,
    setStoreId,
    // Migration status
    isUsingTabContext: true
  };
};

/**
 * Migration utility to convert cookie usage to tab context
 * 
 * Usage:
 * // Before:
 * const orgId = useCookie("orgId");
 * 
 * // After:
 * const { orgId } = useMigratedContext();
 */
export const useMigratedContext = () => {
  const { orgId, storeId, setOrgId, setStoreId, isOrgValid, isStoreValid } = useTabContext();

  // Provide reactive refs that behave like cookies
  const orgIdRef = computed({
    get: () => orgId.value,
    set: (value: string) => {
      setOrgId(value);
    }
  });

  const storeIdRef = computed({
    get: () => storeId.value,
    set: (value: string) => {
      setStoreId(value);
    }
  });

  return {
    orgId: orgIdRef,
    storeId: storeIdRef,
    isOrgValid,
    isStoreValid,
    setOrgId,
    setStoreId
  };
};

/**
 * Debug helper for tab context
 */
export const useTabContextDebug = () => {
  const { getContext, tabId } = useTabContext();

  const logContext = () => {
    const context = getContext();
    console.log('Tab Context Debug:', {
      tabId: context.tabId,
      orgId: context.orgId,
      storeId: context.storeId,
      sessionStorage: {
        [`${context.tabId}_orgId`]: sessionStorage.getItem(`${context.tabId}_orgId`),
        [`${context.tabId}_storeId`]: sessionStorage.getItem(`${context.tabId}_storeId`),
      },
      cookies: {
        orgId: useCookie("orgId").value,
        storeId: useCookie("storeId").value,
      }
    });
  };

  const exportContext = () => {
    const context = getContext();
    const data = {
      tabId: context.tabId,
      orgId: context.orgId,
      storeId: context.storeId,
      timestamp: new Date().toISOString()
    };
    
    // Store in localStorage for debugging across tabs
    localStorage.setItem('debug_tab_context', JSON.stringify(data));
    
    return data;
  };

  const importContext = (data: any) => {
    if (data && data.orgId && data.storeId) {
      const { setContext } = useTabContext();
      setContext(data.orgId, data.storeId);
      console.log('Context imported:', data);
    }
  };

  return {
    logContext,
    exportContext,
    importContext,
    tabId
  };
};
