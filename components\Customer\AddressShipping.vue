<template>
  <div
    class="col-span-3 md:col-span-2 bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 w-full max-w-full overflow-hidden"
  >
    <!-- Header Section -->
    <div
      class="flex flex-row justify-between items-center p-4 border-b border-gray-100 w-full min-w-0"
    >
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <div
          class="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </div>
        <h2 class="text-lg font-semibold text-gray-800 truncate">
          Địa chỉ giao hàng
        </h2>
        <div
          v-if="shippingAddress?.length"
          class="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full font-medium flex-shrink-0"
        >
          {{ shippingAddress.length }}
        </div>
      </div>

      <!-- Add Address Button -->
      <button
        @click="handleCreateShippingAddress"
        class="flex items-center space-x-2 bg-primary hover:bg-primary/90 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md flex-shrink-0"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
        <span>Thêm địa chỉ</span>
      </button>
    </div>

    <!-- Content Section -->
    <div
      class="p-4 w-full max-w-full overflow-hidden max-h-[300px] overflow-y-auto"
    >
      <!-- Address List -->
      <div v-if="shippingAddress?.length" class="space-y-3">
        <div
          v-for="shippingAddress in shippingAddress"
          :key="shippingAddress.id"
        >
          <ShippingAddress
            :shippingAddress="shippingAddress"
            :customer="customerStore.customer"
            class="w-full max-w-full"
          />
        </div>
      </div>
      <!-- Empty State - Enhanced UI -->
      <div v-else class="flex items-center justify-center">
        <div class="flex flex-col items-center space-y-1 text-center max-w-xs">
          <!-- Animated Icon -->
          <div class="relative">
            <div
              class="w-16 h-16 bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl flex items-center justify-center shadow-lg"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-primary animate-pulse"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            <!-- Decorative dots -->
            <div
              class="absolute -top-1 -right-1 w-3 h-3 bg-primary/30 rounded-full animate-bounce"
            ></div>
            <div
              class="absolute -bottom-1 -left-1 w-2 h-2 bg-primary/40 rounded-full animate-bounce"
              style="animation-delay: 0.5s"
            ></div>
          </div>

          <!-- Content -->
          <div class="space-y-2">
            <h3 class="text-base font-semibold text-gray-900">
              Chưa có địa chỉ giao hàng
            </h3>
            <p class="text-xs text-gray-600 leading-relaxed">
              Thêm địa chỉ để khách hàng có thể nhận hàng thuận tiện
            </p>
          </div>

          <!-- Helper text -->
          <p class="text-xs text-gray-400">💡 Nhấn nút "Thêm địa chỉ" ở trên</p>
        </div>
      </div>
    </div>
  </div>
  <ModalCreateShippingAddress
    v-if="isShowCreateShippingAddress"
    @closeModalCreateShippingAddress="closeModalCreateShippingAddress"
    :customer="customerStore.customer"
    :isManagerCustomer="true"
  ></ModalCreateShippingAddress>
</template>
<script setup lang="ts">
const customerStore = useCustomerStore();
const shippingAddress = computed(() => customerStore.shippingAddress);
// const customer = computed(() => {
//   customerStore.customer;
// });

const isShowCreateShippingAddress = ref(false);
const handleCreateShippingAddress = async () => {
  isShowCreateShippingAddress.value = true;
};
const closeModalCreateShippingAddress = (value: boolean) => {
  isShowCreateShippingAddress.value = value;
};
</script>
