import { defineStore } from "pinia";
import { ref } from "vue";

export const useDiariesStore = defineStore("diaries", () => {
  const diaries = ref<any>();
  const { fetchListSellOrder, fetchDataEmployees } = useOrder();
  const handleGetDiary = async (data: any) => {
    diaries.value = [];
    try {
      const response = await fetchListSellOrder(data);
      diaries.value = response.data.data;
    } catch (error) {
      throw error;
    }
  };
  const addDiaries = async (data: any) => {
    if (!Array.isArray(diaries.value)) {
      diaries.value = [];
    }
    diaries.value = [...diaries.value, ...data];
  };
  const dataEmployee = ref();
  const handleGetDataEmployee = async () => {
    try {
      const response = await fetchDataEmployees();
      dataEmployee.value = response;
    } catch (error) {
      throw error;
    }
  };
  const upDateCancelOrderStatus = (order: any, reason: string) => {
    const index = diaries.value.findIndex(
      (orders: any) => orders.id === order.id
    );
    if (index !== -1) {
      const updatedOrder = {
        ...diaries.value[index],
        status: "CANCELLED",
        statusDescription: "Đã hủy",
        order: {
          ...diaries.value[index].order, // Giữ lại các thông tin khác trong order
          note: reason, // Gán lý do hủy vào note của order
        },
      };
      // chọc đến order note đến và gán bằng reason

      diaries.value.splice(index, 1, updatedOrder);
    }
  };
  const updateQuantityPrintOrder = (order: any) => {
    const index = diaries.value.findIndex(
      (orders: any) => orders.id === order.id
    );

    if (index !== -1) {
      const customAttributes = diaries.value[index].order.customAttributes;
      const printTimesAttr = customAttributes.find(
        (attr: any) => attr.key === "printTimes"
      );

      if (printTimesAttr) {
        // Nếu đã có `printTimes`, tăng giá trị lên 1
        printTimesAttr.value = String(Number(printTimesAttr.value) + 1);
      } else {
        // Nếu chưa có, thêm mới
        customAttributes.push({ key: "printTimes", value: "1" });
      }
    }
  };
  return {
    handleGetDiary,
    addDiaries,
    diaries,
    dataEmployee,
    handleGetDataEmployee,
    upDateCancelOrderStatus,
    updateQuantityPrintOrder,
  };
});
