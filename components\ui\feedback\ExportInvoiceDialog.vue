<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-center gap-2 font-bold text-xl">
        <span class="text-red-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"
            />
          </svg>
        </span>
        <span><PERSON><PERSON><PERSON> nhận xuất hóa đơn</span>
      </div>
      <div>
        <div>
          Vui lòng
          <span class="font-semibold text-red-500"
            >kiểm tra kỹ thông tin hóa đơn trước khi xác nhận</span
          >. Hóa đơn chính thức sau khi xuất
          <span class="font-semibold">không thể chỉnh sửa</span>. Mọi sai sót có
          thể dẫn đến những bất tiện trong quá trình sử lý.
        </div>
      </div>
      <div class="flex space-x-4 mt-4">
        <button
          v-if="!isHideButton"
          @click="cancel"
          class="px-2 py-1 w-1/2 rounded flex items-center justify-center gap-2 bg-red-500 text-white"
        >
          <span
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 18 18 6M6 6l12 12"
              />
            </svg>
          </span>
          <span>Hủy</span>
        </button>
        <button
          @click="confirm"
          class="px-2 py-1 w-1/2 text-white rounded bg-green-500 flex items-center justify-center gap-2"
        >
          <span
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m4.5 12.75 6 6 9-13.5"
              />
            </svg>
          </span>
          <span>Đồng ý</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["title", "message", "isHideButton"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);

const confirm = () => {
  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
</script>

<style scoped></style>
