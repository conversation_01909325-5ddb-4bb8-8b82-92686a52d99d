<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold"><PERSON><PERSON><PERSON>u chỉnh sản phẩm</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <div class="max-h-[200px] overflow-y-auto">
        <div>
          <span class="font-bold">Tên: </span>
          <span>{{ product?.orderItemName }}</span>
        </div>
        <div class="space-x-1 flex items-center gap-1">
          <span>Giá bán sản phẩm</span>
          <!-- <input
            type="number"
            v-model="newPrice"
            class="max-w-[90px] outline-none border p-1 rounded"
            @change="handleChangePrice"
            :disabled="!targetItem?.isEditPrice"
          /> -->
          <CurrencyInput
            :disabled="!targetItem?.isEditPrice"
            class="max-w-[90px] outline-none border p-1 rounded"
            :key="flagReset"
            v-model="newPrice"
            :options="{
              currency: 'VND',
              hideCurrencySymbolOnFocus: false,
              hideGroupingSeparatorOnFocus: false,
              hideNegligibleDecimalDigitsOnFocus: false,
            }"
            @change="handleChangePrice"
          />
          <span
            v-if="
              product?.variant.price?.amount !==
              product?.variant.unitPrice?.amount
            "
            @click="handleResetPrice"
            class="cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
              />
            </svg>
          </span>
        </div>
        <div class="space-x-1">
          <span class="font-bold">Thành tiền: </span>
          <span
            :class="
              discountValue > 0
                ? 'line-through font-bold text-red-500'
                : 'font-bold text-primary'
            "
            >{{
              formatCurrency(
                product?.originalTotalPrice?.amount +
                  (product?.totalVAT?.amount || 0)
              )
            }}</span
          >
          <span v-if="discountValue > 0" class="text-primary font-bold">{{
            formatCurrency(
              product?.discountedTotalPrice?.amount +
                (product?.totalVAT?.amount || 0)
            )
          }}</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="font-bold">Giảm :</div>
          <div>
            <div
              class="flex items-center border border-gray-300 rounded-lg px-2 py-1 max-w-[130px] bg-secondary"
            >
              <select
                v-model="discountType"
                class="focus:outline-none bg-transparent"
                @change="handleDiscountTypeChange"
              >
                <option value="MONEY">₫</option>
                <option value="PERCENT">%</option>
              </select>
              <!-- <input
                type="text"
                class="w-full focus:outline-none text-right bg-secondary"
                v-model="discountValue"
                @input="formatCurrencyV2"
                @blur="handleBlur"
              /> -->
              <CurrencyInput
                class="w-full focus:outline-none text-right bg-secondary"
                :key="discountInputKey"
                v-model="discountValue"
                :options="{
                  currency: 'VND',
                  currencyDisplay: 'hidden',
                  hideCurrencySymbolOnFocus: false,
                  hideGroupingSeparatorOnFocus: false,
                  hideNegligibleDecimalDigitsOnFocus: false,
                }"
                @change="handleBlur"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-end">
        <button
          @click="handleCofirm"
          class="bg-primary text-white px-2 py-1 rounded"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
  <ConfirmDialog
    v-if="isEditOrderTypeDisCount"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu sản phẩm`"
    @confirm="confirmTypeDiscount"
    @cancel="cancelTypeDiscount"
  ></ConfirmDialog>
  <ConfirmDialog
    v-if="isEditOrderDisCount"
    :title="`Thông báo`"
    :message="`Đơn hàng đang ở trạng thái chính thức bạn có muốn điều chỉnh chiết khấu sản phẩm`"
    @confirm="confirmDiscount"
    @cancel="cancelDiscount"
  ></ConfirmDialog>
  <EditPricePopup
    v-if="isOpenReasonPopup"
    @confirm="handleConfirm"
    @cancel="toogleReasonPopup"
  ></EditPricePopup>
</template>

<script setup>
const { data } = await useFetch("/data/setting.json");
const route = useRoute();
const targetItem = computed(() =>
  data.value?.find((item) => item.storeId === route.query.orgId)
);
const emit = defineEmits(["confirm", "cancel"]);
const { updateDiscountPriceInOrder, updatePriceNewInOrder } = useOrder();
const orderStore = useOrderStore();
const props = defineProps(["product"]);
const newPrice = ref(props.product?.variant?.price?.amount);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const orderDetail = computed(() => orderStore.orderDetail);

const discountType = ref("MONEY");
const discountValue = ref(props.product?.discount?.value?.amount || 0);
const handleCofirm = () => {
  emit("confirm");
};
const isEditOrderDisCount = ref(false);
const handleBlur = async () => {
  if (discountType.value === "PERCENT" && discountValue.value > 100) {
    discountValue.value = 100; // Đặt giá trị tối đa là 100
    // app.$toast.warning("Chiết khấu phần trăm không thể lớn hơn 100%");
  }

  if (
    discountType.value === "MONEY" &&
    discountValue.value > props.product.originalTotalPrice.amount
  ) {
    discountValue.value = props.product.originalTotalPrice.amount; // Đặt giá trị tối đa là giá sản phẩm
    // app.$toast.warning("Chiết khấu tiền không thể lớn hơn giá sản phẩm");
  }

  if (discountValue.value > 0) {
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrderDisCount.value = true;
    } else {
      const data = {
        type_discount: discountType.value,
        discount_amount: discountValue.value,
        campaign_id: "",
        campaign_action_id: "",
        campaign_action_type: "",
      };
      await updateDiscountPriceInOrder(
        props.product?.order_id,
        props.product?.id,
        data
      );
      await orderStore.getOrderById(props.product?.order_id);
    }
  }
};
const isEditOrderTypeDisCount = ref(false);
const discountInputKey = ref(0);
const handleDiscountTypeChange = async () => {
  // Lưu giá trị hiện tại trước khi thay đổi

  if (orderDetail.value?.status === "APPROVED") {
    isEditOrderTypeDisCount.value = true;
  } else {
    discountValue.value = 0;
    discountInputKey.value++;
    const data = {
      type_discount: discountType.value,
      discount_amount: discountValue.value,
      campaign_id: "",
      campaign_action_id: "",
      campaign_action_type: "",
    };
    await updateDiscountPriceInOrder(
      props.product?.order_id,
      props.product?.id,
      data
    );
    await orderStore.getOrderById(props.product?.order_id);
  }
};
const confirmTypeDiscount = async () => {
  discountValue.value = 0;
  discountInputKey.value++;
  const data = {
    type_discount: discountType.value,
    discount_amount: discountValue.value,
    campaign_id: "",
    campaign_action_id: "",
    campaign_action_type: "",
  };
  await updateDiscountPriceInOrder(
    props.product?.order_id,
    props.product?.id,
    data
  );
  await orderStore.getOrderById(props.product?.order_id);
  isEditOrderTypeDisCount.value = false;
};
const cancelTypeDiscount = () => {
  // Chuyển đổi loại discount
  if (discountType.value === "MONEY") {
    discountType.value = "PERCENT";
  } else {
    discountType.value = "MONEY"; // Nếu không phải MONEY thì đặt lại là MONEY
  }

  isEditOrderTypeDisCount.value = false;
};
const confirmDiscount = async () => {
  const data = {
    type_discount: discountType.value,
    discount_amount: discountValue.value,
    campaign_id: "",
    campaign_action_id: "",
    campaign_action_type: "",
  };
  await updateDiscountPriceInOrder(
    props.product?.order_id,
    props.product?.id,
    data
  );
  await orderStore.getOrderById(props.product?.order_id);
  cancelDiscount();
};
const cancelDiscount = () => {
  isEditOrderDisCount.value = false;
};
const handleChangePrice = async () => {
  toogleReasonPopup();
  // await updatePriceNewInOrder(
  //   props.product?.order_id,
  //   props.product?.id,
  //   newPrice.value
  // );
  // await orderStore.getOrderById(props.product?.order_id);
};
const flagReset = ref(0);
const handleResetPrice = async () => {
  await updatePriceNewInOrder(
    props.product?.order_id,
    props.product?.id,
    props.product?.variant.unitPrice?.amount,
    "Cập nhật lại giá bán ban đầu"
  );
  await orderStore.getOrderById(props.product?.order_id);

  flagReset.value++;
};
const isOpenReasonPopup = ref(false);
const toogleReasonPopup = () => {
  isOpenReasonPopup.value = !isOpenReasonPopup.value;
};
const handleConfirm = async (reason) => {
  await updatePriceNewInOrder(
    props.product?.order_id,
    props.product?.id,
    newPrice.value,
    reason
  );
  await orderStore.getOrderById(props.product?.order_id);
};
</script>
