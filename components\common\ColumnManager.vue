<template>
  <div v-if="showColumnManager" class="fixed inset-0 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-6 relative w-4/12">
      <button
        @click="$emit('close-column-manager')"
        class="absolute top-2 right-2 text-gray-400 hover:text-gray-600 focus:outline-none transition duration-200"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>

      <h2 class="text-xl font-semibold mb-4 text-left text-transparent bg-clip-text bg-[#0D47A1]">
        Cài đặt hiện thị cột
      </h2>

      <div class="grid grid-cols-2 gap-2">
        <label
          v-for="(header, index) in headers"
          :key="header"
          class="group flex items-center p-2 rounded cursor-pointer bg-gray-50/10 hover:bg-gray-50/20 transition duration-200"
        >
          <input type="checkbox" v-model="visibleColumns[index]" class="hidden peer" />
          <div
            class="w-4 h-4 mr-2 rounded-md border border-gray-300 group-hover:border-[#0D47A1] peer-checked:bg-[#0D47A1] peer-checked:border-indigo-500 flex items-center justify-center transition duration-200"
          >
            <svg
              v-if="visibleColumns[index]"
              class="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2.5"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
          </div>
          <span class="text-sm text-gray-800 group-hover:text-indigo-500 transition duration-200">{{
            header
          }}</span>
        </label>
      </div>

      <button
        @click="saveSettings"
        class="mt-4 bg-gradient-to-r bg-[#0D47A1] text-white font-semibold py-2 px-4 rounded-full w-full focus:outline-none focus:ring focus:ring-indigo-200 transition duration-200"
      >
        Lưu
      </button>
    </div>
  </div>
</template>

<script setup>
import { watch } from "vue";

const props = defineProps(["headers", "visibleColumns", "showColumnManager"]);
const emits = defineEmits(["close-column-manager"]);

const saveSettings = () => {
  localStorage.setItem("visibleColumns", JSON.stringify(props.visibleColumns));
  emits("close-column-manager");
};

watch(
  () => props.visibleColumns,
  (newVal) => {
    localStorage.setItem("visibleColumns", JSON.stringify(newVal));
  }
);
</script>
