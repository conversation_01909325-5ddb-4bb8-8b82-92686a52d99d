<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="px-4 md:px-6 py-4">
        <div class="flex items-center gap-3 max-w-7xl mx-auto">
          <button
            @click="$router.back()"
            class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg
              class="w-5 h-5 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 class="text-xl font-semibold text-gray-900">
            Chi tiết chấm công
          </h1>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto">
      <!-- Mobile Layout -->
      <div class="lg:hidden h-[calc(100vh-80px)] overflow-y-auto">
        <div class="p-4 space-y-3 pb-6">
          <!-- Status Badge Mobile -->
          <div class="bg-white rounded-xl border border-gray-200 p-4">
            <div class="text-center">
              <div
                :class="
                  getTimekeepingStatusClass(dataTimeKeeping?.workEffortTypeId)
                "
                class="inline-flex"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    v-if="dataTimeKeeping?.workEffortTypeId === 'CHECK_IN'"
                    d="M10 12a2 2 0 100-4 2 2 0 000 4z"
                  />
                  <path
                    v-if="dataTimeKeeping?.workEffortTypeId === 'CHECK_IN'"
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  />
                  <path
                    v-else
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  />
                </svg>
                {{ getTimekeepingTypeText(dataTimeKeeping?.workEffortTypeId) }}
              </div>
            </div>
          </div>

          <!-- Employee Info Mobile -->
          <div class="bg-white rounded-xl border border-gray-200 p-4">
            <h2
              class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
            >
              <svg
                class="w-5 h-5 text-primary"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clip-rule="evenodd"
                />
              </svg>
              Thông tin nhân viên
            </h2>

            <div class="flex items-center gap-4">
              <div class="relative">
                <img
                  class="w-16 h-16 rounded-full border-2 border-gray-200 object-cover"
                  :src="
                    dataTimeKeeping?.owner?.avatar || 'https://placehold.co/80'
                  "
                  loading="lazy"
                  :alt="dataTimeKeeping?.owner?.name"
                />
                <div
                  class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"
                ></div>
              </div>

              <div class="flex-1">
                <h3 class="font-semibold text-gray-900 text-lg">
                  {{ dataTimeKeeping?.owner?.name || "Không có tên" }}
                </h3>
                <div class="flex items-center gap-2 text-gray-600 mt-1">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                    />
                  </svg>
                  <span>{{
                    dataTimeKeeping?.owner?.phone || "Chưa có số điện thoại"
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Time Info Mobile -->
          <div class="bg-white rounded-xl border border-gray-200 p-4">
            <h2
              class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
            >
              <svg
                class="w-5 h-5 text-primary"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                  clip-rule="evenodd"
                />
              </svg>
              Thông tin thời gian
            </h2>

            <div class="grid grid-cols-2 gap-3">
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm text-gray-600 mb-1">Ngày</div>
                <div class="font-semibold text-gray-900">
                  {{ formatDateOnly(dataTimeKeeping?.createdStamp) }}
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm text-gray-600 mb-1">Thời gian</div>
                <div class="font-semibold text-gray-900">
                  {{ formatTimeOnly(dataTimeKeeping?.createdStamp) }}
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm text-gray-600 mb-1">Loại</div>
                <div
                  class="font-semibold"
                  :class="getTypeColor(dataTimeKeeping?.workEffortTypeId)"
                >
                  {{
                    getTimekeepingTypeText(dataTimeKeeping?.workEffortTypeId)
                  }}
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-3">
                <div class="text-sm text-gray-600 mb-1">Trạng thái</div>
                <div class="flex items-center gap-2">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="font-semibold text-green-700 text-sm"
                    >Đã xác nhận</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Images Mobile -->
          <div class="bg-white rounded-xl border border-gray-200 p-4">
            <h2
              class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
            >
              <svg
                class="w-5 h-5 text-primary"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                  clip-rule="evenodd"
                />
              </svg>
              Hình ảnh chấm công
              <span v-if="hasImages" class="text-sm font-normal text-gray-500">
                ({{ imageUrls.length }} ảnh)
              </span>
            </h2>

            <div v-if="hasImages" class="space-y-3">
              <!-- Main Image Mobile -->
              <div class="relative bg-gray-100 rounded-lg overflow-hidden">
                <img
                  :src="imageUrls[currentImageIndex]"
                  :alt="`Ảnh chấm công ${currentImageIndex + 1}`"
                  class="w-full h-64 object-contain cursor-pointer"
                  loading="lazy"
                  @click="openImageModal(imageUrls[currentImageIndex])"
                />
                <div
                  class="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm"
                >
                  {{ currentImageIndex + 1 }}/{{ imageUrls.length }}
                </div>

                <!-- Navigation Arrows Mobile -->
                <button
                  v-if="imageUrls.length > 1"
                  @click="previousImage"
                  class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <button
                  v-if="imageUrls.length > 1"
                  @click="nextImage"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>

              <!-- Thumbnail Grid Mobile -->
              <div v-if="imageUrls.length > 1" class="grid grid-cols-4 gap-2">
                <div
                  v-for="(url, index) in imageUrls"
                  :key="index"
                  @click="currentImageIndex = index"
                  :class="[
                    'relative bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors',
                    currentImageIndex === index
                      ? 'border-primary'
                      : 'border-transparent hover:border-gray-300',
                  ]"
                >
                  <img
                    :src="url"
                    :alt="`Thumbnail ${index + 1}`"
                    class="w-full h-12 object-cover"
                    loading="lazy"
                  />
                </div>
              </div>
            </div>

            <!-- No Images State Mobile -->
            <div v-else class="text-center py-8">
              <svg
                class="w-12 h-12 text-gray-400 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Không có hình ảnh
              </h3>
              <p class="text-gray-500">
                Chấm công này không có hình ảnh đính kèm
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop Layout -->
      <div class="hidden lg:block h-[calc(100vh-80px)] overflow-y-auto">
        <div class="p-6 pb-6">
          <div class="grid grid-cols-3 gap-6">
            <!-- Left Column: Employee Info & Status -->
            <div class="lg:col-span-1 space-y-6">
              <!-- Status Badge -->
              <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="text-center">
                  <div
                    :class="
                      getTimekeepingStatusClass(
                        dataTimeKeeping?.workEffortTypeId
                      )
                    "
                    class="inline-flex"
                  >
                    <svg
                      class="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        v-if="dataTimeKeeping?.workEffortTypeId === 'CHECK_IN'"
                        d="M10 12a2 2 0 100-4 2 2 0 000 4z"
                      />
                      <path
                        v-if="dataTimeKeeping?.workEffortTypeId === 'CHECK_IN'"
                        fill-rule="evenodd"
                        d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                        clip-rule="evenodd"
                      />
                      <path
                        v-else
                        fill-rule="evenodd"
                        d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    {{
                      getTimekeepingTypeText(dataTimeKeeping?.workEffortTypeId)
                    }}
                  </div>
                </div>
              </div>

              <!-- Employee Info Card -->
              <div class="bg-white rounded-xl border border-gray-200 p-6">
                <h2
                  class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
                >
                  <svg
                    class="w-5 h-5 text-primary"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Thông tin nhân viên
                </h2>

                <div class="text-center space-y-4">
                  <div class="relative inline-block">
                    <img
                      class="w-20 h-20 rounded-full border-2 border-gray-200 object-cover mx-auto"
                      :src="
                        dataTimeKeeping?.owner?.avatar ||
                        'https://placehold.co/80'
                      "
                      loading="lazy"
                      :alt="dataTimeKeeping?.owner?.name"
                    />
                    <div
                      class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 border-2 border-white rounded-full"
                    ></div>
                  </div>

                  <div>
                    <h3 class="font-semibold text-gray-900 text-lg">
                      {{ dataTimeKeeping?.owner?.name || "Không có tên" }}
                    </h3>
                    <div
                      class="flex items-center justify-center gap-2 text-gray-600 mt-2"
                    >
                      <svg
                        class="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"
                        />
                      </svg>
                      <span>{{
                        dataTimeKeeping?.owner?.phone || "Chưa có số điện thoại"
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Middle Column: Time Info -->
            <div class="lg:col-span-1 space-y-6">
              <div class="bg-white rounded-xl border border-gray-200 p-6">
                <h2
                  class="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2"
                >
                  <svg
                    class="w-5 h-5 text-primary"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Thông tin thời gian
                </h2>

                <div class="space-y-4">
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">Ngày chấm công</div>
                    <div class="font-semibold text-gray-900 text-lg">
                      {{ formatDateOnly(dataTimeKeeping?.createdStamp) }}
                    </div>
                  </div>

                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">Thời gian</div>
                    <div class="font-semibold text-gray-900 text-lg">
                      {{ formatTimeOnly(dataTimeKeeping?.createdStamp) }}
                    </div>
                  </div>

                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">Loại chấm công</div>
                    <div
                      class="font-semibold text-lg"
                      :class="getTypeColor(dataTimeKeeping?.workEffortTypeId)"
                    >
                      {{
                        getTimekeepingTypeText(
                          dataTimeKeeping?.workEffortTypeId
                        )
                      }}
                    </div>
                  </div>

                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-600 mb-2">Trạng thái</div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span class="font-semibold text-green-700 text-lg"
                        >Đã xác nhận</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Images -->
            <div class="lg:col-span-1 space-y-6">
              <!-- Images Card -->
              <div class="bg-white rounded-xl border border-gray-200 p-6">
                <h2
                  class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"
                >
                  <svg
                    class="w-5 h-5 text-primary"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Hình ảnh chấm công
                  <span
                    v-if="hasImages"
                    class="text-sm font-normal text-gray-500"
                  >
                    ({{ imageUrls.length }} ảnh)
                  </span>
                </h2>

                <!-- Images Grid -->
                <div v-if="hasImages" class="space-y-4">
                  <!-- Main Image -->
                  <div class="relative bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      :src="imageUrls[currentImageIndex]"
                      :alt="`Ảnh chấm công ${currentImageIndex + 1}`"
                      class="w-full h-64 lg:h-80 object-contain cursor-pointer"
                      loading="lazy"
                      @click="openImageModal(imageUrls[currentImageIndex])"
                    />
                    <div
                      class="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm"
                    >
                      {{ currentImageIndex + 1 }}/{{ imageUrls.length }}
                    </div>

                    <!-- Navigation Arrows -->
                    <button
                      v-if="imageUrls.length > 1"
                      @click="previousImage"
                      class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                    >
                      <svg
                        class="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                    <button
                      v-if="imageUrls.length > 1"
                      @click="nextImage"
                      class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                    >
                      <svg
                        class="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>

                  <!-- Thumbnail Grid -->
                  <div
                    v-if="imageUrls.length > 1"
                    class="grid grid-cols-3 gap-2"
                  >
                    <div
                      v-for="(url, index) in imageUrls"
                      :key="index"
                      @click="currentImageIndex = index"
                      :class="[
                        'relative bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-colors',
                        currentImageIndex === index
                          ? 'border-primary'
                          : 'border-transparent hover:border-gray-300',
                      ]"
                    >
                      <img
                        :src="url"
                        :alt="`Thumbnail ${index + 1}`"
                        class="w-full h-16 object-cover"
                        loading="lazy"
                      />
                    </div>
                  </div>
                </div>

                <!-- No Images State -->
                <div v-else class="text-center py-12">
                  <svg
                    class="w-12 h-12 text-gray-400 mx-auto mb-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">
                    Không có hình ảnh
                  </h3>
                  <p class="text-gray-500">
                    Chấm công này không có hình ảnh đính kèm
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Modal -->
    <div
      v-if="selectedImage"
      class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
      @click="closeImageModal"
    >
      <div class="relative max-w-4xl max-h-full">
        <img
          :src="selectedImage"
          class="max-w-full max-h-full object-contain"
        />
        <button
          @click="closeImageModal"
          class="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2 hover:bg-black/70 transition-colors"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Chi tiết chấm công",
  meta: [
    {
      name: "description",
      content: "chi tiet",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chi tiết chấm công",
});
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";

const { getWorkEffortById, getUrlImageTimeKeeping } = useTimeKeeping();
const route = useRoute();
const id: string = route.params.id as string;

// Reactive data
const dataTimeKeeping = ref<any>();
const selectedImage = ref<string | null>(null);
const currentImageIndex = ref(0);

// Computed properties
const imageUrls = computed(() => {
  if (
    !dataTimeKeeping.value?.attachments ||
    !Array.isArray(dataTimeKeeping.value.attachments) ||
    dataTimeKeeping.value.attachments.length === 0
  ) {
    return [];
  }

  return dataTimeKeeping.value.attachments.map((item: any) => {
    const endpoint = getUrlImageTimeKeeping();
    return `${endpoint}/${item.srcPath}`;
  });
});

const hasImages = computed(() => imageUrls.value.length > 0);

// Format functions
const formatDateOnly = (timestamp: number) => {
  if (!timestamp) return "Không có dữ liệu";
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "eeee, dd/MM/yyyy", { locale: vi });
};

const formatTimeOnly = (timestamp: number) => {
  if (!timestamp) return "Không có dữ liệu";
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm:ss", { locale: vi });
};

// Import utilities
import {
  getTimekeepingTypeText,
  getTimekeepingStatusClass,
} from "~/utils/statusHelpers";

const getTypeColor = (type: string) => {
  if (type === "CHECK_IN") return "text-green-700";
  if (type === "CHECK_OUT") return "text-red-700";
  return "text-gray-700";
};

// Image navigation functions
const nextImage = () => {
  if (currentImageIndex.value < imageUrls.value.length - 1) {
    currentImageIndex.value++;
  } else {
    currentImageIndex.value = 0; // Loop back to first image
  }
};

const previousImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  } else {
    currentImageIndex.value = imageUrls.value.length - 1; // Loop to last image
  }
};

// Modal functions
const openImageModal = (url: string) => {
  selectedImage.value = url;
};

const closeImageModal = () => {
  selectedImage.value = null;
};

// API functions
const handleGetWorkEffortById = async (id: string) => {
  try {
    const response = await getWorkEffortById(id);
    dataTimeKeeping.value = response;
  } catch (error) {
    console.error("Error fetching work effort:", error);
    throw error;
  }
};

onMounted(() => {
  handleGetWorkEffortById(id);
});
</script>
