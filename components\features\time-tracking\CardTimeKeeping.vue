<template>
  <div class="space-y-3">
    <div v-if="!dataCheckin?.length" class="text-center py-12">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
        />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Chưa có dữ liệu chấm công
      </h3>
      <p class="text-gray-500">
        Bắt đầu chấm công để theo dõi thời gian làm việc
      </p>
    </div>

    <div v-for="(item, index) in dataCheckin" :key="index">
      <NuxtLink
        :to="`/timekeeping/${item.id}?storeId=${storeId}`"
        @click="handleChooseTimeKeeping(item)"
        class="block"
      >
        <div
          class="bg-white rounded-lg p-4 border border-gray-200 hover:border-primary hover:shadow-md transition-all duration-200"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-gray-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path
                    fill-rule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <div class="font-semibold text-gray-900">
                  {{ formatDate(item.createdStamp) }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ formatTime(item.createdStamp) }}
                </div>
              </div>
            </div>

            <div :class="getActionClass(item.workEffortTypeId)">
              {{ formatAction(item.workEffortTypeId) }}
            </div>
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
  <ModalEditCheckin v-if="isEditCheckin" @isClose="isClose" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";
const { storeId,orgId } = useTabContext();
const { dataCheckin } = defineProps(["dataCheckin"]);
const isEditCheckin = ref(false);
const timeKeepingStore = useTimeKeepingStore();
const isClose = (value: boolean) => {
  isEditCheckin.value = value;
};

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "eeee, dd/MM/yyyy", { locale: vi });
};

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm:ss", { locale: vi });
};

const formatAction = (value: string) => {
  if (value === "CHECK_IN") {
    return "Vào ca";
  }
  if (value === "CHECK_OUT") {
    return "Hết ca";
  }
  return value;
};

const getActionClass = (value: string) => {
  const baseClass =
    "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
  if (value === "CHECK_IN") {
    return `${baseClass} bg-green-100 text-green-800`;
  }
  if (value === "CHECK_OUT") {
    return `${baseClass} bg-red-100 text-red-800`;
  }
  return `${baseClass} bg-gray-100 text-gray-800`;
};

const handleChooseTimeKeeping = (item: any) => {
  timeKeepingStore.setTimeKeeping(item);
};
</script>
