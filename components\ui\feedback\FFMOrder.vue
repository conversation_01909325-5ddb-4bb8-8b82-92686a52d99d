<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div
      class="bg-secondary rounded-lg shadow-lg p-2 max-w-6xl w-full animate-popup"
    >
      <div class="flex justify-between">
        <div></div>
        <div class="text-lg font-bold mb-2 text-center">Xử lí FFM đơn hàng</div>
        <div class="text-red-500 cursor-pointer" @click="cancel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <div class="block md:hidden max-h-[70vh] overflow-y-auto w-full">
        <InfoOrderFFM :order="order" :dataFFM="dataFFM"></InfoOrderFFM>
        <ExportWarehouse
          v-if="dataFFM"
          :order="order"
          :dataFFM="dataFFM"
          @fetchFFMStatus="handleGetFFMStage"
        >
        </ExportWarehouse>

        <WrapProduct
          v-if="
            dataPackageBox.length > 0 &&
            order?.order?.shippingAddress &&
            dataFFM
          "
          :dataPackageBox="dataPackageBox"
          :order="order"
          :dataFFM="dataFFM"
          @fetchFFMStatus="handleGetFFMStage"
        >
        </WrapProduct>

        <LinkShipping
          v-if="order?.order?.shippingAddress"
          :order="order"
          :dataFFM="dataFFM"
          :shippingAddress="orderStore.dataDefaultAddress"
          :customer="orderStore.customerInOrder"
          @fetchFFMStatus="handleGetFFMStage"
        >
        </LinkShipping>
      </div>
      <div class="md:block hidden">
        <div class="grid grid-cols-12 gap-2">
          <div
            class="col-span-8 space-y-2 max-h-[75svh] overflow-y-auto relative"
          >
            <!-- Thanh tiến trình -->
            <div
              v-if="order.order.fulfillmentStatus !== 'UNFULFILLED'"
              class="sticky top-0 z-10 bg-white rounded py-4"
            >
              <div class="flex items-center justify-around relative">
                <template v-for="(step, index) in steps" :key="index">
                  <!-- Cột chứa step -->
                  <div class="flex flex-col items-center flex-1 relative">
                    <!-- Chấm tròn -->
                    <div
                      class="w-5 h-5 rounded-full z-10"
                      :class="{
                        'bg-primary': index < indexCSS,
                        'border-2 border-primary-500 bg-white':
                          index === indexCSS,
                        'bg-gray-300': index > indexCSS,
                      }"
                    ></div>

                    <!-- Label -->
                    <div
                      class="mt-2 text-sm text-center whitespace-nowrap"
                      :class="{
                        'text-primary font-medium': index < indexCSS,
                        'text-gray-400': index > indexCSS,
                      }"
                    >
                      {{ step }}
                    </div>

                    <!-- Đường kẻ -->
                    <div
                      v-if="index < steps.length - 1"
                      class="absolute top-2 left-1/2 w-full h-1"
                    >
                      <div
                        class="h-1"
                        :class="{
                          'bg-primary': index < indexCSS,
                          'bg-gray-300': index >= indexCSS,
                        }"
                      ></div>
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!--  -->
            <ExportWarehouse
              v-if="dataFFM"
              :order="order"
              :dataFFM="dataFFM"
              @fetchFFMStatus="handleGetFFMStage"
            >
            </ExportWarehouse>

            <WrapProduct
              v-if="
                dataPackageBox.length > 0 &&
                order?.order?.shippingAddress &&
                dataFFM
              "
              :dataPackageBox="dataPackageBox"
              :order="order"
              :dataFFM="dataFFM"
              @fetchFFMStatus="handleGetFFMStage"
            >
            </WrapProduct>

            <LinkShipping
              class="w-auto"
              v-if="order?.order?.shippingAddress"
              :order="order"
              :dataFFM="dataFFM"
              :shippingAddress="orderStore.dataDefaultAddress"
              :customer="orderStore.customerInOrder"
              @fetchFFMStatus="handleGetFFMStage"
            >
            </LinkShipping>
          </div>
          <!-- Thông tin sản phẩm -->
          <div class="col-span-4">
            <InfoOrderFFM :order="order" :dataFFM="dataFFM"></InfoOrderFFM>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["order"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);

const confirm = () => {
  emit("confirm");
  isVisible.value = false;
};
const steps = ref(["Xuất kho", "Đóng gói", "Vận chuyển", "Hoàn thành"]);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
//
const { getPackageBox, ffmStage, shipmentParameter } = usePortal();
const dataPackageBox = ref([]);
const loading = ref(false);
const dataFFM = ref();
const handleGetPackageBox = async () => {
  try {
    const response = await getPackageBox();
    const data = [
      {
        id: "1",
        name: "Chọn kích thước đóng gói",
        dimension: {
          length: "0",
          width: "0",
          height: "0",
          dimensionUnit: "CM",
        },
        weight: "0",
        weightUnit: "KILOGRAMS",
      },
    ];
    if (response?.data) {
      dataPackageBox.value = [...data, ...response?.data];
    }
  } catch (error) {
    throw error;
  }
};
// Import utilities
import { calculateFFMStage } from "~/utils/ffmHelpers";

const styleCSS = ref("");
const indexCSS = ref(0);
const handleGetFFMStage = async () => {
  try {
    const response = await ffmStage(props.order?.id);
    dataFFM.value = response?.data;

    // Use utility function to calculate stage
    const stageResult = calculateFFMStage(dataFFM.value);
    styleCSS.value = stageResult.styleCSS;
    indexCSS.value = stageResult.indexCSS;
  } catch (error) {
    throw error;
  }
};
const handleShipmentParameter = async () => {
  try {
    const response = await shipmentParameter(props.order?.id);
  } catch (error) {
    throw error;
  }
};
const orderStore = useOrderStore();
onMounted(async () => {
  if (!props.order.order.shippingAddress) {
    steps.value = ["Xuất kho", "Hoàn thành"];
  }
  loading.value = true;
  Promise.allSettled([
    handleGetFFMStage(),
    handleGetPackageBox(),
    handleShipmentParameter(),
  ]);
  loading.value = false;
});
//
</script>
