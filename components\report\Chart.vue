<template>
  <div :style="myStyles">
    <Bar :data="chartData" :options="chartOptions" />
  </div>
</template>

<script setup>
import { Bar } from "vue-chartjs";
import {
  Chart as ChartJS,
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  Tooltip,
  LineController,
  BarController,
} from "chart.js";

ChartJS.register(
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  Tooltip,
  LineController,
  BarController
);

const myStyles = computed(() => ({
  height: "400px",
  width: "100%",
  position: "relative",
}));

const chartData = ref({
  labels: [],
  datasets: [
    {
      type: "line",
      borderColor: "#ff6384",
      data: [],
      fill: false,
      tension: 0.1,
      yAxisID: "y1",
    },
    {
      type: "bar",
      backgroundColor: "#3f51b5",
      data: [],
    },
  ],
});

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: "index",
    intersect: false,
  },
  stacked: false,
  plugins: {
    datalabels: false,
    title: {
      display: true,
    },
  },
  scales: {
    x: {
      stacked: false,
    },
    y: {
      type: "linear",
      display: true,
      position: "left",
      stacked: true,
    },
    y1: {
      type: "linear",
      display: true,
      position: "right",
      grid: {
        drawOnChartArea: false,
      },
    },
  },
});
const props = defineProps(["dataChart"]);
const updateChartData = () => {
  if (props.dataChart && props.dataChart.reportDTOS) {
    if (route.params.reportItem === "payment-method") {
      const labels = props.dataChart.reportDTOS.map((item) => item.name);
      const dataPaid = props.dataChart.reportDTOS.map(
        (item) => item.totalAmountRevenue
      );
      const dataNotPaid = props.dataChart.reportDTOS.map(
        (item) => item.totalAmountRefund
      );
      chartData.value = {
        labels: labels,
        datasets: [
          {
            type: "line",
            label: "Chưa thanh toán",
            backgroundColor: "#616161",
            borderColor: "#BEBEBE",
            data: dataNotPaid,
            fill: false,
            tension: 0.1,
            yAxisID: "y1",
          },
          {
            label: "Đã thanh toán",
            backgroundColor: "#3f51b5",
            data: dataPaid,
          },
        ],
      };
    } else {
      const labels = props.dataChart.reportDTOS.map((item) => item.name);
      const dataRevenue = props.dataChart.reportDTOS.map(
        (item) => item.totalPrice
      );
      const dataOrder = props.dataChart.reportDTOS.map(
        (item) => item.totalOrders
      );
      chartData.value = {
        labels: labels,
        datasets: [
          {
            type: "line",
            label: "Số đơn",
            backgroundColor: "#616161",
            borderColor: "#BEBEBE",
            data: dataOrder,
            fill: false,
            tension: 0.1,
            yAxisID: "y1",
          },
          {
            label: "Doanh thu",
            backgroundColor: "#3f51b5",
            data: dataRevenue,
          },
        ],
      };
    }
    //
  }
};
onMounted(() => {
  updateChartData();
});
watch(
  () => props.dataChart,
  () => {
    updateChartData();
  }
);
//
const route = useRoute();
</script>

<style scoped></style>
