<template>
  <div
    class="bg-card relative text-card-foreground p-2 rounded-lg border flex items-center overflow-hidden"
    :class="{ 'opacity-50': !isChecked }"
  >
    <input
      type="checkbox"
      id="choose-me"
      v-model="isChecked"
      class="w-4 h-4 accent-primary rounded-full"
    />
    <label for="product1" class="cursor-pointer flex items-center w-full ml-2">
      <img
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="flex-shrink-0 w-16 h-16 rounded-lg object-contain"
        loading="lazy"
      />
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-sm line-clamp-2 font-semibold">
            {{ product.orderLineItem?.orderItemName }}
          </h3>
        </div>
        <div class="flex items-center justify-between mt-2">
          <p class="text-primary font-semibold">
            {{
              formatCurrency(product.orderLineItem?.originalTotalPrice?.amount)
            }}
          </p>
          <div class="flex items-center">
            <button
              @click="handleDecreaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
              :disabled="!isChecked"
            >
              -
            </button>
            <input
              type="number"
              min="1"
              :disabled="!isChecked"
              class="w-10 h-6 text-center border border-input rounded-md mx-2"
              v-model="product.orderLineItem.currentQuantity"
            />
            <button
              @click="handleIncreaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary-foreground rounded-md"
              :disabled="!isChecked"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </label>

    <div
      v-if="isUpdating"
      class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
    >
      <svg
        class="animate-spin h-5 w-5 text-primary"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
        ></path>
      </svg>
    </div>
  </div>
  <!-- <div @click="handleCancel()"> test</div> -->
</template>

<script setup>
const orderStore = useOrderStore();
const props = defineProps(["product"]);
const isChecked = ref(
  props.product.orderItemStatus === "CANCELLED" ? false : true
);
const isUpdating = ref(false);
const handleDecreaseQuantity = async () => {
  isUpdating.value = true;
  if (props.product.orderLineItem.currentQuantity > 1) {
    const newQuantity = props.product.orderLineItem.currentQuantity - 1;
    await orderStore.updateQuantity(
      props.product.orderLineItem.id,
      newQuantity
    );
  } else {
    useNuxtApp().$toast.warning(
      "Sản phẩm có số lượng nhỏ hơn hoặc bằng 1 nên không thể giảm"
    );
  }
  isUpdating.value = false;
};
const handleIncreaseQuantity = async () => {
  isUpdating.value = true;
  const newQuantity = props.product.orderLineItem.currentQuantity + 1;
  await orderStore.updateQuantity(props.product.id, newQuantity);
  isUpdating.value = false;
};
const { disableOrderItem, enableProductDiary } = useOrder();
const orderDetail = computed(() => orderStore.orderDetail);
watch(
  () => isChecked.value,
  async (newVal, oldVal) => {
    if (newVal) {
      await enableProductDiary(
        orderDetail.value.id,
        props.product?.orderLineItem?.id
      );
    } else {
      await disableOrderItem(
        orderDetail.value.id,
        props.product?.orderLineItem?.id
      );
    }
  }
);
</script>
