<template>
  <div>
    <LoadingSpinner v-if="loadingNavigate" />

    <div class="mt-2 mx-2">
      <SearchOrder
        :dataEmployee="diaryStore.dataEmployee"
        :dataPaymentMethod="dataPaymentMethod"
        :dataOrderStatus="dataOrderStatus"
        :isFFM="true"
        @handleSearch="handleSearch"
      ></SearchOrder>
      <!--  -->
      <div class="mt-2 md:mt-0 flex justify-end">
        <!-- <TabChangeOrder @toogleTab="handleSetTab"></TabChangeOrder> -->
        <button
          @click="handleNavigate"
          class="text-white bg-primary px-2 py-1 rounded-md transition duration-200 items-center gap-2 flex"
        >
          <svg
            class="w-5 h-5 inline-block"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Tạo đơn
        </button>
      </div>

      <!--  -->
      <div v-if="ordersStore.isAlert" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </div>
    </div>
    <div ref="scrollContainer" class="md:mx-2 h-[100vh] overflow-y-auto">
      <div>
        <div class="flex flex-col gap-2 mb-2 mt-2">
          <div class="flex flex-col h-full">
            <LoadingDiary v-if="loading" :isPageOrder="true"></LoadingDiary>

            <div class="bg-white md:block hidden">
              <table class="table-auto w-full text-sm">
                <thead class="sticky top-0 z-2">
                  <tr class="text-left font-semibold bg-blue-100">
                    <th class="p-2 w-1/12 text-center">Mã đơn</th>
                    <th class="p-2 w-1/12 text-center">Fulfillment</th>

                    <th class="p-2 w-2/12">Khách hàng</th>

                    <th class="p-2 w-2/12">Nhân viên</th>
                    <th class="p-2 w-3/12">Sản phẩm</th>
                    <th class="p-2 w-1/12">Tổng giảm</th>

                    <th class="p-2 w-2/12">Thanh toán</th>
                    <th class="p-2 w-full">
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          class="size-5"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                          />
                        </svg>
                      </span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="diary in ordersStore.dataListOrder"
                    :key="diary.id"
                    class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
                  >
                    <TableDiary
                      :diary="diary"
                      :isNotDraft="true"
                      :isFFM="true"
                      @handleLoading="toggleLoading"
                    ></TableDiary>
                  </tr>
                </tbody>
                <div class="mb-[60px]"></div>
              </table>
            </div>
            <div class="block md:hidden">
              <QuickOrder
                v-for="item in ordersStore.dataListOrder"
                :key="item.id"
                :dataOrder="item"
                @handleLoading="toggleLoading"
              ></QuickOrder>
            </div>
          </div>
        </div>

        <LoadingDiary v-if="isLoading"></LoadingDiary>
      </div>
      <div class="mb-[140px]"></div>
    </div>
  </div>
</template>

<script setup>
//lazy loading component
const TableDiary = defineAsyncComponent(() =>
  import("~/components/Diary/TableDiary.vue")
);
const QuickOrder = defineAsyncComponent(() =>
  import("~/components/Order/QuickOrder.vue")
);
//
useHead({
  title: "Đóng goi vận chuyển",
  meta: [
    {
      name: "description",
      content: "Đóng gói vận chuyển",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Đóng gói vận chuyển",
});
const isReturnOrder = ref(false);
const scrollContainer = ref(null);
const ordersStore = useOrdersStore();
const { fetchListSellOrder, fetchDataEmployees, fetchListSellOrderReturn } =
  useOrder();
const loading = ref(false);
const options = reactive({
  currentPage: 1,
  maxResult: 10,
  //   status_ignore: [1],
});
const isLoading = ref(false);
const hasMoreData = ref(true);
// Debounce function to prevent multiple rapid API calls
function debounce(fn, delay) {
  let timeoutID;
  return function (...args) {
    if (timeoutID) {
      clearTimeout(timeoutID);
    }
    timeoutID = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}
const diaryStore = useDiariesStore();

onMounted(async () => {
  ordersStore.tooltip = null;
  localStorage.setItem("paymentAmount", 0);

  loading.value = true;

  // Chạy tất cả promise đồng thời
  await Promise.allSettled([
    await handleSetTab("SELL"),
    diaryStore.handleGetDataEmployee(),
    handleGetDataSearch(),
  ]);

  loading.value = false;
});
const { storeId,orgId } = useTabContext();

const isLoadingNavigate = ref(false);
const handleNavigate = async () => {
  let isTimeout = false;

  // Khởi động một timer 150ms
  const timer = setTimeout(() => {
    isTimeout = true;
    isLoadingNavigate.value = true; // Chỉ bật trạng thái loading nếu vượt quá 150ms
  }, 150);

  // Thực hiện điều hướng
  await navigateTo(`/sale?orgId=${orgId.value}&storeId=${storeId.value}`);

  // Dọn dẹp timer
  clearTimeout(timer);

  // Nếu điều hướng hoàn thành trước 150ms, không bật loading
  if (isTimeout) {
    isLoadingNavigate.value = false;
  }
};
const loadData = async () => {
  if (options.currentPage === 1) return;
  if (isLoading.value || !hasMoreData.value) return;
  isLoading.value = true;
  try {
    if (isReturnOrder.value) {
      const response = await fetchListSellOrderReturn(options);
      const data = response.data?.data || [];
      if (data.length) {
        data.forEach((item) => {
          ordersStore.addOrder(item);
        });
        if (options.currentPage === 1) {
          options.currentPage = 2;
        } else {
          options.currentPage++;
        }
      } else {
        hasMoreData.value = false; // No more data to load
      }
    } else {
      const response = await fetchListSellOrder(options);
      const data = response.data?.data || [];

      if (data.length) {
        data.forEach((item) => {
          ordersStore.addOrder(item);
        });
        if (options.currentPage === 1) {
          options.currentPage = 2;
        } else {
          options.currentPage++;
        }
      } else {
        hasMoreData.value = false; // No more data to load
      }
    }
  } catch (error) {
    console.error("Error loading data", error);
  } finally {
    isLoading.value = false;
  }
};

useInfiniteScroll(scrollContainer, debounce(loadData, 100), {
  distance: 100,
});
const router = useRouter();
const { paymentMethods, getPaymentMethodTypes } = usePayment();
const { fetchListSaleOrderStatus } = useOrder();
const dataPaymentMethod = ref();
const dataOrderStatus = ref();
const handleGetDataSearch = async () => {
  try {
    dataPaymentMethod.value = await getPaymentMethodTypes();
    const response = await fetchListSaleOrderStatus();
    dataOrderStatus.value = response?.data;
    const data = [
      {
        description: "Đã FFM",
        name: "true",
        value: "true",
      },
      {
        description: "Chưa FFM",
        name: "false",
        value: "false",
      },
    ];
    dataOrderStatus.value = [...data, ...dataOrderStatus.value];
  } catch (error) {
    throw error;
  }
};

const handleSearch = async (data) => {
  if (loading.value) return; // Tránh gọi API khi đang loading
  loading.value = true;

  // Kiểm tra nếu currentPage đã là 1 thì không gán lại để tránh reset không cần thiết
  if (options.currentPage !== 1) {
    options.currentPage = 1;
  }

  options.date_create_to = data?.date_create_to;
  options.date_create_from = data?.date_create_from;
  options.employee_assign = data?.employee_assign;
  options.keyword = data?.keyword;
  options.payment_method = data?.payment_method;
  options.customer_multi_value = data?.customer_multi_value;
  options.product_multi_value = data?.product_multi_value;
  options.ffm_status = data?.ffm_status;
  options.exist_ffm_status = data?.exist_ffm_status;
  if (isReturnOrder.value) {
    await ordersStore.getListDataReturnOrder(options);
  } else {
    await ordersStore.getListDataOrder(options);
  }
  options.currentPage = 2;

  hasMoreData.value = true;
  loading.value = false;
};

const loadingNavigate = ref(false);
const toggleLoading = (state) => {
  loadingNavigate.value = state;
};
const handleSetTab = async (tab) => {
  options.currentPage = 1;
  if (tab === "RETURN") {
    isReturnOrder.value = true;
  } else {
    isReturnOrder.value = false;
  }
  isLoading.value = false;
  hasMoreData.value = true;
  loading.value = true;
  if (isReturnOrder.value) {
    await ordersStore.getListDataReturnOrder(options);
  } else {
    await ordersStore.getListDataOrder(options);
  }
  options.currentPage = 2;
  loading.value = false;
};
</script>
