export type Roles = Array<string>;

// Export transaction types
export * from "./Transaction";
export interface ProductFilterOptions {
  category?: string;
  product?: string;
  sku?: string;
  tag?: string;
  priceFrom?: number;
  priceTo?: number;
  status?: string;
  productType?: string;
  subType?: string;
  brandId?: string;
  keyword?: string;
  display?: boolean;
  onlyPromotion?: boolean;
  currentPage?: number;
  maxResult?: number;
}

export type DiaryTab = {
  id: number;
  time: number;
  products: any[];
  customer: {
    id: string;
    email: string;
    name: string;
    phone: string;
    gender: string;
    avatar: string;
    birthday_time_long: null | number;
  };
  note: string;
  order_id: null | number;
  nameTab: string;
  status: string;
  staffId: string;
  typeOrder: string;
};
