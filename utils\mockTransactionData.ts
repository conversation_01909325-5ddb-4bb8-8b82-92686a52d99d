/**
 * Mock Transaction Data Generator
 * Generates realistic transaction data for testing and development
 */

import { 
  Transaction, 
  TransactionStatus, 
  PaymentGatewayType, 
  PaymentConfirmStatus,
  TransactionPurpose 
} from '~/types/Transaction';

// Sample data arrays
const BANK_CODES = ['VCB', 'TCB', 'VTB', 'BIDV', 'ACB', 'MB', 'VPB', 'TPB', 'STB', 'EIB'];
const GATEWAYS = ['VietQR', 'ZaloPay', 'MoMo', 'VNPay', 'PayPal', 'Stripe', 'Banking'];
const DESCRIPTIONS = [
  'Thanh toán đơn hàng #DH001',
  '<PERSON>y<PERSON>n khoản từ khách hàng',
  '<PERSON><PERSON><PERSON> tiền từ ví điện tử',
  '<PERSON>h toán hóa đơn dịch vụ',
  'Hoàn tiền đơn hàng',
  'Nạp tiền vào tài kho<PERSON>n',
  '<PERSON><PERSON> toán phí dịch vụ',
  '<PERSON><PERSON><PERSON><PERSON> khoản nội bộ',
  '<PERSON><PERSON> to<PERSON> qua QR Code',
  '<PERSON><PERSON><PERSON> dịch từ POS'
];

const PARTNER_IDS = ['PARTNER_001', 'PARTNER_002', 'PARTNER_003', 'PARTNER_004', 'PARTNER_005'];

/**
 * Generate a random number within a range
 */
const randomBetween = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Generate a random element from an array
 */
const randomElement = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

/**
 * Generate a random date within the last 30 days
 */
const randomRecentDate = (): Date => {
  const now = new Date();
  const daysAgo = randomBetween(0, 30);
  const date = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000));
  
  // Add random hours and minutes
  date.setHours(randomBetween(0, 23));
  date.setMinutes(randomBetween(0, 59));
  date.setSeconds(randomBetween(0, 59));
  
  return date;
};

/**
 * Generate a random account number
 */
const generateAccountNumber = (): string => {
  return Array.from({ length: 12 }, () => randomBetween(0, 9)).join('');
};

/**
 * Generate a random transaction ID
 */
const generateTransactionId = (bankCode: string, extId: string): string => {
  return `${bankCode}_${extId}`;
};

/**
 * Generate custom attributes
 */
const generateCustomAttributes = (): Record<string, any> => {
  const attributes: Record<string, any> = {};
  
  // Randomly add some custom attributes
  if (Math.random() > 0.5) {
    attributes.customerName = `Khách hàng ${randomBetween(1, 100)}`;
  }
  
  if (Math.random() > 0.7) {
    attributes.promotionCode = `PROMO${randomBetween(100, 999)}`;
  }
  
  if (Math.random() > 0.8) {
    attributes.deviceInfo = randomElement(['Mobile App', 'Web Browser', 'POS Terminal']);
  }
  
  return attributes;
};

/**
 * Generate a single mock transaction
 */
export const generateMockTransaction = (id?: number): Transaction => {
  const bankCode = randomElement(BANK_CODES);
  const extId = `EXT${Date.now()}${randomBetween(100, 999)}`;
  const paymentGatewayType = randomElement(Object.values(PaymentGatewayType));
  const status = randomElement(Object.values(TransactionStatus));
  const paymentConfirmStatus = randomElement(Object.values(PaymentConfirmStatus));
  const purpose = randomElement(Object.values(TransactionPurpose));
  const timeTransaction = randomRecentDate();
  
  // Generate amount based on purpose
  let amount: number;
  if (purpose === TransactionPurpose.SALE_ORDER) {
    amount = randomBetween(50000, 5000000); // 50k to 5M VND
  } else if (purpose === TransactionPurpose.COLLECT_FROM_GATEWAY) {
    amount = randomBetween(100000, ********); // 100k to 10M VND
  } else {
    amount = randomBetween(10000, 2000000); // 10k to 2M VND
  }
  
  const transaction: Transaction = {
    // Core identifiers
    partnerId: randomElement(PARTNER_IDS),
    extId,
    transactionId: generateTransactionId(bankCode, extId),
    
    // Payment gateway information
    paymentGatewayType,
    bankCode,
    bankAccountNumber: generateAccountNumber(),
    extBankAccountNumber: generateAccountNumber(),
    extBank: randomElement(BANK_CODES),
    
    // Transaction details
    amount,
    currencyCode: 'VND',
    description: randomElement(DESCRIPTIONS),
    timeTransaction,
    status,
    gateway: randomElement(GATEWAYS),
    
    // Payment information
    paymentId: `PAY_${Date.now()}_${randomBetween(1000, 9999)}`,
    paymentAmount: amount + randomBetween(-10000, 10000), // Slight variation
    paymentConfirmStatus,
    paymentConfirmNote: paymentConfirmStatus === PaymentConfirmStatus.REJECTED 
      ? 'Thông tin không khớp' 
      : paymentConfirmStatus === PaymentConfirmStatus.CONFIRMED 
        ? 'Xác nhận thành công' 
        : '',
    
    // Related entities
    accountTransactionId: `ACC_${Date.now()}_${randomBetween(100, 999)}`,
    orderId: purpose === TransactionPurpose.SALE_ORDER ? `ORD_${randomBetween(1000, 9999)}` : '',
    invoiceId: purpose === TransactionPurpose.SALE_ORDER ? `INV_${randomBetween(1000, 9999)}` : '',
    cashAccountId: `CASH_${randomBetween(100, 999)}`,
    
    // Purpose and metadata
    purpose,
    customAttributes: generateCustomAttributes(),
    
    // UI/Display fields (for compatibility)
    id: id || randomBetween(1, 10000),
    type: purpose === TransactionPurpose.SALE_ORDER ? "income" : "expense",
    category: {
      id: randomBetween(1, 10),
      name: purpose === TransactionPurpose.SALE_ORDER 
        ? 'Bán hàng' 
        : purpose === TransactionPurpose.COLLECT_FROM_GATEWAY 
          ? 'Rút tiền' 
          : 'Khác'
    },
    paymentMethod: paymentGatewayType === PaymentGatewayType.BANK_TRANSACTION ? 'bank' : 'gateway',
    date: timeTransaction.toISOString()
  };
  
  return transaction;
};

/**
 * Generate multiple mock transactions
 */
export const generateMockTransactions = (count: number = 15): Transaction[] => {
  const transactions: Transaction[] = [];
  
  for (let i = 0; i < count; i++) {
    transactions.push(generateMockTransaction(i + 1));
  }
  
  // Sort by date (newest first)
  transactions.sort((a, b) => 
    new Date(b.timeTransaction).getTime() - new Date(a.timeTransaction).getTime()
  );
  
  return transactions;
};

/**
 * Generate mock transaction statistics
 */
export const generateMockTransactionStats = (transactions: Transaction[]) => {
  const income = transactions
    .filter(t => t.type === "income")
    .reduce((sum, t) => sum + t.amount, 0);
    
  const expense = transactions
    .filter(t => t.type === "expense")
    .reduce((sum, t) => sum + t.amount, 0);
    
  const pendingCount = transactions.filter(t => t.status === TransactionStatus.PENDING).length;
  const completedCount = transactions.filter(t => t.status === TransactionStatus.COMPLETED).length;
  const failedCount = transactions.filter(t => t.status === TransactionStatus.FAILED).length;
  
  return {
    totalIncome: income,
    totalExpense: expense,
    netProfit: income - expense,
    transactionCount: transactions.length,
    pendingCount,
    completedCount,
    failedCount,
    incomeChange: randomBetween(-20, 30), // Mock percentage change
    expenseChange: randomBetween(-15, 25),
    profitChange: randomBetween(-30, 40),
    countChange: randomBetween(-10, 20)
  };
};
