<template>
  <div>
    <div v-if="isModalEditCustomer" class="fixed z-10 inset-0">
      <div
        class="flex items-center justify-center min-h-screen bg-gray-500 bg-opacity-75"
      >
        <ModalEditCustomer
          v-if="selectedCustomer"
          :customer="selectedCustomer"
          @closeModalEditCustomer="toggleModalEditCustomer"
        />
      </div>
    </div>
    <div>
      <div class="flex items-center justify-center">
        <div class="rounded max-w-full w-full h-screen overflow-y-auto">
          <div class="grid grid-cols-2 md:grid-cols-6 p-2 gap-2">
            <InfoCustomerDetail
              :customer="customerStore.customer"
              :wallet="wallet"
            ></InfoCustomerDetail>
            <AddressShipping></AddressShipping>
            <!-- <WalletCustomer :wallet="wallet"></WalletCustomer> -->
            <!-- <ListTopic :listTopic="listTopic"></ListTopic> -->
          </div>
          <div class="grid grid-cols-1 md:grid-cols-6 px-2">
            <div class="md:block hidden md:col-span-6">
              <HistoryBuyOrder
                :listOrder="customerStore.listOrder"
              ></HistoryBuyOrder>
            </div>
          </div>

          <div class="block md:hidden">
            <HistoryBuyOrderMobile
              :listOrder="customerStore.listOrder"
            ></HistoryBuyOrderMobile>
          </div>
          <div class="h-[80px]"></div>
        </div>
      </div>
    </div>
    <div v-if="loading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const isModalEditCustomer = ref(false);
const selectedCustomer = ref(null);
const toggleModalEditCustomer = (customer = null) => {
  isModalEditCustomer.value = !isModalEditCustomer.value;
  selectedCustomer.value = customer;
};
useHead({
  title: "Chi tiết khách hàng",
  meta: [
    {
      name: "description",
      content: "Chi tiết khách hàng",
    },
  ],
});
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: `Khách hàng`,
});
const customerStore = useCustomerStore();
const { getTopicByCustomerId } = useCustomer();
const listTopic = ref();
const handleGetTopic = async (customerId: string) => {
  const response = await getTopicByCustomerId(
    { customerId: customerId },
    10,
    1
  );
  listTopic.value = response?.content;
};
const loading = ref(false);
onMounted(async () => {
  loading.value = true;
  await Promise.all([
    customerStore.handleCusTomer(route.query.customerId as string),
  ]);
  await getWalletCustomer(route.query.customerId as string);
  loading.value = false;
});
const { getCustomerWallet } = useCustomer();
const wallet = ref();
const getWalletCustomer = async (customerId: string) => {
  try {
    const response = await getCustomerWallet(customerId, "wallet");
    if (response?.errorCode) {
      wallet.value = null;
    } else {
      wallet.value = response;
    }
  } catch (error) {
    throw error;
  }
};
watch(
  () => route.query.customerId,
  async (newVal, oldVal) => {
    if (newVal) {
      loading.value = true;
      await Promise.all([customerStore.handleCusTomer(newVal as string)]);
      await getWalletCustomer(newVal as string);
      loading.value = false;
    }
  }
);
</script>
