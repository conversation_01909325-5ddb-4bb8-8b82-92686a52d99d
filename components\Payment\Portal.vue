<template>
  <div class="p-4 rounded-lg max-h-svh">
    <div v-if="!isHide" class="flex flex-col gap-2">
      <div
        v-for="item in dataPaymentMethod"
        :key="item.code"
        class="border rounded-md p-4 flex gap-2 items-center cursor-pointer"
        :class="{ 'border-blue-600': selectedPaymentMethod === item.code }"
        :v-model="selectedPaymentMethod"
        @click="selectPaymentMethod(item)"
      >
        <img
          v-if="item.image"
          class="w-6 h-6 object-contain mr-2 md:w-8 md:h-8"
          :src="item.image"
          alt=""
          loading="lazy"
        />
        <span class="font-semibold text-base">{{ item.name }}</span>
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-between gap-2">
        <div class="flex items-center gap-2">
          <div @click="handleClickBack" class="cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
              />
            </svg>
          </div>
          <div class="font-bold">
            <span class="text-primary">{{ itemName }}</span>
          </div>
        </div>
      </div>
      <!-- Tiền mặt -->
      <div v-if="isManual" class="mt-4 flex flex-col gap-2">
        <div class="">
          <div class="text-gray-500">Tổng thanh toán</div>
          <div class="text-red-500 text-4xl font-bold">
            {{
              paymentAmount > 0
                ? formatCurrency(paymentAmount)
                : formatCurrency(orderDetails.data.remainTotal)
            }}
          </div>
        </div>
        <div class="mt-2">
          <div class="text-gray-500">Đã nhận</div>
          <input
            type="text"
            inputmode="numeric"
            class="w-full p-2 border border-blue-300 bg-blue-100 text-4xl font-bold text-black outline-none rounded"
            v-model="formattedReceived"
            @input="updateReceived(formattedReceived)"
          />
        </div>
        <!-- phần gợi ý giá tiền -->
        <div class="flex gap-3">
          <div
            v-for="suggestedAmount in suggestAmounts(
              orderDetails.data.remainTotal
            )"
            :key="suggestedAmount"
            class="p-1 rounded bg-gray-200 cursor-pointer"
            @click="handleRecommend(suggestedAmount)"
          >
            {{ formatCurrency(suggestedAmount) }}
          </div>
        </div>

        <div class="mt-2">
          <div class="text-gray-500">Tiền thừa</div>
          <div class="text-green-500 text-4xl font-bold">
            {{ formatCurrency(changeAmount) }}
          </div>
        </div>
        <div class="flex items-center justify-end gap-4">
          <button
            @click="handleClickPayment"
            class="flex items-center justify-center bg-primary px-2 py-1 font-bold text-white rounded cursor-pointer"
          >
            Thanh toán
          </button>
          <!-- <button
            @click="handlePrintMethodManual"
            class="flex items-center justify-center p-2 font-bold text-primary border border-primary rounded-lg cursor-pointer"
          >
            In hóa đơn
          </button> -->
        </div>
      </div>

      <div
        v-if="dataPayment && dataPayment.qrCodeUrl && !isManual"
        class="font-bold mt-4 text-nowrap"
      >
        Vui lòng quét mã để thanh toán
      </div>

      <div v-if="loading" class="mt-4">
        <LoadingSpinner />
      </div>
      <div v-else>
        <div
          v-if="dataPayment && dataPayment.qrCodeUrl && !isManual"
          class="mx-2 mb-5 flex items-center justify-center"
        >
          <div class="">
            <img
              :src="dataPayment.qrCodeUrl"
              alt="QR Code"
              class="md:w-[250px] md:h-[250px]"
              loading="lazy"
            />
            <div v-if="isExpired" class="flex flex-col gap-2">
              <div class="flex items-center justify-center text-red-500">
                Giao dịch đã bị hủy vui lòng tạo mã mới!
              </div>
              <div
                @click="createPaymentAgain"
                class="flex items-center justify-center bg-primary text-nowrap text-white py-1 w-[200px] mx-auto rounded"
              >
                Tạo mã mới
              </div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex items-center justify-center mx-2 w-full h-[300px]"
        >
          <div
            v-if="selectedPaymentMethod === 'payon'"
            class="flex items-center justify-center text-primary font-semibold px-2 py-1"
          >
            Đã tạo thanh toán mPos thành công
          </div>
        </div>
        <div
          v-if="isCasso"
          @click="handleConfirmPayment"
          class="bg-primary flex items-center justify-center text-white p-2 rounded-lg font-bold mx-3"
        >
          Xác nhận đã thanh toán
        </div>
        <!-- <div class="mb-3-1 flex items-center justify-center">
          <button
            v-if="dataPayment && dataPayment.qrCodeUrl"
            @click="handlePrint"
            class="bg-primary px-2 py-1 font-bold text-white rounded cursor-pointer"
          >
            In hóa đơn
          </button>
        </div> -->
      </div>
      <button
        @click="createPaymentAgain"
        v-if="selectedPaymentMethod === 'payon'"
        class="flex items-center justify-center text-primary font-semibold border px-2 py-1 border-primary rounded mx-auto"
      >
        Tạo mã thanh toán mới
      </button>
    </div>
    <Dialog v-if="isAlert" @confirm="confirm" @cancel="cancel"></Dialog>
    <!-- <ExpiredMessage v-if="isExpired" @confirm="handleConfirm"></ExpiredMessage> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import type { Auth } from "~/types/Auth";
const props = defineProps([
  "orderDetails",
  "orderId",
  "dataPaymentMethod",
  "paymentAmount",
]);
const { createPaymentOrder, cancelPayment, paymentsByOrders } = usePayment();
const selectedPaymentMethod = ref("");
const route = useRoute();
const auth = useCookie("auth").value as unknown as Auth;
const dataPayment = ref();
const loading = ref<Boolean>(false);
const isManual = ref<Boolean>(false);
const received = ref<number>(0);
const changeAmount = ref<number>(0);
const app = useNuxtApp();
const isHide = ref<boolean>(false);
const itemName = ref<string>();
const itemCode = ref<string>();
const isCasso = ref<Boolean>(false);
let orderId: any = route.query.orderId as any;
const paymentData = ref<any>();
const { storeId, orgId } = useTabContext();

const isAlert = ref<Boolean>(false);
const isExpired = ref<Boolean>(false);
const orderStore = useOrderStore();
const { fetchOrderDetails, updateStatusApproved } = useOrder();
// const paymentAmount = JSON.parse(localStorage.getItem("paymentAmount") || "0");
const formattedReceived = computed({
  get() {
    return formatCurrency(received.value);
  },
  set(value: string) {
    received.value = +value.replace(/[^\d]/g, "");
  },
});
const selectPaymentMethod = async (item: any) => {
  // hủy thằng cuối cùng
  const cancelpayment = await paymentsByOrders([orderId]);
  if (cancelpayment.length > 0) {
    const firstId = cancelpayment[cancelpayment.length - 1];
    await cancelPayment(firstId?.paymentId, "");
    // lọc qua thằng nào chưa hủy thì hủy hết r mới tạo đơn
    cancelpayment.map(async (item: any) => {
      if (item.statusCode === "1") {
        await cancelPayment(item.paymentId, "");
      }
    });
  }
  intervalId = setInterval(handleCheckPaymentStatus, 5000);

  isExpired.value = false;
  isHide.value = true;
  itemName.value = item.name;
  itemCode.value = item.code;
  if (item.code === "casso") {
    isCasso.value = true;
  }
  if (item.code === "manual") {
    selectedPaymentMethod.value = item.code;
    isManual.value = true;
  } else {
    selectedPaymentMethod.value = item.code;
    // console.log("selectedPaymentMethod", selectedPaymentMethod.value);
    // check đồng bộ tạo đơn phải hủy thanh toán trước

    //aáng
    isManual.value = false;
    const host = window.location.origin;
    let data;
    const paymentAmount = JSON.parse(
      localStorage.getItem("paymentAmount") || "0"
    );
    if (paymentAmount > 0) {
      data = {
        orderId: orderId,
        paymentMethod: itemCode.value,
        appliedAmount: paymentAmount,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: auth?.user?.id,
      };
    } else {
      data = {
        orderId: orderId,
        paymentMethod: itemCode.value,
        appliedAmount: props.orderDetails.data.remainTotal,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: auth?.user?.id,
      };
    }
    loading.value = true;
    const payment = await createPaymentOrder(data);
    loading.value = false;
    dataPayment.value = payment;
    const paymentId = await paymentsByOrders([orderId]);
    //
    const lastPaymentId = paymentId[paymentId.length - 1];

    paymentData.value = lastPaymentId?.paymentId;
  }
};

const updateReceived = (value: string) => {
  formattedReceived.value = value;
};

const calculateChange = () => {
  changeAmount.value = received.value - props.orderDetails.data.remainTotal;
  if (changeAmount.value < 0) {
    changeAmount.value = 0;
  }
};
const handleClickPayment = async () => {
  if (
    received.value <
    (props.paymentAmount > 0
      ? props.paymentAmount
      : props.orderDetails.data.remainTotal)
  ) {
    app.$toast.warning("Vui lòng nhập số tiền lớn hơn số tiền cần thanh toán");
  } else {
    const host = window.location.origin;
    const cancelpayment = await paymentsByOrders([orderId]);
    if (cancelpayment.length > 0) {
      const firstId = cancelpayment[cancelpayment.length - 1];
      await cancelPayment(firstId?.paymentId, "");
    }
    const paymentAmount = JSON.parse(
      localStorage.getItem("paymentAmount") || "0"
    );

    let data;
    if (paymentAmount > 0) {
      data = {
        orderId: orderId,
        paymentMethod: itemCode.value,
        appliedAmount: paymentAmount,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: auth?.user?.id,
      };
    } else {
      data = {
        orderId: orderId,
        paymentMethod: itemCode.value,
        appliedAmount: props.orderDetails.data.remainTotal,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        returnUrl: `${host}/thanh-toan`,
        paymentType: "ONLINE",
        createBy: auth?.user?.id,
      };
    }
    const payment = await createPaymentOrder(data);
    if (payment) {
      // app.$toast.success("Thanh toán thành công");
      orderStore.paymentAmount = 0;
      localStorage.removeItem("paymentAmount");
      await updateStatusApproved(orderId);
      isAlert.value = true;
    } else {
      app.$toast.error("Thanh toán thất bại");
    }
  }
};

const handleClickBack = async () => {
  clearInterval(intervalId);

  isHide.value = false;
  selectedPaymentMethod.value = "";
  dataPayment.value = {};
  isCasso.value = false;
  const data = await cancelPayment(paymentData.value, "");
};
const handleConfirmPayment = () => {
  orderStore.paymentAmount = 0;
  localStorage.removeItem("paymentAmount");
  navigateTo(`/diary?orgId=${orgId.value}&storeId=${storeId.value}`);
};
const confirm = (value: boolean) => {
  isAlert.value = value;
  navigateTo(`/diaries?orgId=${orgId.value}&storeId=${storeId.value}`);
};
const cancel = () => {
  isAlert.value = false;
};
// hàm tạo lại qr nếu thanh toán bị hủy hoặc hết hạn
const createPaymentAgain = async () => {
  isExpired.value = false;
  const cancelpayment = await paymentsByOrders([orderId]);
  // hủy thằng cuối cùng cho an toàn
  if (cancelpayment.length > 0) {
    const firstId = cancelpayment[cancelpayment.length - 1];
    await cancelPayment(firstId?.paymentId, "");
  }
  // tọa lại thanh toán mới
  isManual.value = false;
  const host = window.location.origin;
  let data;
  const paymentAmount = JSON.parse(
    localStorage.getItem("paymentAmount") || "0"
  );
  if (paymentAmount > 0) {
    data = {
      orderId: orderId,
      paymentMethod: itemCode.value,
      appliedAmount: paymentAmount,
      payDate: Date.now(),
      source: "ORDER_SOURCE",
      returnUrl: `${host}/thanh-toan`,
      paymentType: "ONLINE",
      createBy: auth?.user?.id,
    };
  } else {
    data = {
      orderId: orderId,
      paymentMethod: itemCode.value,
      appliedAmount: props.orderDetails.data.remainTotal,
      payDate: Date.now(),
      source: "ORDER_SOURCE",
      returnUrl: `${host}/thanh-toan`,
      paymentType: "ONLINE",
      createBy: auth?.user?.id,
    };
  }

  loading.value = true;
  const payment = await createPaymentOrder(data);
  loading.value = false;
  dataPayment.value = payment;
  const paymentId = await paymentsByOrders([orderId]);
  const lastPaymentId = paymentId[paymentId.length - 1];
  intervalId = setInterval(handleCheckPaymentStatus, 5000);
  paymentData.value = lastPaymentId?.paymentId;
};
const handleCheckPaymentStatus = async () => {
  if (dataPayment.value?.data && itemCode.value !== "casso") {
    const response = await paymentsByOrders([orderId]);
    const lastPaymentId = response[response.length - 1];
    if (lastPaymentId.statusCode === "0") {
      await updateStatusApproved(orderId);
      orderStore.paymentAmount = 0;
      localStorage.removeItem("paymentAmount");
      clearInterval(intervalId);
      isAlert.value = true;
    }
    if (lastPaymentId.statusCode === "-1") {
      clearInterval(intervalId);
      isExpired.value = true;
    }
  }
};

function suggestAmounts(amount: number) {
  const suggestionThresholds = [50000, 100000, 200000, 500000];

  let suggestions = suggestionThresholds.filter(
    (threshold) => threshold >= amount
  );

  if (!suggestions.includes(amount)) {
    suggestions.unshift(amount);
  }

  return suggestions.slice(0, 3);
}

let intervalId: any;

onUnmounted(() => {
  clearInterval(intervalId);
});
watch(
  () => route.query.orderId,
  (newId, oldId) => {
    if (newId !== oldId) {
      formattedReceived.value = "";
      orderId = newId;
      isHide.value = false;
      selectedPaymentMethod.value = "";
      dataPayment.value = {};
      isCasso.value = false;
    }
  }
);

watch(received, calculateChange);
const handleRecommend = (value: any) => {
  received.value = value;
  formattedReceived.value = formatCurrency(received.value);
  updateReceived(formattedReceived.value);
};
// in hóa đơn
const { printOrderHTML } = useOrder();
import printJS from "print-js";

const handlePrint = async () => {
  const route = useRoute();
  try {
    const response = await printOrderHTML(
      route.query.orderId as string,
      selectedPaymentMethod.value
    );

    const data = response.data;

    const printContainer = document.createElement("div");
    printContainer.id = "print-test1";
    printContainer.innerHTML = `${data}`;
    document.body.appendChild(printContainer);

    printJS({
      printable: "print-test1",
      type: "html",
      scanStyles: true,
      maxWidth: 1200,
      targetStyles: ["*"],
    });

    document.body.removeChild(printContainer);
  } catch (error) {
    console.error("Error printing the order:", error);
    throw error;
  }
};

const handlePrintMethodManual = async () => {
  try {
    const response = await printOrderHTML(
      route.query.orderId as string,
      "manual"
    );
    const data = response.data;

    const printContainer = document.createElement("div");
    printContainer.id = "print-test2";
    printContainer.innerHTML = `${data}`;
    document.body.appendChild(printContainer);

    printJS({
      printable: "print-test2",
      type: "html",
      scanStyles: true,
      maxWidth: 1200,
      targetStyles: ["*"],
    });

    document.body.removeChild(printContainer);
  } catch (error) {
    console.error("Error printing the order:", error);
    throw error;
  }
};
</script>

<style scoped>
.border-blue-600 {
  border-color: #1d4ed8 !important;
}
</style>
