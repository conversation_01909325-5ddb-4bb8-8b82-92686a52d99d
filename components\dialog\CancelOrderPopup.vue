<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-lg w-full animate-popup"
    >
      <h2 class="text-lg font-bold text-center">{{ title }}</h2>
      <div class="mb-2">
        {{ text }}
      </div>
      <div class="flex items-center gap-2 mb-2">
        <label for="warehouse-select" class="block w-40">{{ reasonText }}</label>
        <select
          id="warehouse-select"
          class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
          v-model="reason"
          @change="handleChangeReason"
        >
          <option
            v-for="reason in dataReason"
            :key="reason.name"
            :value="reason.name"
          >
            {{ reason?.name }}
          </option>
        </select>
      </div>
      <!--  -->
      <textarea
        v-if="reason === 'Khác'"
        rows="2"
        id="note"
        class="py-1 px-2 w-full text-base rounded outline-none border bg-secondary mb-2"
        placeholder="Lý do hủy "
        v-model="reasonWrite"
      ></textarea>
      <div class="flex justify-end space-x-4">
        <button @click="cancel" class="px-2 py-1 bg-gray-300 rounded">
          Đóng
        </button>
        <button
          @click="confirm"
          class="px-2 py-1 bg-primary text-white rounded"
        >
          Đồng ý
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["order", "title", "text", "reasonText", "dataReason"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const reason = ref("Chọn lý do hủy");
const confirm = () => {
  if (reasonWrite.value === "Chọn lý do hủy" || !reasonWrite.value) {
    useNuxtApp().$toast.warning("Vui lòng chọn lý do hủy đơn");
    return;
  }
  emit("confirm", reasonWrite.value);
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
//

const reasonWrite = ref("Chọn lý do hủy");
const handleChangeReason = () => {
  if (reason.value !== "Khác") {
    reasonWrite.value = reason.value;
  } else {
    reasonWrite.value = "";
  }
};
</script>