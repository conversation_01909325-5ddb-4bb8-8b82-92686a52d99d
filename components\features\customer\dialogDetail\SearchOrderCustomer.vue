<template>
  <div class="px-2 py-2">
    <div class="flex justify-between">
      <span class="text-md font-bold text-primary">Sản phẩm</span>
      <div
        class="text-md font-bold text-primary cursor-pointer underline decoration-primary decoration-[1px]"
      >
        <PERSON><PERSON> mục sản phẩm
      </div>
    </div>
    <div class="relative w-full mt-2 mb-1 bg-bg<PERSON><PERSON> rounded-[16px] flex gap-2">
      <div class="w-full relative">
        <input
          v-model="keyword"
          class="w-full py-2 px-2 text-base outline-none bg-secondary rounded-md pr-[120px]"
          type="text"
          placeholder="T<PERSON><PERSON> kiếm theo tên sản phẩm, id, sku, ...."
        />

        <svg
          v-if="searchLoading === false && keyword"
          class="absolute top-3 right-[55px] cursor-pointer size-4 text-red-500"
          @click="clearInputSearch"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
          />
        </svg>
        <svg
          v-if="searchLoading"
          class="animate-spin h-3 w-3 text-primary absolute top-3 right-[55px]"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
          ></path>
        </svg>
        <span
          id="searchProductBtn"
          @click="handleGetProduct"
          class="absolute top-[50%] -translate-y-[50%] cursor-pointer right-0 h-full py-[11px] px-[4px] flex justify-center items-center bg-primary w-[50px] rounded-r-md"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="#fff"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            ></path>
          </svg>
        </span>
        <ProductList
          v-if="products.length"
          :products="products"
          :isOpen="isModalOpen"
          @close="closeModal"
        ></ProductList>
      </div>
    </div>
    <div v-if="isProduct" class="text-sm text-red-400">
      Không tìm thấy sản phẩm mà bạn đang tìm kiếm
    </div>
  </div>
  <CategoryProductPopup
    v-if="isOpenCategory"
    @cancel="handleOpenCategory"
    @confirm="handleOpenCategory"
  ></CategoryProductPopup>
</template>

<script setup>
let timeoutId = ref(null);
const { options, productImages, searchLoading, getProducts } = useProduct();
const keyword = ref("");
const products = ref([]);
const isModalOpen = ref(false);
const closeModal = () => {
  isModalOpen.value = false;
};
const isProduct = ref(false);
// const debounceSearch = () => {
//   clearTimeout(timeoutId);
//   timeoutId = setTimeout(() => {
//     getProducts();
//   }, 1000);
// };
const clearInputSearch = () => {
  keyword.value = "";
  isModalOpen.value = false;
  isProduct.value = false;
};
const handleGetProduct = async () => {
  if (keyword.value) {
    searchLoading.value = true;
    products.value = await getProducts({ keyword: keyword.value });
    searchLoading.value = false;
    isModalOpen.value = true;
  }
};
const debounceSearch = () => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }

  timeoutId.value = setTimeout(async () => {
    if (keyword.value) {
      searchLoading.value = true;
      products.value = await getProducts({ keyword: keyword.value });
      searchLoading.value = false;
      isModalOpen.value = true;
      if (products.value.length) {
        isProduct.value = false;
      } else {
        isProduct.value = true;
      }
    }
  }, 500);
};
//
const isOpenCategory = ref(false);

const handleOpenCategory = () => {
  isOpenCategory.value = !isOpenCategory.value;
};
</script>

<style scoped></style>
