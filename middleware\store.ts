export default defineNuxtRouteMiddleware((to) => {
  // Use tab-isolated context instead of cookies
  const { orgId, storeId, isOrgValid, isStoreValid } = useTabContext();

  // <PERSON><PERSON><PERSON> tra orgId cho tất cả các trang
  if (!isOrgValid.value) {
    return navigateTo("/org");
  }

  // Kiểm tra storeId cho tất cả các trang ngoại trừ /org và /dashboard
  if (!isStoreValid.value) {
    if (to.path !== "/dashboard" && to.path !== "/org") {
      return navigateTo("/dashboard");
    }
  }

  // Điều kiện riêng cho trang gốc "/"
  if (to.path === "/") {
    return navigateTo(`/feature?orgId=${orgId.value}&storeId=${storeId.value}`);
  }
});
