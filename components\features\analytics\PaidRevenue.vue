<template>
  <div class="mt-5">
    <h2 class="text-2xl text-primary-light font-bold mt-5">
      <PERSON><PERSON><PERSON> thu đã thanh toán
    </h2>
    <div class="flex flex-row">
      <div class="w-1/2 p-4">
        <table class="min-w-full divide-y divide-gray-200 border">
          <thead class="bg-gray-50">
            <tr class="border">
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-4 border"
              >
                STT
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider max-w-[600px] border"
              >
                PTTT
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border"
              >
                Doanh thu
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in data.reportDTOS" class="border">
              <td class="px-6 py-4 whitespace-nowrap text-sm border">
                <div class="w-full">{{ index + 1 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div class="">{{ item.name }}</div>
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 border"
              >
                {{ formatCurrency(item.totalAmountRevenue) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="w-1/2 p-4">
        <ChartRevenue :dataPayment="data"></ChartRevenue>
      </div>
    </div>
  </div>
</template>
<script setup>
const { data } = defineProps(["data"]);
</script>
