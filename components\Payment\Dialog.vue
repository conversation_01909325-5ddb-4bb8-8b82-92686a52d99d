<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-2 max-w-6xl w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold"><PERSON>h toán thành công</div>
        <div>
          <!-- <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg> -->
        </div>
      </div>
      <!-- <PERSON><PERSON>n nội dung -->
      <div class="max-h-[350px] overflow-y-auto">
        <InfoOrder :dataOrder="orderDetails?.data"></InfoOrder>
      </div>
      <div class="flex items-center border-t gap-1 my-1 py-2">
        <span class="font-semibold text-primary">Chia sẻ đơn: </span>
        <SendZalo :orderDetail="orderDetails?.data"></SendZalo>
      </div>
      <div class="flex items-center justify-between">
        <button
          @click="reviewOrder"
          class="text-primary border border-primary px-2 py-1 rounded"
        >
          Xem chi tiết đơn hàng
        </button>
        <button
          @click="confirm"
          class="bg-primary text-white px-2 py-1 rounded"
        >
          Tiếp tục tạo đơn
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
// const props = defineProps(["orderDetails"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const confirm = () => {
  emit("confirm", false);
};
const { getOrderById } = useOrderStore();
const route = useRoute();
const orderDetails = ref();
const { fetchOrderDetails, updateStatusApproved } = useOrder();
const reviewOrder = async () => {
  cancel();
  navigateTo(
    `/sale?orderId=${route.query.orderId}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
  );
};
const fetchOrderData = async (orderId: string) => {
  try {
    orderDetails.value = await fetchOrderDetails(orderId);
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  fetchOrderData(route.query.orderId as string);
});
</script>

<style scoped></style>
