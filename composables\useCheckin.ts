import { defineNuxtPlugin } from "#app";
import { ref } from "vue";
import type { Auth } from "~/types/Auth";
// import type {
//   AddAttachmentRequest,
//   BaseSort,
// } from "@longvansoftware/storefront-js-client/dist/src/types/crm";
export interface createCompanyRequest {
  name: string;
  phone: string;
  address: string;
  email: string;
}
export interface AddAttachmentRequest {
  srcId: string;
  srcName: string;
  srcPath: string;
  srcConfigPathId?: string;
  name?: string;
  type?: string;
  fileType?: string;
}
export interface AddAttachmentRequestFull {
  name: string;
  description: string;
  parentId?: string | null;
  priorityName: string;
  referName: string;
  referPhone: string;
  referEmail: string;
  targetId: string;
  extSource: string;
  ownerId?: string;
}
export interface GetOpportunityRequest {
  id?: string;
  pageSize: number;
  pageNumber: number;
  isPagination: boolean;
  workEffortTypeId: string;
  sort: {
    key: string;
    asc: boolean;
  };
}
export default function useCheckin() {
  const $sdk = useNuxtApp().$sdk;
  const nuxtApp = useNuxtApp();
  const token = useCookie("token").value;
  const auth = useCookie("auth").value as unknown as Auth;
  if (token) {
    $sdk.setToken(token);
  } else {
    throw new Error("Token is not defined");
  }

  const checkinStore = useCheckinStore();
  const dataPerson = ref([]);
  const dataWorkEffort = ref([]);
  const actionLinkLocation = ref();
  const actionLinkImage = ref();

  const getPersonByPartyIds = async (): Promise<any> => {
    const dataQuery: string[] = [];
    checkinStore.dataCheckin?.map((item: { ownerId: string }) => {
      const ownerId = item.ownerId;
      dataQuery.push(ownerId);
    });
    try {
      const response = await $sdk.user.getPersonByPartyIds(dataQuery);
      dataPerson.value = response;
      return response;
    } catch (error) {
      console.log("error");
      throw error;
    }
  };
  const getListWorkEfforts = async (
    performerId: string,
    workEffortTypeId: string,
    source: string,
    pageNumber: number,
    pageSize: number
  ) => {
    const sorts = {
      key: "createdStamp",
      asc: false,
    };
    try {
      const response = await $sdk.crm.getWorkEfforts(
        performerId,
        workEffortTypeId,
        source,
        pageNumber,
        pageSize,
        sorts
      );
      checkinStore.setDataCheckin(response.data);
    } catch (error) {
      throw error;
    }
  };

  const getPersonByPartyId = async (id: string) => {
    try {
      const response = await $sdk.user.getPersonByPartyId(id);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const getListWorkEffortTypes = async (id: any) => {
    try {
      const response = await $sdk.crm.getListWorkEffortType(id);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const searchCompany = async (keyword: string, limit: number) => {
    try {
      const res = await $sdk.user.searchCompany(keyword, limit);
      return res;
    } catch (error) {
      console.log("loi khi search company");
      throw error;
    }
  };

  const addOpportunity = async (performerId: string, datarequest?: any) => {
    const data = {
      // id: auth?.user.id,
      pageSize: 4,
      pageNumber: 0,
      isPagination: true,
      sort: {
        key: "createdStamp",
        asc: false,
      },
      workEffortTypeId: "SALE_POINT_MARKETING",
    };
    try {
      const res = await $sdk.crm.addOpportunity(performerId, datarequest);
      // await getListOpportunitys(data);
      return res;
    } catch (error) {
      throw error;
    }
  };

  const uploadImage = async (imageCode: string) => {
    try {
      const response = await $sdk.upload.uploadImage(imageCode);
      return response;
    } catch (error) {
      throw error;
    }
  };

  const AddAttachmentForWorkEffort = async (
    userId: string,
    id: string,
    arr: AddAttachmentRequest
  ) => {
    try {
      const res = await $sdk.crm.addAttachmentForWorkEffort(userId, id, arr);
      return res;
    } catch (error) {
      throw error;
    }
  };
  const UpdateWorkEffortDescriptions = async (
    userId: string,
    workEffortId: string,
    description: string
  ) => {
    try {
      const response = await $sdk.crm.updateWorkEffortDescription(
        userId,
        workEffortId,
        description
      );
      // await getListWorkEfforts(userId, "SALE_POINT_MARKETING", "", 0, 10);

      return response;
    } catch (error) {
      throw error;
    }
  };
  const UpdateWorkEffortName = async (
    performerId: string,
    workEffortId: string,
    name: string
  ) => {
    try {
      const response = await $sdk.crm.updateWorkEffortName(
        performerId,
        workEffortId,
        name
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateStatusAttachmentByIds = async (
    performerId: string,
    attachmentId: string,
    status: string
  ) => {
    try {
      const response = await $sdk.crm.updateStatusAttachmentById(
        performerId,
        attachmentId,
        status
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createCompany = async (data: createCompanyRequest, id: string) => {
    try {
      const response = await $sdk.user.createCompany(data, id);
      console.log("response", response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getOpportunityById = async (id: string) => {
    try {
      const response = await $sdk.crm.getWorkEffortById(id);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateWorkEffortProcessStatus = async (
    workEffortId: string,
    processStatus: string,
    performerId: string
  ) => {
    try {
      const response = await $sdk.crm.updateWorkEffortProcessStatus(
        workEffortId,
        processStatus,
        performerId
      );
      // await getOpportunityById(workEffortId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createWorkEfforts = async (
    createdBy: string,
    name: string,
    decription: string,
    workEffortTypeId: string,
    source: string,
    attributes: object,
    addAttachmentRequest: AddAttachmentRequest
  ) => {
    // try {
    //   const response = await $sdk.crm.createWorkEffort(
    //     createdBy,
    //     name,
    //     decription,
    //     workEffortTypeId,
    //     source,
    //     attributes,
    //     addAttachmentRequest
    //   );
    //   await getListWorkEfforts(createdBy, "", source, 0, 10);
    //   return response;
    // } catch (error) {
    //   throw error;
    // }
  };
  const getListTodoByid = async (id: string) => {
    try {
      const response = await $sdk.crm.getListTodo([id]);
      return response[0].toDoList;
    } catch (error) {
      throw error;
    }
  };
  return {
    getPersonByPartyIds,
    dataPerson,
    getListWorkEffortTypes,
    searchCompany,
    addOpportunity,
    uploadImage,
    dataWorkEffort,
    AddAttachmentForWorkEffort,
    UpdateWorkEffortDescriptions,
    UpdateWorkEffortName,
    updateStatusAttachmentByIds,
    createCompany,
    actionLinkImage,
    actionLinkLocation,
    getOpportunityById,
    getPersonByPartyId,
    updateWorkEffortProcessStatus,
    getListWorkEfforts,
    createWorkEfforts,
    getListTodoByid,
  };
}
