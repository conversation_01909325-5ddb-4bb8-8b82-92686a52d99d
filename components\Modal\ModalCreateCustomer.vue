<template>
  <!-- Modal Backdrop with Blur Effect -->
  <Transition
    name="modal"
    enter-active-class="transition-all duration-300 ease-out"
    leave-active-class="transition-all duration-200 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isVisible"
      class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[100] p-4 text-sm"
      @click.self="closeModal"
    >
      <!-- Modal Container -->
      <Transition
        name="modal-content"
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-200 ease-in"
        enter-from-class="opacity-0 scale-95 translate-y-4"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-95 translate-y-4"
      >
        <div
          v-if="isVisible"
          class="bg-white w-full max-w-2xl shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-300 ease-in-out max-h-[90vh] flex flex-col"
        >
          <!-- Header -->
          <div class="bg-primary px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <button
                @click="closeModal"
                class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 group"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 transform group-hover:-translate-x-1 transition-transform duration-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                <span class="text-sm font-medium">Trở lại</span>
              </button>
            </div>

            <h2
              class="text-xl font-bold text-white flex items-center space-x-2"
            >
              <span>Thêm khách hàng</span>
            </h2>

            <button
              @click="handleAddCustomer"
              :disabled="isLoading || !isFormValid"
              type="button"
              class="bg-white text-primary px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
            >
              <svg
                v-if="isLoading"
                class="animate-spin h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span>{{ isLoading ? "Đang lưu..." : "Lưu" }}</span>
            </button>
          </div>

          <!-- Form Content -->
          <div class="flex-1 overflow-y-auto">
            <form class="p-6 space-y-2">
              <!-- Personal Information Section -->
              <div class="space-y-2">
                <h3
                  class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <span>Thông tin cá nhân</span>
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Name Field -->
                  <div class="space-y-2">
                    <label
                      class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                    >
                      <span>Họ và tên</span>
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model="name"
                        type="text"
                        placeholder="Nhập họ và tên"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.name,
                        }"
                      />
                      <div
                        v-if="errors.name"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.name }}
                      </div>
                    </div>
                  </div>

                  <!-- Phone Field -->
                  <div class="space-y-2">
                    <label
                      class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                    >
                      <span>Điện thoại</span>
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model="phone"
                        type="tel"
                        placeholder="Nhập số điện thoại"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.phone,
                        }"
                      />
                      <div
                        v-if="errors.phone"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.phone }}
                      </div>
                    </div>
                  </div>

                  <!-- Birth Date Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Ngày sinh</label
                    >
                    <input
                      v-model="birthDate"
                      type="date"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <!-- Email Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Email</label
                    >
                    <div class="relative">
                      <input
                        v-model="email"
                        type="email"
                        placeholder="Nhập email"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.email,
                        }"
                      />
                      <div
                        v-if="errors.email"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.email }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Address Information Section -->
              <div class="space-y-2">
                <h3
                  class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Thông tin địa chỉ</span>
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Address Field -->
                  <div class="space-y-2 md:col-span-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Địa chỉ</label
                    >
                    <input
                      v-model="address"
                      type="text"
                      placeholder="Nhập địa chỉ"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <!-- Province Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Thành phố/Tỉnh</label
                    >
                    <select
                      v-model="selectedCity"
                      @change="fetchDistricts"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Chọn thành phố/tỉnh</option>
                      <option
                        v-for="province in provinces"
                        :key="province.geoId"
                        :value="province.geoId"
                      >
                        {{ province.geoName }}
                      </option>
                    </select>
                  </div>

                  <!-- District Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Quận/Huyện</label
                    >
                    <select
                      v-model="selectedDistrict"
                      @change="fetchWards"
                      :disabled="!selectedCity"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    >
                      <option value="">Chọn quận/huyện</option>
                      <option
                        v-for="district in districts"
                        :key="district.geoId"
                        :value="district.geoId"
                      >
                        {{ district.geoName }}
                      </option>
                    </select>
                  </div>

                  <!-- Ward Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Phường/Xã</label
                    >
                    <select
                      v-model="selectedWard"
                      :disabled="!selectedDistrict"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    >
                      <option value="">Chọn phường/xã</option>
                      <option
                        v-for="ward in wards"
                        :key="ward.geoId"
                        :value="ward.geoName"
                      >
                        {{ ward.geoName }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, onMounted, computed, watch } from "vue";

// Composables
const { writeLog } = useLogger();
const { name, phone, email, birthDate, handleCreateCustomer } = useCustomer();

// Use tab-isolated context instead of cookies
const { orgId, storeId } = useTabContext();
const authStore = useAuthStore();
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { $sdk } = useNuxtApp();

// Props & Emits
const emit = defineEmits(["dataCreateCustomer", "closeModelUser"]);
const { nameCusTomer, phoneCusTomer, isManagerCustomer } = defineProps([
  "nameCusTomer",
  "phoneCusTomer",
  "isManagerCustomer",
]);

// Reactive Data
const user = computed(() => authStore.user);
const isVisible = ref(true);
const isLoading = ref(false);
const address = ref("");

// Location Data
const provinces = ref([]);
const districts = ref([]);
const wards = ref([]);
const selectedCity = ref("");
const selectedDistrict = ref("");
const selectedWard = ref("");

// Form Validation
const errors = ref({
  name: "",
  phone: "",
  email: "",
});

// Validation Rules
const validateName = (value) => {
  if (!value || value.trim().length === 0) {
    return "Họ và tên là bắt buộc";
  }
  if (value.trim().length < 2) {
    return "Họ và tên phải có ít nhất 2 ký tự";
  }
  return "";
};

const validatePhone = (value) => {
  if (!value || value.trim().length === 0) {
    return "Số điện thoại là bắt buộc";
  }
  const phoneRegex = /^[0-9]{10,11}$/;
  if (!phoneRegex.test(value.replace(/\s/g, ""))) {
    return "Số điện thoại không hợp lệ";
  }
  return "";
};

const validateEmail = (value) => {
  if (value && value.trim().length > 0) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return "Email không hợp lệ";
    }
  }
  return "";
};

// Form Validation Computed
const isFormValid = computed(() => {
  return (
    name.value &&
    name.value.trim().length >= 2 &&
    phone.value &&
    validatePhone(phone.value) === "" &&
    validateEmail(email.value) === ""
  );
});

// Watchers for real-time validation
watch(name, (newValue) => {
  errors.value.name = validateName(newValue);
});

watch(phone, (newValue) => {
  errors.value.phone = validatePhone(newValue);
});

watch(email, (newValue) => {
  errors.value.email = validateEmail(newValue);
});
// Methods
const closeModal = () => {
  isVisible.value = false;
  emit("closeModelUser", isVisible.value);
};

const handleAddCustomer = async () => {
  // Validate form before submission
  errors.value.name = validateName(name.value);
  errors.value.phone = validatePhone(phone.value);
  errors.value.email = validateEmail(email.value);

  if (!isFormValid.value) {
    return;
  }

  isLoading.value = true;

  const body = {
    time: new Date().getTime(),
    customerType: "POS_CREATE_CUSTOMER",
    platform: "WEB",
  };

  try {
    const response = await handleCreateCustomer();
    if (response) {
      if (isManagerCustomer) {
        // Tạo một mảng tạm
        const tempArray = [];
        tempArray.push(response);
        customerStore.handleAddCustomerFirst(tempArray);
      }

      if (orderStore.orderDetail) {
        await orderStore.addCustomerToOrder(response, "");
        await orderStore.getDataShippingAddress(response?.id);
      }

      // Success notification
      closeModal();
    } else {
      useNuxtApp().$toast?.error("Thêm khách hàng thất bại");
    }
  } catch (error) {
    console.error("Error creating customer:", error);
    useNuxtApp().$toast?.error("Có lỗi xảy ra khi thêm khách hàng");

    writeLog({
      timestamp: new Date(),
      user_id: user.value.id,
      action: "Customer",
      status: "error",
      data: body,
      output: error,
      org_id: orgId.value,
      store_id: storeId.value,
      module_from: "WEB_POS",
      module_to: "customerService",
      type: "ADD",
    });
  } finally {
    isLoading.value = false;
  }
};

// Location API Methods
const getProvinces = async () => {
  try {
    const response = await $sdk.user.getProvinces();
    provinces.value = response;
  } catch (error) {
    console.error("Error fetching provinces:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách tỉnh/thành phố");
  }
};

const fetchDistricts = async () => {
  if (!selectedCity.value) return;

  try {
    const response = await $sdk.user.getDistricts(selectedCity.value);
    districts.value = response;
    selectedDistrict.value = "";
    wards.value = [];
    selectedWard.value = "";
  } catch (error) {
    console.error("Error fetching districts:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách quận/huyện");
  }
};

const fetchWards = async () => {
  if (!selectedDistrict.value) return;

  try {
    const response = await $sdk.user.getWards(selectedDistrict.value);
    wards.value = response;
    selectedWard.value = "";
  } catch (error) {
    console.error("Error fetching wards:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách phường/xã");
  }
};

// Lifecycle
onMounted(() => {
  getProvinces();

  // Initialize form with props data
  if (nameCusTomer) {
    name.value = nameCusTomer;
  }
  if (phoneCusTomer) {
    phone.value = phoneCusTomer;
  }
});
</script>

<style scoped>
/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes for transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(1rem);
}
</style>
