<template>
  <input ref="inputRef" type="text" class="" />
</template>

<script setup>
import { ref, toRefs, watch } from "vue";
import { useCurrencyInput } from "vue-currency-input";
import { watchDebounced } from "@vueuse/core";

// Nhận props
const props = defineProps({
  modelValue: Number,
  options: Object,
});

// Khai báo emit
const emit = defineEmits(["update:modelValue"]);

// Sử dụng currency input
const { inputRef, numberValue } = useCurrencyInput(props.options, false);

// Theo dõi numberValue và phát sự kiện sau khi debounce
watchDebounced(
  numberValue,
  (value) => {
    emit("update:modelValue", value);
  },
  { debounce: 10 }
);
</script>
