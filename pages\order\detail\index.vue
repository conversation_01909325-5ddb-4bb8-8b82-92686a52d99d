<template>
  <div class="flex items-center justify-center">
    <div
      class="bg-white rounded p-2 max-w-full w-full overflow-y-auto h-[calc(100vh-4rem)]"
    >
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
        <InfoOrderDetail class="order-1 md:order-none"></InfoOrderDetail>
        <InfoCustomer class="order-2 md:order-none"></InfoCustomer>
        <InfoNote class="order-3 md:order-none"></InfoNote>
      </div>

      <div class="mt-4 ">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
          <ListProduct class="order-1 md:order-none"></ListProduct>
          <InfoPaymentDetail class="order-2 md:order-none"></InfoPaymentDetail>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
          <HistoryPayment class="order-1 md:order-none"></HistoryPayment>
          <RelativeOrder class="order-2 md:order-none"></RelativeOrder>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InfoOrderDetail from "~/components/DetailOrder/InfoOrderDetail.vue";

useHead({
  title: "Thông tin chi tiết đơn hàng",
  meta: [
    {
      name: "description",
      content: "Thông tin chi tiết đơn hàng",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Thông tin chi tiết đơn hàng",
});
const orderStore = useOrderStore();
const route = useRoute();
onMounted(async () => {
  await orderStore.getOrderById(route.query.orderId as string);
});
watch(
  () => route.query.orderId,
  async (newId, oldId) => {
    if (newId !== oldId) {
      orderStore.getOrderById(newId as string);
    }
  }
);
</script>
