<template>
  <section
    class="bg-white text-gray-800 px-4 pt-2 w-full md:w-2/3 flex flex-col gap-2 rounded-lg"
  >
    <div class="flex flex-col gap-2">
      <div class="flex items-center flex-wrap">
        <a
          class="text-xl font-bold text-primary text-nowrap ml-1"
          :href="transactionId"
          target="_blank"
          >Link Thanh toán</a
        >
      </div>
    </div>
    <div>
      <div class="mx-1 font-semibold">Chia sẻ link thanh toán</div>
      <div class="flex flex-nowrap">
        <input
          v-model="transactionId"
          type="text"
          placeholder="Link thanh toán"
          class="border mt-1 border-gray-300 rounded-md p-2 w-[240px] focus:outline-none"
        />
        <span class="flex text-nowrap items-center p-2" @click="handleCopy">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
            />
          </svg>
        </span>
      </div>
      <div class="flex items-center gap-3 mt-1">
        <span class="text-white flex items-center gap-3 mt-2 mb-5"
          ><SocialShare
            v-for="network in ['facebook', 'twitter', 'line']"
            :key="network"
            :network="network"
            :styled="true"
            :label="false"
            :url="transactionId"
          />
        </span>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const props = defineProps(["orderDetails", "orderId"]);
const transactionId = ref<string>("");
const route = useRoute();

// Use tab-isolated context instead of cookies
const { storeId } = useTabContext();
const productStoreLink = route.query.productStoreLink;
const handleCopy = async () => {
  await navigator.clipboard.writeText(transactionId.value);
  useNuxtApp().$toast.success("Đã lưu vào clipboard");
};
const handleOpenLink = (): void => {
  window.open(transactionId.value, "_blank");
};
const handleclickUrl = () => {
  window.open(transactionId.value, "_blank");
};
onMounted(() => {
  transactionId.value = `${productStoreLink}/thanh-toan?id=${route.query.id}&orderId=${route.query.orderId}&orgId=${route.query?.orgId}&storeId=${storeId.value}`;
});
const updateTransactionId = () => {
  transactionId.value = `${productStoreLink}/thanh-toan?id=${route.query.id}&orderId=${route.query.orderId}&orgId=${route.query?.orgId}&storeId=${storeId.value}`;
};

watch(() => route.query.orderId, updateTransactionId);
watch(() => storeId.value, updateTransactionId);
watch(() => productStoreLink, updateTransactionId);
</script>

<style scoped></style>
