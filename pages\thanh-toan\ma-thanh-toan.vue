<template>
  <div class="h-screen flex flex-col">
    <!-- Header -->
    <div
      class="bg-primary h-[50px] w-full flex items-center fixed top-0 left-0 z-10"
    >
      <div class="text-white font-bold flex gap-2 ml-4">
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="h-6 w-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
            />
          </svg>
        </span>
        <span>
          {{ route.query.orgId ? route.query.orgId : route.query.partnerCode }}
        </span>
      </div>
    </div>

    <!-- Nội dung cuộn -->
    <div class="mt-[50px] flex-1 overflow-y-auto mb-2 bg-secondary">
      <!-- desktop -->
      <div class="md:block hidden">
        <PaymentByPaymentId
          :dataPayment="dataPayment"
          :dataOrder="dataOrder"
          :dataPaymentMethod="dataPaymentMethod"
        ></PaymentByPaymentId>
      </div>
      <!-- mobile -->
      <div class="block md:hidden bg-white">
        <PaymentByPaymentId
          :dataPayment="dataPayment"
          :dataOrder="dataOrder"
          :dataPaymentMethod="dataPaymentMethod"
          :isMobile="true"
        ></PaymentByPaymentId>
      </div>
    </div>
  </div>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
</template>
<script setup>
useHead({
  title: "Thanh toán mã thanh toán",
  meta: [
    {
      name: "description",
      content: "Thanh toán mã thanh toán",
    },
  ],
});
const route = useRoute();
const storeId = useCookie("storeId");
////
const { paymentInfo } = usePayment();
const { getOrderByIdNoLogin, updateStatusApproved } = useOrder();
const { hasPermission, setOrgId, setStore } = usePermission();
const { getPaymentMethodTypes, paymentMethods, paymentStatus } = usePayment();
const dataPayment = ref();
const dataOrder = ref([]);
const dataPaymentMethod = ref();
const handleGetPaymentInfo = async () => {
  try {
    const response = await paymentInfo(route.query.paymentId);
    if (!response) return;

    dataPayment.value = response;
    const orderPromises = dataPayment.value.items.map((order) =>
      getOrderByIdNoLogin(
        route.query.orgId || route.query?.partnerCode,
        "ALL",
        order.orderId
      )
    );
    const orderResponses = await Promise.all(orderPromises);
    dataOrder.value = orderResponses
      .map((res) => res?.data)
      .filter((order) => order !== undefined);
  } catch (error) {
    console.error("Error fetching payment info:", error);
  }
};

const handleGetPaymentMethod = async () => {
  try {
    const response = await paymentMethods();
    dataPaymentMethod.value = response.filter(
      (item) =>
        ![
          "manual",
          "clearing_debt",
          "portal",
          "refund",
          "ecomos",
          "wallet",
          "agent",
          "pos",
          "payon",
          "payos",
          "cybersource",
        ].includes(item.code) // Loại bỏ các mã không mong muốn
    );
  } catch (error) {
    throw error;
  }
};

const checkPaymentStatus = async () => {
  try {
    const response = await paymentStatus(route.query.paymentId);
    console.log("response", response);
  } catch (error) {
    throw error;
  }
};
const isLoading = ref(false);
onMounted(async () => {
  //
  //
  isLoading.value = true;
  await setOrgId(route.query.orgId || route.query.partnerCode);
  await setStore(route.query.storeId);
  try {
    await handleGetPaymentInfo();
    await handleGetPaymentMethod();
  } catch (error) {
    throw error;
  }
  isLoading.value = false;
});
</script>
