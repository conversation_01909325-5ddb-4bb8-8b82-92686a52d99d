<template>
  <div class="flex flex-col">
    <div class="flex items-center justify-between">
      <span v-if="add" class="text-sm font-bold">
        Tìm <PERSON> hàng <span class="text-red-500 text-xs">*</span>
      </span>
      <span v-else></span>
      <div @click="handleAdd" class="text-primary">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-6"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
          />
        </svg>
      </div>
    </div>
    <div v-if="isAlert && add" class="text-red-600 mb-2">
      Không có thông tin cửa hàng vui lòng tạo mới
    </div>
    <div>
      <div class="flex w-full gap-2 justify-between">
        <div v-if="add" class="relative w-full">
          <input
            class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
            type="text"
            placeholder="Tìm tên doanh nghiệp, số điện thoại,..."
            v-model="keyword"
          />
          <span
            class="absolute top-[50%] -translate-y-[50%] cursor-pointer right-0 h-full py-[9px] px-[9px] flex justify-center items-center bg-primary-light rounded-[25px] w-[80px]"
            :style="{ borderRadius: '0 5px 5px 0' }"
            @click="handleSearch"
          >
            <div>
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="#fff"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </span>
            </div>
          </span>
        </div>
      </div>

      <div
        v-if="dataSearch && dataSearch.length > 0 && keyword && add"
        class="flex flex-col gap-1 border rounded border-[#3F51B5] mt-2 p-2 h-auto max-h-[200px] overflow-auto"
      >
        <div
          v-for="c in dataSearch"
          :key="c.id"
          @click="handleClickCompany(c)"
          class="w-full h-auto p-2 mt-2 cursor-pointer bg-white border-b-[#00000044] border rounded"
        >
          <div class="flex justify-between">
            <div>
              <div>
                <span class="font-semibold">Tên doanh nghiệp:</span>
                {{ c.name }} | {{ c.phone }} | {{ c.address }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="!add" className="flex flex-col">
    <span className="pb-2 text-sm font-bold">
      Số điện thoại <span className="text-red-500 text-xs">*</span>
    </span>
    <input
      className="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-[16px] outline-none rounded-md bg-bgGray"
      type="text"
      v-model="phone"
      placeholder="Số điện thoại"
    />
  </div>
</template>

<script setup>
const emit = defineEmits(["company"]);

const isAlert = ref(false);
const keyword = ref();
const { searchCompany } = useCheckin();
const dataSearch = ref();
const phone = ref();
const handleKeyDown = (event) => {
  // xử lý sự kiện key down
};

const handleSearchCompany = (keyword, param) => {
  console.log("dang search company");
  // xử lý tìm kiếm công ty
};

const handleClickCompany = (company) => {
  keyword.value = `${company.name}| ${company.address}`;
  emit("company", company);
  dataSearch.value = null;
};

const search = ref();
const add = ref(true);
const handleAdd = () => {
  add.value = !add.value;
};
const handleSearch = async () => {
  const response = await searchCompany(keyword.value, 10);
  dataSearch.value = response;
  if (dataSearch.value.length === 0) {
    isAlert.value = true;
  } else {
    isAlert.value = false;
  }
};
watch(phone, (newVal, oldVal) => {
  emit("company", newVal);
});
watch(keyword, (newVal, oldVal) => {
  isAlert.value = false;
  if (newVal === "") {
    dataSearch.value = null;
  }
});
watch();
</script>
