<template>
  <div v-if="isOpen">
    <div class="w-full absolute z-10">
      <div
        class="mt-3 lg:mt-[5px] bg-secondary overflow-y-scroll md:max-h-[70vh] max-h-[70vh] border border-gray-200 rounded p-1"
      >
        <div
          class="p-2 cursor-pointer rounded mb-1 bg-white border hover:bg-gray-100"
          v-for="product in products"
          :key="product.id"
          @click="addToCart(product)"
        >
          <ItemProductSearch :product="product"></ItemProductSearch>
          <div class="flex text-sm"></div>
        </div>
      </div>
    </div>
    <div v-if="isModalOpen">
      <ModalProductDetail
        :isOpen="isModalOpen"
        :productId="productId"
        @close="closeModal"
      />
    </div>
    <ConfirmDialog
      v-if="isEditOrder"
      :title="`Thông báo`"
      :message="`Đơn hàng đang ở trạng thái ch<PERSON>h thức bạn có muốn điều chỉnh sản phẩm`"
      @confirm="confirm"
      @cancel="cancel"
    ></ConfirmDialog>
    <div
      v-if="isLoading"
      class="fixed z-[999999999] h-screen inset-0 flex items-center justify-center bg-black bg-opacity-50 pointer-events-none"
    >
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from "vue"; // Import necessary functions
// Lazy load heavy components
const ModalProductDetail = defineAsyncComponent(() =>
  import("~/components/ui/feedback/ModalProductDetail.vue")
);
const isModalOpen = ref(false);
const productId = ref("");
const openModal = () => {
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
  close();
};

const emit = defineEmits(["close"]);
const orderStore = useOrderStore();
const close = () => {
  emit("close");
};

// Define props
const props = defineProps(["products", "isOpen"]);
const isLoading = ref(false);
const isEditOrder = ref(false);
const orderDetail = computed(() => orderStore.orderDetail);
const productDraft = ref();
const addToCart = async (product) => {
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  if (product.subType !== "VARIABLE") {
    // kiểm tra trạng thái đơn chính thức có popup
    if (orderDetail.value?.status === "APPROVED") {
      isEditOrder.value = true;
      productDraft.value = product;
    } else {
      isLoading.value = true;
      await orderStore.addProductToOrder(product); // Use product parameter instead of props.product
      isLoading.value = false;
      close();
    }
  } else {
    productId.value = product.id; // Use product parameter instead of props.product
    openModal();
  }
};
const confirm = async () => {
  isEditOrder.value = false;
  isLoading.value = true;
  await orderStore.addProductToOrder(productDraft.value); // Use product parameter instead of props.product
  isLoading.value = false;
  close();
};
const cancel = () => {
  isEditOrder.value = false;
};
</script>
