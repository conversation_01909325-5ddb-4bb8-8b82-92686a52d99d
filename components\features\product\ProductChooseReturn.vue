<template>
  <li
    class="text-gray-700 text-sm sm:text-base flex items-center gap-2 w-auto my-1 cursor-pointer"
    @click="handleChooseItem"
  >
    <img
      :src="image || 'https://via.placeholder.com/300'"
      alt=""
      class="object-contain w-10 h-10 mt-1 rounded-lg"
      loading="lazy"
    />
    <div class="w-full">
      <h3 class="text-sm">
        {{ product.orderItemName }}
      </h3>
      <div class="flex items-center justify-between">
        <div>
          <div>Đơn giá</div>
          <div>{{ formatCurrency(product?.realPriceSell?.amount) }}</div>
        </div>
        <div>
          <div>Số lượng</div>
          <div>Số lượng</div>
        </div>
        <div>
          <div>Thành tiền</div>
          <div>1000</div>
        </div>
      </div>
    </div>
    <div>Xóa</div>
  </li>
</template>

<script setup>
const props = defineProps(["product"]);
const productStore = useProductStore();
const image = ref("");
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  if (props.product.variant.id) {
    const url = getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
    image.value = url;
  }
});
const handleChooseItem = async () => {
  console.log("sản phẩm mình chọn là", props.product);
};
</script>
