<template>
  <div class="overflow-x-auto h-[calc(100svh-16rem)]" ref="scrollContainer">
    <table class="min-w-full border-collapse border border-gray-300">
      <colgroup>
        <col class="w-1/12" />
        <col class="w-2/12" />
        <col class="w-2/12" />
        <col class="w-2/12" />
        <col class="w-1/12" />
        <col class="w-1/12" />
        <col class="w-2/12" />
        <col class="w-4/48" />
      </colgroup>

      <thead class="sticky top-0">
        <tr class="bg-gray-100">
          <th class="border border-gray-300 px-4 py-2 text-left">STT</th>
          <th class="border border-gray-300 px-4 py-2 text-left">
            M<PERSON> khách hàng
          </th>
          <th class="border border-gray-300 px-4 py-2 text-left">Tên</th>
          <th class="border border-gray-300 px-4 py-2 text-left">
            <PERSON><PERSON> điện thoại
          </th>
          <th class="border border-gray-300 px-4 py-2 text-left">Ng<PERSON><PERSON> sinh</th>
          <th class="border border-gray-300 px-4 py-2 text-left">Email</th>
          <th class="border border-gray-300 px-4 py-2 text-left">Địa chỉ</th>
          <th class="border border-gray-300 px-4 py-2 text-center">
            <input
              type="checkbox"
              class="accent-primary w-4 h-4 cursor-pointer text-center"
              v-model="customerStore.isCheckGlobal"
            />
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="loading" v-for="item in 10" class="animate-pulse">
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td
            class="border border-gray-300 px-4 py-2 flex items-center justify-center"
          >
            <div class="bg-secondary w-6 h-6"></div>
          </td>
        </tr>
        <tr
          v-else
          v-for="(customer, index) in customerStore.listCustomer"
          :key="customer.id"
        >
          <ItemTableCustomer
            :customer="customer"
            :index="index"
          ></ItemTableCustomer>
        </tr>
        <tr v-if="isLoading" class="animate-pulse">
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-auto mx-8 h-6"></div>
          </td>
          <td class="border border-gray-300 px-4 py-2">
            <div class="bg-secondary w-6 h-6"></div>
          </td>
        </tr>
      </tbody>
    </table>
    <div></div>
  </div>
</template>

<script setup lang="ts">
const { scrollCustomer } = useCustomer();
const scrollContainer = ref(null);
const customerStore = useCustomerStore();
// const isCheckGlobal = computed(() => customerStore.isCheckGlobal);
let hasMoreData = ref(true);
let isLoading = ref(false);
useInfiniteScroll(
  scrollContainer,
  async () => {
    if (!hasMoreData.value || isLoading.value) return;
    if (customerStore.listCustomer) {
      isLoading.value = true;
      hasMoreData.value = await scrollCustomer();
      isLoading.value = false;
    }
  },
  {
    distance: 10,
  }
);
// const isCheckGlobal = ref(false);
// const { loading } = useCustomer();
const props = defineProps(["loading"]);
</script>
