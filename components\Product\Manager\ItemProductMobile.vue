<template>
  <div class="p-4">
    <div class="flex gap-3">
      <!-- Product Image -->
      <div class="flex-shrink-0">
        <NuxtImg
          :src="handleGetProductImage(product?.id)"
          alt="Product Image"
          class="w-16 h-16 rounded-lg object-contain border border-gray-200"
          loading="lazy"
        />
      </div>

      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 truncate mb-1">
          {{ product?.title }}
        </h3>

        <div class="space-y-1 text-sm text-gray-600">
          <div class="flex items-center gap-4">
            <div>
              <span class="font-medium">ID:</span>
              <span class="text-gray-800">{{ product?.id }}</span>
            </div>
            <div>
              <span class="font-medium">SKU:</span>
              <span class="text-gray-800">{{ product?.sku || "N/A" }}</span>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <div>
              <span class="font-medium">Giá:</span>
              <span class="text-primary font-semibold">
                {{ formatCurrency(product?.price) }}
              </span>
            </div>
            <div v-if="product?.compareAtPrice">
              <span class="font-medium">Giá KM:</span>
              <span class="text-green-600 font-semibold">
                {{ formatCurrency(product?.compareAtPrice) }}
              </span>
            </div>
          </div>

          <!-- Danh mục -->
          <div v-if="product?.categories?.length > 0" class="text-xs">
            <span class="font-medium">Danh mục:</span>
            <div class="flex flex-wrap gap-1 mt-1">
              <!-- Hiển thị tối đa 2 danh mục trên mobile -->
              <span
                v-for="(category, index) in displayedCategories"
                :key="category.id"
                class="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs max-w-[120px] truncate inline-block"
                :title="category.title"
              >
                {{ category.title }}
              </span>
              <!-- Hiển thị "+X khác" nếu có nhiều hơn 2 danh mục -->
              <span
                v-if="product.categories.length > 2"
                class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                :title="remainingCategoriesText"
              >
                +{{ product.categories.length - 2 }} khác
              </span>
            </div>
          </div>

          <div v-if="product?.unitDTO?.name" class="text-xs">
            <span class="font-medium">Đơn vị:</span>
            <span class="bg-gray-100 px-2 py-1 rounded text-gray-700">
              {{ product?.unitDTO?.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0 flex items-center">
        <button
          @click="handleViewDetail"
          class="inline-flex items-center justify-center w-10 h-10 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          title="Xem chi tiết"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-5 h-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  product: any;
  dataUnit: any[];
}

const props = defineProps<Props>();
const emit = defineEmits(["viewDetail"]);

const { getImageProducrUrl } = usePortal();

const handleGetProductImage = (id: string) => {
  const url = getImageProducrUrl(id, "PRODUCT");
  return url;
};

const handleViewDetail = () => {
  emit("viewDetail", props.product?.id);
};

// Format currency helper
const formatCurrency = (amount: number | string) => {
  if (!amount) return "0 ₫";
  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(numAmount);
};

// Computed properties for categories
const displayedCategories = computed(() => {
  return props.product?.categories?.slice(0, 2) || [];
});

const remainingCategoriesText = computed(() => {
  if (props.product?.categories?.length > 2) {
    const remainingCategories = props.product.categories.slice(2);
    return remainingCategories.map((cat: any) => cat.title).join(", ");
  }
  return "";
});
</script>
