<template>
  <div>
    <div
      class="w-full h-full min-h-[100vh] flex items-center justify-center flex-row"
    >
      <div
        class="flex flex-col my-6 space-y-6 md:space-y-0 md:space-x-6 md:flex-row md:my-0"
      >
        <div
          class="element-container grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4"
        >
          <div
            class="w-[300px] text-center max-w-sm border m-3 border-gray-200 rounded-lg shadow bg-[#ffffff] justify-center items-center"
          >
            <div class="flex justify-end px-4 py-4"></div>
            <div>
              <div class="flex flex-col items-center pb-10">
                <img
                  class="w-24 h-24 mb-3 rounded-full shadow-lg object-cover border-2 border-[#d0d5dd]"
                  src="https://img.lovepik.com/free-png/20210928/lovepik-warehouse-png-image_401635232_wh1200.png"
                  alt="Bonnie image"
                  loading="lazy"
                />

                <h6 class="m-1 text-sm font-semibold text-black uppercase">
                  Nhập hàng
                </h6>
                <span class="text-sm text-gray-500 dark:text-gray-400"></span>
                <div class="flex mt-4 md:mt-4 no-underline">
                  <button
                    @click="
                      handlePageChange(
                        `https://portal.dev.longvan.vn/facility/manager/public/transfer-request.xhtml?orgId=${orgId}&toFacilityId=&accessToken=${token}`
                      )
                    "
                    class="uppercase sinline-flex items-center px-4 py-2 text-sm font-medium text-center text-black bg-[#ffffffdf] rounded-lg cursor-pointer dark:bg-[#f0f0f0] hover:text-white hover:bg-[#7f76de]"
                  >
                    Tạo Yêu Cầu
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="w-[300px] text-center max-w-sm border m-3 border-gray-200 rounded-lg shadow bg-[#ffffff] justify-center items-center"
          >
            <div class="flex justify-end px-4 py-4"></div>
            <div>
              <div class="flex flex-col items-center pb-10">
                <img
                  class="w-24 h-24 mb-3 rounded-full shadow-lg object-cover border-2 border-[#d0d5dd]"
                  src="https://img.lovepik.com/free-png/20210928/lovepik-warehouse-png-image_401635232_wh1200.png"
                  alt="Bonnie image"
                  loading="lazy"
                />

                <h6 class="m-1 text-sm font-semibold text-black uppercase">
                  Nhập hàng
                </h6>
                <span class="text-sm text-gray-500 dark:text-gray-400"></span>
                <div class="flex mt-4 md:mt-4 no-underline">
                  <button
                    @click="
                      handlePageChange(
                        `https://portal.dev.longvan.vn/facility/manager/public/transfer-request.xhtml?orgId=${orgId}&toFacilityId=&accessToken=${token}`
                      )
                    "
                    class="uppercase sinline-flex items-center px-4 py-2 text-sm font-medium text-center text-black bg-[#ffffffdf] rounded-lg cursor-pointer dark:bg-[#f0f0f0] hover:text-white hover:bg-[#7f76de]"
                  >
                    Nhập hàng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth"],
  name: "Điều phối kho",
});
const router = useRouter();

// Use tab-isolated context instead of cookies
const { orgId } = useTabContext();
const token = useCookie("token").value;

const handlePageChange = (path: any) => {
  if (path.startsWith("http")) {
    window.location.href = path;
  } else {
    router.push(path);
  }
};
</script>

<style scoped></style>
