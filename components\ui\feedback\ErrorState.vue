<template>
  <div class="flex flex-col items-center justify-center py-12 px-4">
    <!-- Error Icon -->
    <div class="text-red-400 mb-4">
      <svg
        class="mx-auto h-12 w-12"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
        />
      </svg>
    </div>

    <!-- Error Message -->
    <h3 class="text-lg font-medium text-gray-900 mb-2">
      {{ title }}
    </h3>
    <p class="text-gray-500 text-center mb-6 max-w-md">
      {{ message }}
    </p>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-3">
      <button
        v-if="showRetry"
        @click="handleRetry"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
      >
        <svg
          class="mr-2 h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Thử lại
      </button>

      <button
        v-if="showGoBack"
        @click="handleGoBack"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
      >
        <svg
          class="mr-2 h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10 19l-7-7m0 0l7-7m-7 7h18"
          />
        </svg>
        Quay lại
      </button>
    </div>

    <!-- Additional Help Text -->
    <div v-if="helpText" class="mt-6 text-center">
      <p class="text-sm text-gray-500">
        {{ helpText }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
  message?: string;
  helpText?: string;
  showRetry?: boolean;
  showGoBack?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: "Đã xảy ra lỗi",
  message: "Không thể tải dữ liệu. Vui lòng thử lại sau.",
  helpText: "",
  showRetry: true,
  showGoBack: false,
});

const emit = defineEmits<{
  retry: [];
  goBack: [];
}>();

const handleRetry = () => {
  emit("retry");
};

const handleGoBack = () => {
  emit("goBack");
};
</script>

<style scoped>
/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* CSS containment for performance */
.flex {
  contain: layout;
}
</style>
