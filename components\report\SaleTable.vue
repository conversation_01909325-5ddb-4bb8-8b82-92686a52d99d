<template>
  <h2 class="text-xl text-primary-light font-bold my-5"><PERSON><PERSON><PERSON> b<PERSON><PERSON> bán hàng</h2>

  <div class="overflow-x-auto">
    <table class="tableData divide-y divide-gray-200 w-full min-w-full">
      <thead class="bg-gray-50">
        <tr>
          <th
            v-for="(item, index) in listItem?.value"
            :key="index"
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider border"
          >
            {{ item.name }}
          </th>
        </tr>
      </thead>
      <tbody v-if="isItemTable" class="bg-white divide-y divide-gray-200">
        <tr v-for="(item, index) in data.reportDTOS" :key="index">
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ index + 1 }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.id }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.totalOrders }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ formatCurrency(item.totalPrice) }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">
              {{ formatCurrency(Math.floor(item.totalAmountRefund)) }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.percentRefund }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.percentSell }}</div>
          </td>
        </tr>
      </tbody>
      <tbody v-else class="bg-white divide-y divide-gray-200">
        <tr v-for="(item, index) in data?.reportDTOS" :key="index">
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ index + 1 }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">
              {{ formatCurrency(Math.floor(item.totalPrice)) }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.totalOrders }}</div>
          </td>

          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">
              {{ formatCurrency(Math.floor(item.totalAmountRefund)) }}
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.percentRefund }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap border">
            <div class="text-sm text-gray-900">{{ item.percentSell }}</div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="my-5 flex justify-end gap-4">
    <button
      v-if="isButtonExcel"
      class="bg-primary text-white px-4 py-2 rounded-md flex gap-2"
      @click="handleButtonExcel"
    >
      <span>Xuất Excel</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M7.5 7.5h-.75A2.25 2.25 0 0 0 4.5 9.75v7.5a2.25 2.25 0 0 0 2.25 2.25h7.5a2.25 2.25 0 0 0 2.25-2.25v-7.5a2.25 2.25 0 0 0-2.25-2.25h-.75m-6 3.75 3 3m0 0 3-3m-3 3V1.5m6 9h.75a2.25 2.25 0 0 1 2.25 2.25v7.5a2.25 2.25 0 0 1-2.25 2.25h-7.5a2.25 2.25 0 0 1-2.25-2.25v-.75"
        />
      </svg>
    </button>
    <button
      v-if="isButtonPrint"
      class="bg-primary text-white px-4 py-2 rounded-md flex gap-2 items-center"
      @click="handleButtonPrint"
    >
      <span>In</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-5"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
        />
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import * as XLSX from "xlsx";
const listItem = ref<any>([]);
const dataListItem = ref([]);
const isItemTable = ref(false);
const isButtonExcel = ref(true);
const isButtonPrint = ref(true);
const { data } = defineProps(["data"]);
const route = useRoute();
if (route.params.reportItem === "overview") {
  listItem.value = overviewReport;
} else if (route.params.reportItem === "employees") {
  listItem.value = staffReport;
  isItemTable.value = true;
  isButtonPrint.value = false;
} else {
  isButtonExcel.value = false;
  isButtonPrint.value = false;
}
const handleButtonExcel = () => {
  const table = document.querySelector(".tableData");
  const sheetData = XLSX.utils.table_to_sheet(table);

  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, sheetData, "Sheet1");

  XLSX.writeFile(workbook, "bao-cao.xlsx");
};

const handleButtonPrint = () => {
  const printContents = document.querySelector(".tableData").outerHTML ;
  const originalContents = document.body.innerHTML;

  document.body.innerHTML = printContents;
  window.print();
  document.body.innerHTML = originalContents;
  window.location.reload();
};
</script>

<style scoped></style>
