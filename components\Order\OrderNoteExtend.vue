<template>
  <div class="flex flex-col m-0 px-2 pb-2 bg-white rounded">
    <div class="flex justify-between">
      <h3 class="text-sm font-semibold text-primary">Ghi chú</h3>
      <!-- <div class="flex items-center">
        <button
          class="flex items-center justify-center w-4 h-4 rounded-full text-primary"
          @click="toggleContent"
        >
          <svg
            v-if="!isShow"
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 15l7-7 7 7"
            ></path>
          </svg>
        </button>
      </div> -->
    </div>

    <div class="grid grid-cols-1 gap-2 mt-2 animate-popup" v-if="isShow">
      <div class="flex flex-col gap-2">
        <textarea
          rows="2"
          id="note"
          v-model="orderDescription"
          class="py-1 px-2 w-full md:text-sm text-base rounded outline-none border bg-secondary"
          placeholder="Ghi chú đơn hàng"
          @blur="updateOrderDescription"
          :disabled="isNotDraft || !orderStore.orderDetail"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useOrderStore } from "@/stores/order";

const orderStore = useOrderStore();
const isNotDraft = computed(() => orderStore.isNotDraft);

const note = ref("");

const orderDescription = computed({
  get() {
    return orderStore.orderDetail?.order?.note || "";
  },
  set(value) {
    note.value = value;
  },
});

const isShow = ref(true);

const toggleContent = () => {
  isShow.value = !isShow.value;
};

const updateOrderDescription = () => {
  orderStore.updateDescriptionToOder(note.value);
};
</script>
