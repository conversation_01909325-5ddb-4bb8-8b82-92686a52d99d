<template>
  <div
    class="bg-card relative text-card-foreground p-2 rounded-lg border flex items-center overflow-hidden"
    :class="{ 'opacity-50': !isChecked }"
    @click="toggleCheck"
  >
    <input
      type="checkbox"
      id="choose-me"
      v-model="isChecked"
      @click.stop="toggleCheck"
      class="w-4 h-4 accent-primary rounded-full cursor-pointer"
    />
    <label for="product1" class="cursor-pointer flex items-center w-full ml-2">
      <NuxtImg
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="flex-shrink-0 w-16 h-16 rounded-lg object-contain"
        loading="lazy"
        preload
      />
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-sm line-clamp-2 font-semibold">
            {{
              `${product?.variant.title} (#${product?.variant?.id}${
                product?.variant?.sku ? ` - SKU: ${product?.variant?.sku}` : ``
              })`
            }}
          </h3>
        </div>
        <div class="flex items-center justify-between mt-2">
          <p class="text-primary font-semibold">
            {{ formatCurrency(product.realPriceSell.amount) }}
          </p>
          <div class="flex items-center">
            <button
              v-if="!isDecreaseQuantity"
              @click.prevent.stop="decreaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
            >
              -
            </button>
            <button
              @click.prevent.stop="handleQuantity"
              v-else
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
              :disabled="!isChecked"
            >
              -
            </button>
            <input
              type="number"
              min="1"
              v-model="product.currentQuantity"
              @input="updateTotalPrice"
              @blur="updateQuantity"
              class="w-10 h-6 text-center border border-input rounded-md mx-2"
            />
            <button
              @click.prevent.stop="increaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary-foreground rounded-md"
              :disabled="!isChecked"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </label>

    <div
      v-if="isUpdating"
      class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
    >
      <svg
        class="animate-spin h-5 w-5 text-primary"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
        ></path>
      </svg>
    </div>
    <ConfirmDialog
      v-if="isDialogVisible"
      :title="'Xác nhận'"
      :message="'Bạn có chắc chắn muốn xoá sản phẩm này?'"
      @confirm="removeFromCartConfirmed"
      @cancel="handleCancel"
    />
    <LoadingSpinner v-if="isLoading" />
  </div>
</template>

<script setup>
import debounce from "lodash/debounce";
const productStore = useProductStore();
const props = defineProps({
  product: Object,
});
const editPrice = ref(false);
const newPrice = ref();
const discountType = ref("MONEY");
const discountValue = ref(props.product.discount?.amount || 0);
const originalPrice = ref(props.product?.amount);
const isDialogVisible = ref(false);
const isChecked = ref(false);
const orderStore = useOrderStore();
const isUpdating = ref(false);
const image = ref("");
const route = useRoute();
const isDecreaseQuantity = ref(false);
const app = useNuxtApp();
const { enableProductDiary } = useOrder();
const orderDetail = computed(() => orderStore.orderDetail);
const isLoading = ref(false);
const handleQuantity = () => {
  app.$toast.warning("Sản phẩm có số lượng là 1, không thể giảm");
};
const handleCancel = () => {
  orderStore.handleDisibleOrderItems();
};
const toggleCheck = async (event) => {
  event.stopPropagation();
  isChecked.value = !isChecked.value;
  isLoading.value = true;
  await enableProductDiary(orderDetail.value.id, props.product.id);
  await orderStore.getOrderById(orderDetail.value.id);
  isLoading.value = false;
};

const increaseQuantity = async () => {
  isUpdating.value = true;
  const newQuantity = props.product.currentQuantity + 1;
  await orderStore.updateQuantity(props.product.id, newQuantity);
};
const decreaseQuantity = async () => {
  if (props.product.currentQuantity > 1) {
    isUpdating.value = true;
    const newQuantity = props.product.currentQuantity - 1;
    await orderStore.updateQuantity(props.product.id, newQuantity);
  } else {
    isDecreaseQuantity.value = true;
  }
};
watch(
  () => props.product.currentQuantity,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      isUpdating.value = false;
    }
  }
);
watch(
  () => props.product.currentQuantity,
  (newVal, oldVal) => {
    if (newVal <= 1) {
      isDecreaseQuantity.value = true;
    } else {
      isDecreaseQuantity.value = false;
    }
  }
);
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  const url = getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
  image.value = url;
  if (props.product.currentQuantity === 1) {
    isDecreaseQuantity.value = true;
  }
});
const savePrice = async () => {
  try {
    await saleStore.updatePriceInOrder(
      currentOrderId,
      props.product.id,
      newPrice.value
    );
    props.product.price = newPrice.value;
    editPrice.value = false;
  } catch (error) {
    console.error("Error updating price:", error);
  }
};
// hàm kiểm tra thay đổi để able sản phẩm lại
// watch(
//   () => toggleCheck.value,
//   (newVal, oldVal) => {
//     console.log("newVal", newVal);
//   }
// );
const updateTotalPrice = () => {
  originalPrice.value = props.product.amount * props.product.currentQuantity;
};

const showRemoveDialog = () => {
  isDialogVisible.value = true;
};

const removeFromCartConfirmed = async () => {
  isChecked.value = false;
  emits("product", props.product);
};

const saveDiscount = async () => {
  try {
    if (discountType.value === "PERCENT") {
      if (discountValue.value < 0) {
        discountValue.value = 0;
      } else if (discountValue.value > 100) {
        discountValue.value = 100;
      }
    } else if (discountType.value === "MONEY") {
      if (discountValue.value < 0) {
        discountValue.value = 0;
      } else if (discountValue.value > props.product.originalTotalPrice) {
        discountValue.value = props.product.originalTotalPrice;
      }
    }

    await saleStore.updateDiscountProductInCart(
      currentOrderId,
      props.product.id,
      discountValue.value,
      discountType.value
    );
  } catch (error) {
    console.error("Error updating discount:", error);
  }
};
const debouncedSaveDiscount = debounce(saveDiscount, 300);
const handleDelete = () => {
  showRemoveDialog();
};
</script>
