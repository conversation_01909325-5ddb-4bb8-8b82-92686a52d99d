<template>
  <div
    class="fixed bottom-0 md:relative left-0 right-0 bg-white p-4 md:rounded"
  >
    <div class="flex items-center justify-center gap-3">
      <button
        class="text-primary border border-primary py-[7px] px-4 rounded w-full"
        @click="handleSaveDiary"
      >
        L<PERSON><PERSON> nhật ký
      </button>
      <button
        class="bg-primary text-white py-2 px-4 rounded w-full"
        @click="orderStore.createOrderFromDiary"
      >
        Tạo đơn
      </button>
    </div>
  </div>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
</template>
<script setup>
const orderStore = useOrderStore();
const route = useRoute();
const isLoading = ref(false);

const handleSaveDiary = async () => {
  await useNuxtApp().$toast.success("Lưu nhật ký thành công");

  setTimeout(() => {
    navigateTo(
      `/diaries?orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  }, 1500);
};
</script>
