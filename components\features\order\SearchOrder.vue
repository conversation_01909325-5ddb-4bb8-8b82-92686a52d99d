<template>
  <div class="mx-auto">
    <TabStoreOrder
      @setStore="handleSetStore"
      class="block md:hidden overflow-x-auto"
    ></TabStoreOrder>
    <div class="flex flex-row justify-between items-center py-2">
      <TabStoreOrder
        @setStore="handleSetStore"
        class="md:block hidden overflow-x-auto"
      ></TabStoreOrder>

      <div class="flex items-center gap-2 mt-2 md:mt-0">
        <TabChangeSearchOrder @toogleTab="handleSetTab" />
      </div>
    </div>
    <!-- Search Section -->

    <div class="p-2 rounded-lg mb-2">
      <div class="space-y-4">
        <!-- Basic Search -->
        <div
          class="grid grid-cols-2 md:grid-cols-5 gap-2 items-center"
          v-if="!isSearchDetail"
        >
          <input
            type="text"
            placeholder="Tìm kiếm mã đơn hàng..."
            class="col-span-1 min-w-[200px] py-1 px-2 text-base rounded outline-none bg-white border"
            v-model="keyword"
            @keydown.enter="handleSearch"
          />
          <input
            type="text"
            placeholder="Tìm kiếm theo ID khách hàng..."
            class="col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white"
            v-model="customerKeyWord"
            @keydown.enter="handleSearch"
          />
          <flat-pickr
            class="col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white text-center"
            placeholder="Chọn khoảng thời gian"
            :config="datePickerConfig"
            v-model="dateRange"
          />
          <InputField
            type="select"
            :defaultText="'Theo Trạng thái'"
            :selectClass="'col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white'"
            v-model="selectedStatus"
            :options="dataOrderStatus"
            @change="handleChangeStatus"
          />
          <div class="col-span-1 grid grid-cols-2 gap-2">
            <button
              @click="reset"
              class="bg-gray-200 text-gray-800 px-2 py-1 rounded-md hover:bg-gray-300 transition duration-200 flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 inline-block mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Xóa
            </button>
            <button
              @click="handleSearch"
              class="bg-primary text-white px-2 py-1 rounded-md hover:bg-blue-700 transition duration-200 flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 inline-block mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607Z"
                />
              </svg>
              Tìm
            </button>
          </div>
        </div>

        <!-- Advanced Search -->
        <div v-if="isSearchDetail" class="space-y-4">
          <!-- All Search Fields -->
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2 items-center">
            <input
              type="text"
              placeholder="Tìm kiếm mã đơn hàng..."
              class="col-span-1 min-w-[200px] py-1 px-2 text-base rounded outline-none bg-white"
              v-model="keyword"
              @keydown.enter="handleSearch"
            />
            <input
              type="text"
              placeholder="Tìm kiếm theo ID khách hàng..."
              class="col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white"
              v-model="customerKeyWord"
              @keydown.enter="handleSearch"
            />
            <flat-pickr
              class="col-span-1 min-w-[150px] py-1 px-2 rounded outline-none bg-white text-center"
              placeholder="Chọn khoảng thời gian"
              :config="datePickerConfig"
              v-model="dateRange"
            />
            <InputField
              type="select"
              :defaultText="'Theo Trạng thái'"
              :selectClass="'col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white'"
              v-model="selectedStatus"
              :options="dataOrderStatus"
              @change="handleChangeStatus"
            />
            <input
              type="text"
              placeholder="Tìm kiếm theo tên, mã sản phẩm, SKU..."
              class="col-span-1 min-w-[200px] py-1 px-2 text-base rounded outline-none bg-white"
              v-model="productKeyword"
              @keydown.enter="handleSearch"
            />
            <InputField
              type="select"
              :defaultText="'Theo phương thức thanh toán'"
              :selectClass="'col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white'"
              v-model="selectedPaymentMethod"
              :options="dataPaymentMethod"
              @change="handleChangePaymentMethod"
            />
            <InputField
              type="select"
              :defaultText="'Theo Nhân viên'"
              :selectClass="'col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white'"
              v-model="selectedEmployee"
              :options="dataEmployee"
              @change="handleEmployeeChange"
            />
            <InputField
              type="select"
              :defaultText="'Theo Trạng thái FFM'"
              :selectClass="'col-span-1 min-w-[150px] py-1 px-2 text-base rounded outline-none bg-white'"
              v-model="selectedFfmStatus"
              :options="dataFFMStatus"
              @change="handleChangeFFMStatus"
            />
            <div class="col-span-1 grid grid-cols-2 gap-2">
              <button
                @click="reset"
                class="bg-gray-200 text-gray-800 px-2 py-1 rounded-md hover:bg-gray-300 transition duration-200 flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 inline-block mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Xóa
              </button>
              <button
                @click="handleSearch"
                class="bg-primary text-white px-2 py-1 rounded-md hover:bg-blue-700 transition duration-200 flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 inline-block mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="m21 21-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607Z"
                  />
                </svg>
                Tìm
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import "tippy.js/dist/tippy.css";

import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
import type { Options } from "flatpickr/dist/types/options"; // Đúng kiểu dữ liệu
const emits = defineEmits([
  "selectedDate",
  "selectedStaff",
  "changeKeyWord",
  "clearKeyword",
  "handleSearch",
]);
const dateRange = ref<string[]>([]);
const datePickerConfig = ref<Partial<Options>>({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: (selectedDates: any) => {
    if (selectedDates.length === 2) {
      handleChangeDate(selectedDates[1].getTime(), selectedDates[0].getTime());
    }
  },
});
const selectedPaymentMethod = ref("");
const { loading } = useOrder();
const isAlert = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const keyword = ref<string>("");
const selectedStatus = ref("");
const productKeyword = ref("");
const customerKeyWord = ref("");
const dataRequest = reactive({
  keyword: keyword.value,
  product_multi_value: productKeyword.value,
  customer_multi_value: customerKeyWord.value,
  date_create_to: "" as string | null,
  date_create_from: "" as string | null,
  employee_assign: "" as string | null,
  status: "" as string | null,
  ffm_status: "" as string | null,
  payment_method: "" as string | null,
  exist_ffm_status: "",
});
const props = defineProps([
  "dataEmployee",
  "dataOrderStatus",
  "dataPaymentMethod",
]);
// const dataFFM = ref([
//   { name: "Chưa ffm", value: "false" },
//   { name: "Đã ffm", value: "true" },
// ]);
const selectedFfmStatus = ref();
// tìm kiếm theo ffm
const dataFFMStatus = ref([
  { name: "Chưa FFM", value: "Chưa FFM" },
  { name: "Đã FFM", value: "Đã FFM" },
  { name: "Đang xử lý", value: "OPEN" },
  { name: "Đang chờ DVVC lấy hàng", value: "WAIT_DELIVERY" },
  { name: "Đang giao", value: "PROCESSING_DELIVERY" },
  { name: "Hoàn thành", value: "FULFILLED" },
  { name: "Đang trả hàng", value: "RETURNING" },
  { name: "Đã trả hàng", value: "RETURNED" },
  { name: "Hủy FFM", value: "UNFULFILLED" },
]);
const handleChangeFFMStatus = () => {
  if (selectedFfmStatus.value) {
    switch (selectedFfmStatus.value) {
      case "Chưa FFM":
      case "":
        console.log("Chưa FFM");
        dataRequest.exist_ffm_status = false;
        dataRequest.ffm_status = "";
        break;
      case "Đã FFM":
        console.log("Đã FFM");
        dataRequest.exist_ffm_status = true;
        dataRequest.ffm_status = "";
        break;
      default:
        dataRequest.exist_ffm_status = "";

        dataRequest.ffm_status = selectedFfmStatus.value;

        console.log("Sau có trạng thái");
    }
  }
};

// const handleChangeFFMStatus = async (ffmStatus) => {
//   switch (selectedFfmStatus.value) {
//     case "Chưa ffm":
//     case "":
//       options.exist_ffm_status = false;
//       options.ffm_status = "";
//       break;
//     case "Đã ffm":
//       options.ffm_status = "";
//       options.exist_ffm_status = true;
//       break;
//     case "Đang xử lý":
//       options.exist_ffm_status = "";
//       options.ffm_status = "OPEN";
//       break;
//     case "Đang chờ DVVC lấy hàng":
//       options.exist_ffm_status = "";
//       options.ffm_status = "WAIT_DELIVERY";
//       break;
//     case "Đang giao":
//       options.exist_ffm_status = "";

//       options.ffm_status = "PROCESSING_DELIVERY";
//       break;
//     case "Hoàn thành":
//       options.exist_ffm_status = "";

//       options.ffm_status = "FULFILLED";
//       break;
//     case "Đang trả hàng":
//       options.exist_ffm_status = "";

//       options.ffm_status = "RETURNING";
//       break;
//     case "Đã trả hàng":
//       options.exist_ffm_status = "";

//       options.ffm_status = "RETURNED";
//       break;
//     default:
//       break;
//   }
//   console.log("options.ffm_status", options.ffm_status);
// };

// tìm kiếm theo ffm
const clearInput = () => {
  keyword.value = "";
  dataRequest.keyword = "";
  emits("handleSearch", dataRequest);
};
const handleSearch = async () => {
  emits("handleSearch", dataRequest);
};

const selectedEmployee = ref();

const handleEmployeeChange = () => {
  dataRequest.employee_assign = selectedEmployee.value;
  emits("handleSearch", dataRequest);
};
const isLoadingNavigate = ref(false);
const handleNavigate = async () => {
  let isTimeout = false;

  // Khởi động một timer 150ms
  const timer = setTimeout(() => {
    isTimeout = true;
    isLoadingNavigate.value = true; // Chỉ bật trạng thái loading nếu vượt quá 150ms
  }, 150);

  // Use tab-isolated context instead of cookies
  const { storeId, orgId } = useTabContext();

  // Thực hiện điều hướng
  await navigateTo(`/sale?orgId=${orgId.value}&storeId=${storeId.value}`);

  // Dọn dẹp timer
  clearTimeout(timer);

  // Nếu điều hướng hoàn thành trước 150ms, không bật loading
  if (isTimeout) {
    isLoadingNavigate.value = false;
  }
};
const handleChangeDate = (dateTo: string, dateFrom: string) => {
  dataRequest.date_create_to = dateTo;
  dataRequest.date_create_from = dateFrom;
  emits("handleSearch", dataRequest);
};
watch(keyword, (newKeyword) => {
  dataRequest.keyword = newKeyword;
});
watch(productKeyword, (newKeyword) => {
  dataRequest.product_multi_value = newKeyword;
});
watch(customerKeyWord, (newKeyword) => {
  dataRequest.customer_multi_value = newKeyword;
});
const reset = () => {
  selectedPaymentMethod.value = "";
  selectedStatus.value = "";
  dateRange.value = [];
  selectedEmployee.value = "";
  productKeyword.value = "";
  customerKeyWord.value = "";
  keyword.value = "";
  dataRequest.keyword = "";
  selectedFfmStatus.value = "";
  dataRequest.date_create_from = "";
  (dataRequest.ffm_status = ""), (dataRequest.exist_ffm_status = "");
  dataRequest.date_create_to = "";
  dataRequest.employee_assign = "";
  (dataRequest.customer_multi_value = ""),
    (dataRequest.payment_method = ""),
    (dataRequest.product_multi_value = "");
  dataRequest.status = "";
  dataRequest.exist_ffm_status = "";
  emits("handleSearch", dataRequest);
};
const router = useRouter();

router.beforeEach(async (to, from, next) => {
  if (to.path === `/order`) {
    selectedEmployee.value = "";
  }
  next();
});
const isSearchDetail = ref(false);
const handleSetTab = (tab: string) => {
  if (tab === "DETAIL") {
    isSearchDetail.value = true;
  } else {
    isSearchDetail.value = false;
    (dataRequest.payment_method = ""), (dataRequest.product_multi_value = "");
  }
};
const handleChangeStatus = () => {
  if (selectedStatus.value === "Tất cả") {
    dataRequest.status = "";
    return;
  }
  dataRequest.status = selectedStatus.value;
};
const handleChangePaymentMethod = () => {
  dataRequest.payment_method = selectedPaymentMethod.value;
};
const { setStore } = usePermission();

const handleSetStore = async (store: any) => {
  await setStore(store?.id);
  router.push({
    query: {
      ...router.currentRoute.value.query,
      storeId: store.id,
    },
  });
  emits("handleSearch", dataRequest);
};
</script>
