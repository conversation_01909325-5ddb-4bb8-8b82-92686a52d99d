import { defineStore } from "pinia";
 export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  birthDate: string;
  roles?: string[];
  // Add other properties as needed
}

export interface AuthState {
  token: string | null;
  user: User | null;
}

export const useAuthStore = defineStore("auth", {
  state: (): AuthState => ({
    token: null,
    user: null,
  }),
  persist: {
    storage: persistedState.cookiesWithOptions({
      sameSite: 'strict',
    }),
  },
  actions: {
    setToken(token: string | null) {
      this.token = token;
    },
    setUser(user: User | null) {
      this.user = user;
    },
    
  },
  getters: {
    getUser: (state) => state.user
  },

});
