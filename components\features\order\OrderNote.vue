<template>
  <div class="flex flex-col p-0 m-0 md:p-4 bg-white rounded">
    <div class="flex justify-between">
      <span class="text-md font-bold textShadow">Ghi chú</span>
      <div class="flex items-center">
        <!-- toggle content-->
        <button
          class="flex items-center justify-center w-4 h-4 rounded-full text-primary"
          @click="toggleContent"
        >
          <svg
            v-if="!isShow"
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            class="w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 15l7-7 7 7"
            ></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Ô ghi chú đơn hàng-->
    <div class="grid grid-cols-1 gap-2 mt-4" v-if="isShow">
      <div class="flex justify-between gap-2">
        <textarea
          @blur="console.log('blur')"
          rows="4"
          id="note"
          class="py-1 px-2 w-full text-sm rounded outline-none border bg-secondary"
          type="text"
          placeholder="Nhập ghi chú đơn hàng"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const isShow = ref(false);
const { updateNoteDiary } = useDiary();

const toggleContent = () => {
  isShow.value = !isShow.value;
};
</script>

<style scoped></style>
