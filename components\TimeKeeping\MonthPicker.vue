<template>
  <div>
    <input
      id="date-select"
      type="month"
      class="block outline-none bg-white px-2 py-1 rounded cursor-pointer"
      v-model="selectedMonth"
      @change="handleMonthChange"
    />
    <p>Đã chọn: {{ formattedMonth }}</p>
    <!-- Hiển thị giá trị đã định dạng -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// Khai báo biến để lưu tháng đã chọn
const selectedMonth = ref("");
const formattedMonth = ref(""); // Biến để lưu giá trị định dạng MM/YYYY

// Hàm chạy khi component được mount
onMounted(() => {
  // Lấy tháng và năm hiện tại
  const now = new Date();
  selectedMonth.value = `${now.getFullYear()}-${String(
    now.getMonth() + 1
  ).padStart(2, "0")}`;
  formattedMonth.value = formatMonth(selectedMonth.value); // Định dạng lúc khởi tạo
});

// Hàm để định dạng giá trị thành MM/YYYY
const formatMonth = (value: string) => {
  const [year, month] = value.split("-"); // Tách year và month
  return `${String(month).padStart(2, "0")}/${year}`; // Định dạng lại thành "MM/YYYY"
};

// Hàm xử lý khi tháng thay đổi
const handleMonthChange = (event: Event) => {
  const value = (event.target as HTMLInputElement).value; // Lấy giá trị từ input (YYYY-MM)
  selectedMonth.value = value; // Cập nhật giá trị đã chọn
  formattedMonth.value = formatMonth(value); // Cập nhật giá trị định dạng
};
</script>

<style scoped>
/* Thêm style nếu cần */
</style>
