<template>
  <div class="p-3 bg-white">
    <div
      class="items-center gap-1 border rounded-lg inline-flex relative overflow-hidden"
    >
      <div
        class="absolute inset-0 transition-all duration-300 ease-in-out bg-primary rounded-lg"
        :class="
          selected === 'Trả hàng'
            ? 'translate-x-0 w-[50%]'
            : 'translate-x-full w-[50%]'
        "
      ></div>

      <span
        @click="toggleSwitch('Trả hàng')"
        class="cursor-pointer px-2 py-1 rounded-lg relative z-10 transition-all duration-300"
        :class="selected === 'Trả hàng' ? 'text-white' : 'text-gray-700'"
      >
        Tr<PERSON> hàng
      </span>
      <span
        @click="toggleSwitch('Đổi hàng')"
        class="cursor-pointer px-2 py-1 rounded-lg relative z-10 transition-all duration-300"
        :class="selected === 'Đổi hàng' ? 'text-white' : 'text-gray-700'"
      >
        Đ<PERSON><PERSON> hàng
      </span>
    </div>
  </div>
</template>

<script setup>
const selected = ref("Trả hàng");
const emits = defineEmits(["switch"]);
const returnStore = returnOrderStore();
const toggleSwitch = (option) => {
  selected.value = option;
  returnStore.handleSwitchType(option);
};
const route = useRoute();
watch(
  () => route.query.orderReturnId,
  async (newVal, oldVal) => {
    if (newVal) {
      selected.value = "Trả hàng";
    }
  }
);
</script>
