<template>
  <div>
    <div class="mt-2 mx-2">
      <SearchBuyOrder
        @selectedDate="handleSearchDate"
        @changeKeyWord="handleChangeKeyword"
        @clearKeyword="handleClearKeyword"
      ></SearchBuyOrder>
      <div v-if="ordersStore.isAlert === 0" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </div>
    </div>
    <div ref="scrollContainer" class="md:mx-2 h-[100vh] overflow-y-auto">
      <LoadingDiary v-if="loading" :isPageOrder="true"></LoadingDiary>
      <div v-else>
        <div class="flex flex-col gap-2 mb-2 mt-2">
          <div class="flex flex-col h-full">
            <div class="bg-white md:block hidden">
              <table class="table-auto w-full text-sm">
                <thead class="sticky top-0 z-2">
                  <tr class="bg-blue-100 text-left font-semibold">
                    <th class="p-2 w-1/12 text-center">Mã đơn</th>

                    <th class="p-2 w-3/12">Kh<PERSON>ch hàng</th>
                    <th class="p-2 w-5/12">Sản phẩm</th>
                    <th class="p-2 w-1/12">Tổng giảm</th>

                    <th class="p-2 w-3/12">Thanh toán</th>
                    <th class="p-2 w-full">
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          class="size-5"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                          />
                        </svg>
                      </span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="diary in ordersStore.dataListOrder"
                    :key="diary.id"
                    class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
                  >
                    <TableDiary
                      :diary="diary"
                      :isNotDraft="true"
                      :isAgency="true"
                    ></TableDiary>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="block md:hidden">
              <div
                v-for="item in ordersStore.dataListOrder"
                :key="item.id"
                class="flex flex-col h-full"
              >
                <BuyOrder :dataOrder="item"></BuyOrder>
              </div>
            </div>
          </div>
        </div>

        <LoadingScrollDiary v-if="isLoading"></LoadingScrollDiary>
      </div>
      <div class="mb-[60px]"></div>
    </div>
  </div>
</template>

<script setup>
useHead({
  title: "Danh sách đơn mua",
  meta: [
    {
      name: "description",
      content: "Danh sách đơn mua",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Đơn mua",
});

const scrollContainer = ref(null);
const auth = useCookie("auth").value;
const ordersStore = useOrdersStore();
const { fetchListSellOrder, fetchDataEmployees } = useOrder();
const loading = ref(false);
const options = reactive({
  currentPage: 1,
  maxResult: 10,
  status_ignore: [1],
});
const isLoading = ref(false);
const hasMoreData = ref(true);

// Debounce function to prevent multiple rapid API calls
function debounce(fn, delay) {
  let timeoutID;
  return function (...args) {
    if (timeoutID) {
      clearTimeout(timeoutID);
    }
    timeoutID = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}
onMounted(async () => {
  loading.value = true;
  await ordersStore.getListDataOrder(options);
  options.currentPage = 2;
  loading.value = false;
});

const loadData = async () => {
  if (isLoading.value || !hasMoreData.value) return;
  isLoading.value = true;
  try {
    const response = await fetchListSellOrder(options);
    const data = response.data?.data || [];

    if (data.length) {
      data.forEach((item) => {
        ordersStore.addOrder(item);
      });
      options.currentPage++; // Increment page only if data is returned
    } else {
      hasMoreData.value = false; // No more data to load
    }
  } catch (error) {
    console.error("Error loading data", error);
  } finally {
    isLoading.value = false;
  }
};

useInfiniteScroll(scrollContainer, debounce(loadData, 100), {
  distance: 10,
});
const router = useRouter();

router.beforeEach(async (to, from, next) => {
  if (to.path === `/buy`) {
    loading.value = true;
    options.currentPage = 1;
    await ordersStore.getListDataOrder(options);
    options.currentPage = 2;
    loading.value = false;
  }
  next();
});
const handleSearchDate = async (dateTo, dateFrom) => {
  loading.value = true;
  options.currentPage = 1;
  options.date_create_to = dateTo;
  options.date_create_from = dateFrom;
  options.currentPage = 1;
  await ordersStore.getListDataOrder(options);
  options.currentPage = 2;
  loading.value = false;
};
const handleChangeKeyword = async (keyword) => {
  loading.value = true;
  options.keyword = keyword;
  options.currentPage = 1;
  await ordersStore.getListDataOrder(options);
  options.currentPage = 2;
  loading.value = false;
};
const handleClearKeyword = async (keyword) => {
  loading.value = true;
  options.keyword = keyword;
  options.currentPage = 1;
  await ordersStore.getListDataOrder(options);
  options.currentPage = 2;
  loading.value = false;
};
</script>

<style scoped></style>
