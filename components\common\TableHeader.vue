<template>
  <thead class="bg-gray-50">
    <tr>
      <th
        v-for="(header, index) in headers"
        :key="header"
        v-show="visibleColumns[index]"
        scope="col"
        class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider sticky top-0 z-10 bg-gray-50 border-b border-gray-200"
      >
        {{ header }}
      </th>
      <th
        v-if="isCheckBox"
        scope="col"
        class="text-left whitespace-nowrap font-semibold sticky top-0 z-10 border-b"
      >
        <input
          type="checkbox"
          name=""
          id=""
          class="accent-primary w-4 h-4 cursor-pointer ml-[6px]"
          v-model="customerStore.isCheckGlobal"
        />
      </th>

      <th
        v-if="isOption"
        scope="col"
        class="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider sticky top-0 z-10 bg-gray-50 border-b border-gray-200"
      >
        <div class="flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
            />
          </svg>
        </div>
      </th>
    </tr>
  </thead>
</template>

<script setup>
defineProps({
  headers: {
    type: Array,
    required: true,
  },
  visibleColumns: {
    type: Array,
    required: true,
  },
  isCheckBox: {
    type: Boolean,
  },
  isOption: {
    type: Boolean,
  },
});
const customerStore = useCustomerStore();
</script>
<style scoped></style>
