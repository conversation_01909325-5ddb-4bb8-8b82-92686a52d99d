<template>
  <div class="z-1000">
    <div v-if="previewImages.length" class="mt-2 grid grid-cols-3 gap-2">
      <div
        v-for="(image, index) in previewImages"
        :key="index"
        class="relative border border-primary rounded-md w-[90px]"
      >
        <img
          :src="image"
          alt="Image Preview"
          class="w-[80px] h-[80px] object-contain border rounded-sm mx-auto"
          loading="lazy"
        />
        <span
          @click="removeImage(index)"
          class="absolute top-0 right-0 text-red-700 cursor-pointer"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </span>
      </div>
    </div>
    <div
      v-if="isOpen"
      class="px-2 bottom-12 flex flex-col items-center justify-center"
    >
      <button
        class="bg-primary px-6 py-2 rounded-md text-white my-4"
        @click="handleTakePhoto"
      >
        Chụp ảnh
      </button>
      <video ref="videoElement" autoplay></video>
    </div>
    <input
      type="file"
      @change="handleImageUpload"
      class="hidden"
      ref="fileInput"
      multiple
    />
    <button
      @click="triggerFileInput"
      class="bg-primary px-6 py-2 rounded-md text-white my-4"
    >
      Tải ảnh lên
    </button>
  </div>
</template>

<script setup>
import { ref } from "vue";

const previewImages = ref([]);
const dataImage = ref([]);
const isOpen = ref(false);
const videoElement = ref(null);

const handleImageUpload = (event) => {
  const files = event.target.files;
  Array.from(files).forEach((file) => {
    dataImage.value.push(file);
  });

  Array.from(files).forEach((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImages.value.push(e.target.result);
    };
    reader.readAsDataURL(file);
  });
};

const removeImage = (index) => {
  previewImages.value.splice(index, 1);
};

const triggerFileInput = () => {
  const fileInput = videoElement.value.querySelector("input[type='file']");
  if (fileInput) {
    fileInput.click();
  }
};

const handleOpenCamera = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    openCamera();
  } else {
    closeCamera();
  }
};

const openCamera = () => {
  navigator.mediaDevices
    .getUserMedia({ video: { facingMode: "environment" } })
    .then((stream) => {
      videoElement.value.srcObject = stream;
    })
    .catch((err) => {
      console.error("Error accessing the camera:", err);
    });
};

const closeCamera = () => {
  const stream = videoElement?.value?.srcObject;
  if (stream) {
    const tracks = stream.getTracks();
    tracks.forEach((track) => track.stop());
  }
};

const handleTakePhoto = () => {
  if (!videoElement.value) return;
  const canvas = document.createElement("canvas");
  canvas.width = videoElement.value.videoWidth;
  canvas.height = videoElement.value.videoHeight;

  const ctx = canvas.getContext("2d");
  ctx.drawImage(videoElement.value, 0, 0, canvas.width, canvas.height);
  canvas.toBlob((blob) => {
    dataImage.value.push(blob);
    const res = URL.createObjectURL(blob);
    previewImages.value.push(res);
  }, "image/png");
  handleOpenCamera();
};
</script>

<style scoped>
video {
  width: 100%;
  height: auto;
}
</style>
