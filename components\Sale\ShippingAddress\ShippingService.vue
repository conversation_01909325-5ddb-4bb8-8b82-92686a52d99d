<template>
  <div class="items-center text-sm">
    <div v-if="!isPageFFM" class="font-semibold mb-2 text-primary">Vận chuyển</div>

    <div v-if="!isPageFFM" class="flex flex-col space-y-2">
      <!-- ĐVVC -->
      <div class="flex items-center">
        <label class="w-40">ĐVVC</label>
        <select
          v-model="selectedCarrierId"
          class="w-full outline-none border px-2 py-1 rounded bg-secondary"
          :disabled="
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
            'INVOICE_PUBLISHED'
          "
        >
          <option value="" disabled>Chọn đơn vị vận chuyển</option>
          <option
            v-for="shippingCarrier in dataListShippingCarrier"
            :key="shippingCarrier.id"
            :value="shippingCarrier.id"
          >
            {{ shippingCarrier.name }}
          </option>
        </select>
      </div>

      <!-- HTVC -->
      <div class="flex items-center">
        <label class="w-40">HTVC</label>
        <select
          v-model="selectedServiceId"
          class="w-full outline-none border px-2 py-1 rounded bg-secondary cursor-pointer"
          :disabled="
            orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
            'INVOICE_PUBLISHED'
          "
        >
          <option value="" disabled>Chọn hình thức vận chuyển</option>
          <option
            v-for="service in dataListShippingService"
            :key="service.id"
            :value="service.id"
          >
            {{ service.name }}
          </option>
        </select>
      </div>
    </div>
    <!-- Phí vận chuyển -->
    <div v-if="!isPageFFM" class="flex items-center mt-2">
      <div class="my-1 w-40">Phí vận chuyển</div>
      <input
        type="number"
        inputmode="numeric"
        class="w-full border bg-secondary text-base px-2 py-1 rounded font-semibold outline-none"
        v-model="shippingFee"
        :disabled="
          orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
          'INVOICE_PUBLISHED'
        "
        @blur="handleUpdateShippingFee"
      />
    </div>
    <!-- pageFFM -->
    <div v-if="isPageFFM" class="flex items-center justify-center gap-2">
      <div class="w-1/3">
        <label class="w-40">Đơn vị vận chuyển</label>
        <select
          v-model="selectedCarrierId"
          class="w-full outline-none border px-2 py-[6px] rounded bg-secondary cursor-pointer"
        >
          <option value="" disabled>Chọn đơn vị vận chuyển</option>
          <option
            v-for="shippingCarrier in dataListShippingCarrier"
            :key="shippingCarrier.id"
            :value="shippingCarrier.id"
          >
            {{ shippingCarrier.name }}
          </option>
        </select>
      </div>

      <!-- HTVC -->
      <div class="w-1/3">
        <label class="w-40">Hình thức vận chuyển</label>
        <select
          v-model="selectedServiceId"
          class="w-full outline-none border px-2 py-[6px] rounded bg-secondary cursor-pointer"
        >
          <option value="" disabled>Chọn hình thức vận chuyển</option>
          <option
            v-for="service in dataListShippingService"
            :key="service.id"
            :value="service.id"
          >
            {{ service.name }}
          </option>
        </select>
      </div>
      <div class="w-1/3">
        <div class="w-40">Phí vận chuyển</div>
        <input
          type="number"
          inputmode="numeric"
          class="w-full border bg-secondary text-base px-2 py-1 rounded font-semibold outline-none"
          v-model="shippingFee"
          @blur="handleUpdateShippingFee"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const orderStore = useOrderStore();
const props = defineProps(["orderDetail", "isPageFFM"]);
const selectedCarrierId = ref<string>(
  props.orderDetail?.order?.customAttribute?.carrierId || ""
);

const selectedServiceId = ref<string>(
  props.orderDetail?.order?.customAttribute?.shippingServiceId || ""
);

const shippingFee = ref(
  props.orderDetail?.order?.totalShippingPrice?.amount || 0
);
const {
  getListShippingCarrier,
  getShippingService,
  updateShippingService,
  updateShippingFee,
  updateShippingOrder,
} = useOrder();
const dataListShippingCarrier = ref<any>([]);
const dataListShippingService = ref<any>([]);
const handleGetShippingCarrier = async () => {
  try {
    const response = await getListShippingCarrier();
    dataListShippingCarrier.value = response?.data;
    return response;
  } catch (error) {
    throw error;
  }
};
const handleGetShippingService = async (shippingCarrier: string) => {
  try {
    const response = await getShippingService(shippingCarrier);
    dataListShippingService.value = response?.data;
    return response;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  await handleGetShippingCarrier();
  if (selectedCarrierId.value) {
    await handleGetShippingService(selectedCarrierId.value);
  }
});
const isProgrammaticUpdate = ref(false);

watch(selectedCarrierId, async (newVal: string) => {
  if (isProgrammaticUpdate.value) return; // không chạy nếu đang cập nhật từ code

  if (newVal) {
    await handleGetShippingService(newVal);
    orderStore.idShippingCarrier = newVal;
    orderStore.idShippingService = "";
    selectedServiceId.value = "";
    await updateShippingOrder(props.orderDetail?.id, newVal);
    await orderStore.updateOrder(props.orderDetail?.id);
  }
});
watch(selectedServiceId, async (newVal: string) => {
  if (isProgrammaticUpdate.value) return; // không chạy nếu đang cập nhật từ code

  if (newVal) {
    orderStore.idShippingService = newVal;
    await updateShippingService(props.orderDetail?.id, newVal, "");
    await orderStore.updateOrder(props.orderDetail?.id);
  }
});
// idShippingCarrier,
// idShippingService,
// Cập nhật lại giá trị khi props thay đổi (do dùng ref -> chỉ cập nhật khi mounted)
watch(
  () => props.orderDetail,
  async (newVal) => {
    isProgrammaticUpdate.value = true;
    //
    orderStore.idShippingCarrier =
      newVal?.order?.customAttribute?.carrierId || "";
    orderStore.idShippingService =
      newVal?.order?.customAttribute?.shippingServiceId || "";
    //
    selectedCarrierId.value = newVal?.order?.customAttribute?.carrierId || "";
    selectedServiceId.value =
      newVal?.order?.customAttribute?.shippingServiceId || "";
    shippingFee.value = newVal?.order?.totalShippingPrice?.amount || 0;

    if (selectedCarrierId.value) {
      await handleGetShippingService(selectedCarrierId.value);
    }
    isProgrammaticUpdate.value = false;
  },
  { deep: true }
);

const handleUpdateShippingFee = async () => {
  if (
    props.orderDetail?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    useNuxtApp().$toast.warning(
      "Đơn đã xuất hóa đơn, không thể thực hiện hành động "
    );
    return;
  }
  await updateShippingFee(props.orderDetail?.id, shippingFee.value);
  await orderStore.updateOrder(props.orderDetail?.id);
};
</script>
