<template>
  <div class="p-2 bg-white rounded">
    <div class="text-primary font-bold text-md pb-2 p-2"><PERSON><PERSON><PERSON> h<PERSON>nh sản phẩm</div>
    <div class="h-[23vh] overflow-y-auto mb-3">
      <div
        v-for="product in order?.activeOrderItemProfiles"
        class="mt-2 border py-4 p-2 rounded shadow-md"
      >
        <ItemProductInvoice
          :product="product"
          :units="units"
          @updateInvoice="handleUpdateInvoice"
        ></ItemProductInvoice>
      </div>
    </div>
    <div class="flex items-center justify-end">
      <button
        v-if="
          order?.order.customAttribute.exportVatInvoiceStatus !==
          'INVOICE_PUBLISHED'
        "
        @click="handleExportInvoiceDraft"
        class="px-2 py-1 bg-white text-primary border border-primary rounded mt-4"
      >
        Cập nhật thông tin
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps(["order"]);
const emit = defineEmits(["createInvoiceDraft"]);
const { getUnitInvoice } = useProduct();
const units = ref();
onMounted(async () => {
  units.value = await getUnitInvoice();
});
const handleUpdateInvoice = async (data: any) => {
  console.log("data sau khi emit", data);
  console.log("goi api update");
  handleExportInvoiceDraft();
};
const handleExportInvoiceDraft = async () => {
  console.log("xuất hóa đơn");
  emit("createInvoiceDraft");
};
</script>
