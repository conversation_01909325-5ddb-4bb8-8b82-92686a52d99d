import { createClient, MatrixClient, Room } from "matrix-js-sdk";
import axios from "axios";
interface Message {
  sender: string | undefined;
  content: string | undefined;
  contentUrl?: string;
  type: string;
  timestamp: number;
  eventId: string | undefined;
  hasThread: boolean;
  threadMessages: ThreadMessage[];
}

// Định nghĩa kiểu cho tin nhắn trong luồng
interface ThreadMessage {
  sender: string | undefined;
  content: string | undefined;
  timestamp: number;
  eventId: string | undefined;
}
interface MatrixPlugin {
  connectMatrix: (token: string) => Promise<void>;
  getClient: () => MatrixClient | null;
  isTokenValid: (token: string) => Promise<boolean>;
  loadMessages: (roomId: string, limit?: number) => Promise<any[]>;
  loadThreadMessages: (
    roomId: string,
    threadEventId: string,
    limit?: number
  ) => Promise<any[]>;
  sendMessage: (roomId: string, message: string) => Promise<void>;
  sendThreadMessage: (
    roomId: string,
    threadEventId: string,
    message: string
  ) => Promise<void>;
  loadCredentials: () => { token: string | null; userId: string | null };
}

export default defineNuxtPlugin(() => {
  const client: Ref<MatrixClient | null> = ref(null);
  const config = useRuntimeConfig();

  const getUserIdFromToken = async (token: string): Promise<string> => {
    try {
      const response = await axios.get(
        `${config.public.MATRIX_LINK}/_matrix/client/v3/account/whoami`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data.user_id;
    } catch (error: any) {
      console.error("Error fetching userId:", error);
      if (error.response && error.response.status === 401) {
        console.warn("Unauthorized! Clearing local storage...");
        localStorage.removeItem("matrixToken");
        localStorage.removeItem("matrixUserId");
      }
      throw new Error("Failed to fetch userId from token");
    }
  };

  const saveCredentials = (token: string, userId: string) => {
    localStorage.setItem("matrixToken", token);
    localStorage.setItem("matrixUserId", userId);
  };

  const loadCredentials = () => {
    const token = localStorage.getItem("matrixToken");
    const userId = localStorage.getItem("matrixUserId");
    return { token, userId };
  };

  const connectMatrix = async (token: string): Promise<void> => {
    try {
      const userId = await getUserIdFromToken(token);
      client.value = createClient({
        baseUrl: `${config.public.MATRIX_LINK}`,
        accessToken: token,
        userId: userId,
      });

      saveCredentials(token, userId);

      await client.value.startClient();
    } catch (error) {
      console.error("Failed to connect to Matrix server:", error);
      throw new Error("Matrix connection failed");
    }
  };

  const isTokenValid = async (token: string): Promise<boolean> => {
    try {
      await axios.get(
        `${config.public.MATRIX_LINK}/_matrix/client/v3/account/whoami`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return true;
    } catch {
      return false;
    }
  };
  const loginWithAccessToken = async (token: string): Promise<void> => {
    try {
      // Gửi yêu cầu đăng nhập
      const response = await axios.post(
        `${config.public.MATRIX_LINK}/_matrix/client/v3/login`,
        {
          type: "m.login.token",
          token: token,
        }
      );

      // Lấy thông tin từ phản hồi
      const accessToken = response.data.access_token;
      const userId = response.data.user_id;

      // Lưu access token và user ID
      saveCredentials(accessToken, userId);

      // Kết nối Matrix client
      await connectMatrix(accessToken);
      console.log("Logged in successfully with access token");
    } catch (error) {
      console.error("Failed to log in with access token:", error);
      throw new Error("Login with access token failed");
    }
  };

  const getClient = (): MatrixClient | null => {
    if (!client.value) {
      console.error("Matrix client is not initialized");
    }
    return client.value;
  };

  // Send a message to a room
  const sendMessage = async (
    roomId: string,
    message: string
  ): Promise<void> => {
    if (!client.value) {
      throw new Error("Matrix client is not initialized");
    }

    try {
      await client.value.sendTextMessage(roomId, message);
    } catch (error: any) {
      console.error("Failed to send message:", error);
      if (error.response && error.response.status === 401) {
        console.warn("Unauthorized! Clearing local storage...");
        localStorage.removeItem("matrixToken");
        localStorage.removeItem("matrixUserId");
      }
      throw new Error("Failed to send message");
    }
  };

  // Lấy tin nhắn từ phòng
  const loadMessages = async (roomId: string, limit = 20) => {
    if (!client.value) throw new Error("Matrix client chưa được khởi tạo");

    const room = client.value.getRoom(roomId);
    if (!room) throw new Error("Không tìm thấy phòng");

    //lay thread
    // tra theem thread chinh

    const messages = room.timeline.slice(-limit).map((event) => ({
      sender: event.getSender(),
      content: event.getContent().body,
      timestamp: event.getTs(),
      eventId: event.getId(),
      hasThread: event.getContent().format === "org.matrix.custom.html",
      threadMessages: [],
      type: event.getContent().msgtype,
    }));

    return messages;
  };

  // Send a message in a thread
  const sendThreadMessage = async (
    roomId: string,
    threadEventId: string,
    message: string | object
  ): Promise<void> => {
    if (!client.value) {
      throw new Error("Matrix client is not initialized");
    }

    try {
      // Kiểm tra loại tin nhắn (chuỗi hoặc object)
      if (typeof message === "string") {
        // Gửi tin nhắn văn bản
        await client.value.sendEvent(roomId, "m.room.message", {
          "m.relates_to": {
            rel_type: "m.thread",
            event_id: threadEventId,
          },
          body: message,
          msgtype: "m.text",
        });
      } else if (typeof message === "object") {
        // Gửi tin nhắn phức tạp (ví dụ: file media hoặc JSON)
        const msgtype = message["msgtype"] || "m.text";
        const body = message["body"] || "";
        await client.value.sendEvent(roomId, "m.room.message", {
          "m.relates_to": {
            rel_type: "m.thread",
            event_id: threadEventId,
          },
          ...message,
          body,
          msgtype,
        });
      }
    } catch (error) {
      console.error("Failed to send thread message:", error);
      throw new Error("Failed to send thread message");
    }
  };

  const joinRoom = async (roomId: string): Promise<void> => {
    if (!client.value) {
      throw new Error("Matrix client is not initialized");
      localStorage.removeItem("matrixToken");
      localStorage.removeItem("matrixUserId");
    }

    try {
      // Lấy thông tin phòng
      const room = client.value.getRoom(roomId);

      // Kiểm tra nếu đã tham gia phòng
      if (room && room.getMyMembership() === "join") {
        console.log("User is already in the room");
        return;
      }

      // Thực hiện yêu cầu tham gia phòng nếu chưa tham gia
      await client.value.joinRoom(roomId);
      console.log(`Successfully joined room: ${roomId}`);
    } catch (error) {
      console.error("Failed to join room:", error);
      throw new Error("Failed to join room");
    }
  };

  const loadThreadMessages = async (
    roomId: string,
    threadEventId: string,
    limit = 50
  ) => {
    if (!client.value) {
      throw new Error("Matrix client is not initialized");
      localStorage.removeItem("matrixToken");
      localStorage.removeItem("matrixUserId");
    }

    const room = await client.value.getRoom(roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Lấy các tin nhắn liên quan đến thread từ API Matrix sử dụng phương thức relations
    const response = await client.value.relations(
      roomId,
      threadEventId,
      "m.thread",
      "m.room.message",
      {
        limit: limit,
      }
    );

    const messages =await response.events
      .map((event) => {
        const sender = event.getSender();
        const member = room.getMember(sender); // Lấy thông tin thành viên từ room
        const senderName = member ? member.name : sender; // Nếu không có name thì dùng ID

        return {
          sender: sender,
          senderName: senderName, // Thêm tên hiển thị của người gửi
          content: event.getContent().body || "",
          timestamp: event.getTs(),
          eventId: event.getId(),
          type: event.getContent().msgtype,
          contentUrl: event.getContent().url
            ? mxcUrlToHttp(event.getContent().url)
            : null,
          isCurrentUser: sender === client.value?.getUserId(),
        };
      })
      .reverse(); // Đảo ngược thứ tự tin nhắn để hiển thị tin mới nhất ở cuối

    const rootEvent = await client.value.fetchRoomEvent(roomId, threadEventId);

    // Tạo dữ liệu phòng và trả về
    const roomData = {
      name: room.name,
      messages: messages, // Tin nhắn đã sắp xếp, tin mới nhất sẽ ở cuối
      topic: rootEvent.content,
    };

    return roomData;
  };

  const mxcUrlToHttp = (url: any) => {
    const baseUrl = client.value?.getHomeserverUrl();
    return url.replace("mxc://", `${baseUrl}/_matrix/media/r0/download/`);
  };
  // kiểm tra user đã join room hay chưa
  const checkUserExisted = async (
    roomId: string,
    userId: string
  ): Promise<boolean> => {
    if (!client.value) {
      throw new Error("Matrix client is not initialized");
      localStorage.removeItem("matrixToken");
      localStorage.removeItem("matrixUserId");
    }

    try {
      // Lấy đối tượng phòng
      const room = await client.value.getRoom(roomId);

      // Nếu không tìm thấy phòng, nghĩa là người dùng hoặc bot chưa tham gia phòng
      if (!room) {
        return false;
      }

      // Kiểm tra trạng thái thành viên của người dùng
      const isMember = await room.hasMembershipState(userId, "join");

      return isMember;
    } catch (error) {
      throw new Error("Failed to check user membership");
    }
  };

  return {
    provide: {
      matrixClient: {
        connectMatrix,
        getClient,
        isTokenValid,
        sendMessage,
        sendThreadMessage,
        loadCredentials,
        loadMessages,
        loadThreadMessages,
        joinRoom,
        loginWithAccessToken,
        checkUserExisted,
      },
    },
  };
});
