<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-3 max-w-lg w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">Tạo đơn đổi trả thành công</div>
        <div>
          <!-- <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg> -->
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[350px] overflow-y-auto mb-2">
        <div class="flex items-center gap-1">
          <div class="font-semibold">Mã đơn:</div>
          <div>{{ orderDetails?.id }}</div>
        </div>
        <div class="flex items-center gap-1">
          <div class="font-semibold">Trạng thái đơn:</div>
          <div>{{ orderDetails?.statusDescription }}</div>
        </div>
        <div class="flex items-center gap-1">
          <div class="font-semibold">Ngày đặt hàng:</div>
          <div>{{ formatTimestampV3(orderDetails?.order?.createdStamp) }}</div>
        </div>
        <div class="flex items-center gap-1">
          <div class="font-semibold">Khách hàng:</div>
          <div class="flex items-center gap-1">
            <div class="flex items-center gap-1">
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                  />
                </svg>
              </div>
              <div>
                {{ orderDetails?.order?.ownerName }}
              </div>
            </div>
            <div class="flex items-center gap-1">
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                  />
                </svg>
              </div>
              <div>
                {{ orderDetails?.order?.ownerPhone }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between gap-4">
        <button
          @click="continueCreateOrder"
          class="bg-primary text-white px-2 py-1 rounded w-full"
        >
          Xem danh sách đơn
        </button>
        <button
          @click="navigateOrderReturn"
          class="bg-primary text-white px-2 py-1 rounded w-full"
        >
          Chi tiết đơn trả
        </button>
        <button
          v-if="isSwapOrder"
          @click="navigateOrderSwap"
          class="bg-primary text-white px-2 py-1 rounded w-full"
        >
          Chi tiết đơn đổi
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const props = defineProps(["dataReturnOrder", "isSwapOrder"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const route = useRoute();
const navigateOrderReturn = () => {
  isVisible.value = false;

  navigateTo(
    `/order/return/detail?orderId=${orderDetails.value?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
  emit("confirm", false);
};
const navigateOrderSwap = () => {
  isVisible.value = false;
  navigateTo(
    `/sale?orderId=${route.query?.orderId}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );

  emit("confirm", false);
};
const continueCreateOrder = () => {
  isVisible.value = false;
  navigateTo(
    `/order?orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
  emit("confirm", false);
};
const { getOrderById } = useOrderStore();
const orderDetails = ref();
const { fetchOrderDetails, updateStatusApproved, getInfoSellOrderReturn } =
  useOrder();

const fetchOrderData = async (orderId: string) => {
  try {
    const res = await getInfoSellOrderReturn(orderId);
    orderDetails.value = res?.data;
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  fetchOrderData(props.dataReturnOrder);
});
</script>

<style scoped></style>
