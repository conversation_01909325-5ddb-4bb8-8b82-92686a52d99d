import { defineStore } from "pinia";
import { ref } from "vue";
import useProduct from "@/composables/useProduct";
export const useProductStore = defineStore("product", () => {
  const { getProducts, getImageProduct } = useProduct();
  const featuredProducts = ref<any[]>([]);
  const isLoading = ref(false);

  const getFeaturedProducts = async () => {
    isLoading.value = true;
    try {
      const products = await getProducts();
      featuredProducts.value = products;
    } catch (error) {
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  };

  const getProductImage = async (productId: string) => {
    try {
      const response = await getImageProduct(productId);
      return response;
    } catch (error) {
      console.error("Error getting image products:", error);
    }
  };
  return {
    featuredProducts,
    isLoading,
    getFeaturedProducts,
    getProductImage,
  };
});
