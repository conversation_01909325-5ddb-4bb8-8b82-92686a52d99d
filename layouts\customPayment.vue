<template>
  <div class="flex h-screen">
    <div class="flex flex-col flex-1 overflow-y-auto">
      <slot></slot>
    </div>

    <!-- Session Expired Modal -->
    <SessionExpiredModal
      :is-visible="isSessionExpiredModalVisible"
      :countdown-seconds="sessionExpiredCountdown"
      :allow-stay-here="false"
      @login-now="handleLoginNow"
      @time-expired="handleTimeExpired"
    />
  </div>
</template>

<script setup>
// Components
const SessionExpiredModal = defineAsyncComponent(() =>
  import("~/components/ui/feedback/SessionExpiredModal.vue")
);

// Session expired modal management
const {
  isSessionExpiredModalVisible,
  sessionExpiredCountdown,
  handleLoginNow,
  handleTimeExpired,
} = useSessionExpired();
</script>
