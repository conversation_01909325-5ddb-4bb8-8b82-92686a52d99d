<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full animate-popup">
      <!-- <h2 class="text-lg font-bold mb-4">{{ title }}</h2> -->
      <div>
        <span>Mã giao dịch đã hết hạn vui lòng tạo lại mã giao dịch mới</span>
      </div>
      <p class="mb-6">{{ message }}</p>
      <div class="flex justify-end space-x-4">
        <!-- <button @click="cancel" class="px-4 py-2 bg-gray-300 rounded">
          Đóng
        </button> -->
        <button
          @click="confirm"
          class="px-2 py-1 bg-blue-500 text-white rounded"
        >
          Đồng ý
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps(["title", "message", "data"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);

const confirm = () => {
  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
</script>

<style scoped></style>
