<template>
  <div class="h-screen flex flex-col overflow-y-auto">
    <!-- Thanh tiêu đề -->
    <!-- <div class="p-2 z-10">
      <TabReturnOrder></TabReturnOrder>
    </div> -->

    <!-- Nội dung chính -->
    <div class="flex-1 pb-16 lg:pb-0 mt-2">
      <!-- Chừa khoảng trống cho footer trên mobile -->
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-2 mx-2">
        <!-- Bảng sản phẩm -->
        <div class="bg-white px-2 order-1 lg:order-1 lg:col-span-8 h-screen-50">
          <TableReturnOrder></TableReturnOrder>
          <SearchProductReturnOrder />
          <TableProduct :isPageSale="true" />
        </div>

        <!-- Thông tin thanh toán -->
        <div class="bg-white order-2 lg:order-2 lg:col-span-4">
          <div class="p-2 md:h-screen-110 overflow-y-auto pb-[60px] md:pb-0">
            <CustomerOrderReturnDetail
              :orderDetail="returnStore.orderReturn"
            ></CustomerOrderReturnDetail>
            <InfoPaymentReturn
              :orderReturn="returnStore.orderReturn"
              :orderChooseReturn="returnStore.orderChooseReturn"
              :totalPriceProductReturn="returnStore.totalPriceProductReturn"
              :totalProductReturn="returnStore.totalProductReturn"
            />
            <div class="border-t my-2"></div>
            <InfoPaymentSwap />
            <InfoRelateReturnOrder />
          </div>
          <div class="md:block hidden">
            <FooterButtonReturnOrder />
          </div>
        </div>
      </div>
    </div>

    <!-- Footer cố định ở dưới màn hình trên mobile -->
    <div class="block md:hidden">
      <div
        class="fixed bottom-0 left-0 w-full lg:static bg-white shadow-md p-2"
      >
        <FooterButtonReturnOrder />
      </div>
    </div>

    <!-- Loading Spinner -->
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </div>
</template>

<script setup>
import { returnOrderStore } from "~/stores/return";

useHead({
  title: "Đổi trả hàng",
  meta: [
    {
      name: "description",
      content: "Đổi trả hàng",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Đổi trả hàng",
});
const route = useRoute();
const returnStore = returnOrderStore();
const orderStore = useOrderStore();
const isLoading = ref(false);
const handleGetDataReturnOrder = async (orderId) => {
  try {
    isLoading.value = true;
    await returnStore.getOrderReturn(orderId);
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
onMounted(async () => {
  await handleGetDataReturnOrder(route.query?.orderReturnId);
  if (route.query?.orderId) {
    await orderStore?.getOrderById(route.query?.orderId);
  }
});
watch(
  () => route.query.orderReturnId,
  async (newVal, oldVal) => {
    if (newVal) {
      await handleGetDataReturnOrder(newVal);
    }
  }
);
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/order/return`) {
    orderStore.orderDetail = null;
    orderStore.dataListProductDiary = null;
  }
  next();
});
</script>
