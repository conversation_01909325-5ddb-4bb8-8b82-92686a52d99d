import { useNuxtApp } from "#app";
export default function useReport() {
  const $sdk = useNuxtApp().$sdk;
  const token = useCookie("token").value;
  if (token) {
    $sdk.setToken(token);
  } else {
    throw new Error("Token is not defined");
  }
  const reportBySaleEmployees = async (
    employee_assign: string,
    date_from: number,
    date_to: number
  ) => {
    try {
      const response = await $sdk.order.reportBySaleEmployee(
        employee_assign,
        date_from,
        date_to
      );
      return response;
    } catch (error) {
      console.error("Error update status:", error);
    }
  };
  const reportByDetails = async (
    employee_assign: string,
    type_view: string,
    date_from: number,
    date_to: number
  ) => {
    try {
      const response = await $sdk.order.reportByDetail(
        employee_assign,
        type_view,
        date_from,
        date_to
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const reportDateMonthYears = async (
    employee_assign: string,
    type_view: string,
    date_from: number,
    date_to: number
  ) => {
    try {
      const response = await $sdk.order.reportDateMonthYear(
        employee_assign,
        type_view,
        date_from,
        date_to
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const reportByPaymentMethods = async (
    employee_assign: string,
    date_from: number,
    date_to: number
  ) => {
    try {
      const response = await $sdk.order.reportByPaymentMethod(
        employee_assign,
        date_from,
        date_to
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const reportByStores = async (date_from: number, date_to: number) => {
    try {
      const response = await $sdk.order.reporByStores(date_from, date_to);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    reportBySaleEmployees,
    reportByDetails,
    reportDateMonthYears,
    reportByPaymentMethods,
    reportByStores,
  };
}
