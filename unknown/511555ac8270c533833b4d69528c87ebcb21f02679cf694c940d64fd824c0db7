/**
 * FFM (Fulfillment) Helper Utilities
 * Centralized functions for handling FFM stage and progress operations
 */

// Type definitions
export interface FFMStageData {
  fulfillmentStatus?: string;
  takenStatus?: string;
  packageStatus?: string;
  exportStatus?: string;
}

export interface FFMStageResult {
  styleCSS: string;
  indexCSS: number;
}

/**
 * Calculate FFM stage progress based on status data
 * @param ffmData - FFM stage data object
 * @returns Object containing CSS width class and stage index
 */
export const calculateFFMStage = (ffmData: FFMStageData): FFMStageResult => {
  if (!ffmData) {
    return { styleCSS: "", indexCSS: 0 };
  }

  // Check fulfillment status first (highest priority)
  if (ffmData.fulfillmentStatus === "FULFILLED") {
    return { styleCSS: "w-full", indexCSS: 4 };
  }
  
  // Check taken status
  if (ffmData.takenStatus === "TAKEN") {
    return { styleCSS: "w-3/4", indexCSS: 3 };
  }
  
  // Check package status
  if (ffmData.packageStatus === "PACKAGED") {
    return { styleCSS: "w-2/4", indexCSS: 2 };
  }
  
  // Check export status
  if (ffmData.exportStatus === "EXPORTED") {
    return { styleCSS: "w-1/4", indexCSS: 1 };
  }
  
  // Default state
  return { styleCSS: "", indexCSS: 0 };
};

/**
 * Get FFM stage description based on index
 * @param stageIndex - Stage index (0-4)
 * @returns Vietnamese description of the stage
 */
export const getFFMStageDescription = (stageIndex: number): string => {
  const stageDescriptions: Record<number, string> = {
    0: "Chưa bắt đầu",
    1: "Đã xuất kho",
    2: "Đã đóng gói",
    3: "Đã lấy hàng",
    4: "Hoàn thành",
  };
  
  return stageDescriptions[stageIndex] || "Không xác định";
};

/**
 * Get FFM stage progress percentage
 * @param stageIndex - Stage index (0-4)
 * @returns Progress percentage (0-100)
 */
export const getFFMStageProgress = (stageIndex: number): number => {
  const progressMap: Record<number, number> = {
    0: 0,
    1: 25,
    2: 50,
    3: 75,
    4: 100,
  };
  
  return progressMap[stageIndex] || 0;
};

/**
 * Check if FFM stage is completed
 * @param stageIndex - Stage index to check
 * @param currentStageIndex - Current stage index
 * @returns Boolean indicating if stage is completed
 */
export const isFFMStageCompleted = (stageIndex: number, currentStageIndex: number): boolean => {
  return stageIndex <= currentStageIndex;
};

/**
 * Get FFM stage icon based on stage index
 * @param stageIndex - Stage index (0-4)
 * @returns Icon name or class
 */
export const getFFMStageIcon = (stageIndex: number): string => {
  const stageIcons: Record<number, string> = {
    0: "clock",
    1: "box",
    2: "package",
    3: "truck",
    4: "check-circle",
  };
  
  return stageIcons[stageIndex] || "question-mark";
};

/**
 * Get FFM stage color based on completion status
 * @param stageIndex - Stage index
 * @param currentStageIndex - Current stage index
 * @returns Tailwind color class
 */
export const getFFMStageColor = (stageIndex: number, currentStageIndex: number): string => {
  if (stageIndex < currentStageIndex) {
    return "text-green-500"; // Completed
  } else if (stageIndex === currentStageIndex) {
    return "text-blue-500"; // Current
  } else {
    return "text-gray-400"; // Pending
  }
};

/**
 * Get all FFM stages with their status
 * @param currentStageIndex - Current stage index
 * @returns Array of stage objects with status information
 */
export const getAllFFMStages = (currentStageIndex: number) => {
  const stages = [
    { index: 0, name: "Tạo đơn", description: "Đơn hàng được tạo" },
    { index: 1, name: "Xuất kho", description: "Hàng đã được xuất khỏi kho" },
    { index: 2, name: "Đóng gói", description: "Hàng đã được đóng gói" },
    { index: 3, name: "Lấy hàng", description: "Đơn vị vận chuyển đã lấy hàng" },
    { index: 4, name: "Hoàn thành", description: "Giao hàng thành công" },
  ];

  return stages.map(stage => ({
    ...stage,
    isCompleted: isFFMStageCompleted(stage.index, currentStageIndex),
    isCurrent: stage.index === currentStageIndex,
    color: getFFMStageColor(stage.index, currentStageIndex),
    icon: getFFMStageIcon(stage.index),
  }));
};

/**
 * Validate FFM stage data
 * @param ffmData - FFM stage data to validate
 * @returns Boolean indicating if data is valid
 */
export const validateFFMStageData = (ffmData: any): ffmData is FFMStageData => {
  return (
    ffmData &&
    typeof ffmData === 'object' &&
    (ffmData.fulfillmentStatus !== undefined ||
     ffmData.takenStatus !== undefined ||
     ffmData.packageStatus !== undefined ||
     ffmData.exportStatus !== undefined)
  );
};

/**
 * Get next FFM stage information
 * @param currentStageIndex - Current stage index
 * @returns Next stage information or null if completed
 */
export const getNextFFMStage = (currentStageIndex: number) => {
  if (currentStageIndex >= 4) {
    return null; // Already completed
  }
  
  const nextIndex = currentStageIndex + 1;
  return {
    index: nextIndex,
    description: getFFMStageDescription(nextIndex),
    icon: getFFMStageIcon(nextIndex),
  };
};

/**
 * Check if FFM is in final state
 * @param ffmData - FFM stage data
 * @returns Boolean indicating if FFM is completed
 */
export const isFFMCompleted = (ffmData: FFMStageData): boolean => {
  return ffmData?.fulfillmentStatus === "FULFILLED";
};

/**
 * Check if FFM has started
 * @param ffmData - FFM stage data
 * @returns Boolean indicating if FFM process has started
 */
export const hasFFMStarted = (ffmData: FFMStageData): boolean => {
  return !!(
    ffmData?.exportStatus ||
    ffmData?.packageStatus ||
    ffmData?.takenStatus ||
    ffmData?.fulfillmentStatus
  );
};
