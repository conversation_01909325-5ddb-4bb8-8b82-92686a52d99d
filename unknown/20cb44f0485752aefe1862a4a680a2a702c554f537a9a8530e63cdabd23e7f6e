export default function useWarehouse() {
  const $sdk = useNuxtApp().$sdk;
  const getInventory = async (sku: string, warehouseId: string) => {
    try {
      const response = await $sdk.warehouse.getInventory(sku, warehouseId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInventoryV2 = async (warehouseId: string, data: any) => {
    try {
      const response = await $sdk.warehouseV2.getInventory(warehouseId, data);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInforWarehouse = async (warehouseId: string) => {
    try {
      const response = await $sdk.warehouseV2.getInfoWarehouse(warehouseId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return { getInventory, getInventoryV2, getInforWarehouse };
}
