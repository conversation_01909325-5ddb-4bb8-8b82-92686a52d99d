const { printOrderHTML } = useOrder();

export const PrintOrderHTML = async (
  orderId: string,
  paymentMethod?: string,
  paymentUrl?: string
) => {
  try {
    const response = await printOrderHTML(orderId, paymentMethod, paymentUrl);

    const printFrame = document.createElement("iframe");
    printFrame.style.position = "absolute";
    printFrame.style.top = "-10000px";
    document.body.appendChild(printFrame);

    const doc =
      printFrame.contentDocument || printFrame.contentWindow?.document;
    if (doc) {
      doc.open();
      doc.write(response?.data);
      doc.close();
    }
    printFrame.contentWindow?.focus();
    printFrame.contentWindow?.print();

    document.body.removeChild(printFrame);
  } catch (error) {
    console.error("Error printing the order:", error);
    throw error;
  }
};
