<template>
  <div
    @click="handleClick"
    class="border flex py-1 my-2 rounded items-center gap-1"
  >
    <NuxtImg
      v-if="isAndroid"
      :src="bank?.appLogo"
      alt=""
      class="w-10 h-10 object-contain bg-white m-2"
      loading="lazy"
      preload
    />
    <NuxtImg
      v-else
      :src="bank?.appLogo"
      alt=""
      class="w-16 h-16 object-contain bg-white m-2"
      loading="lazy"
      preload
    />
    <div class="font-semibold">{{ bank?.bankName }}</div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  bank: Object,
  isAndroid: Boolean,
});
const handleClick = () => {
  window.location.href = props.bank?.deeplink;
};
</script>
