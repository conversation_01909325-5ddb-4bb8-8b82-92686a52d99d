import { defineStore } from "pinia";
import { ref } from "vue";

export const useCheckinStore = defineStore("checkin", () => {
  const dataCheckin = ref<any>([]);
  const curentIndex = ref<number>(0);
  const {
    getOpportunityById,
    UpdateWorkEffortName,
    getPersonByPartyId,
    updateWorkEffortProcessStatus,
  } = useCheckin(); // bỏ phần dưới
  const dataDetail = ref<any>();
  const location = ref<string>();
  const idImage = ref<string>();
  const image = ref<any>();
  const setDataCheckin = (newData: any[]) => {
    if (Array.isArray(newData)) {
      dataCheckin.value = newData;
    } else {
      console.error("newData is not an array.");
      dataCheckin.value = [];
    }
  };
  const setCurentIndex = (newVal: number) => {
    curentIndex.value = newVal;
  };
  const addCheckin = (newCheckin: any) => {
    if (!Array.isArray(dataCheckin.value)) {
      dataCheckin.value = [];
    }
    dataCheckin.value = [...dataCheckin.value, newCheckin];
  };
  ///
  const handleLocation = () => {
    const item = dataDetail.value?.subTasks.find(
      (item: any) => item.workEffortTypeId === "TAKE_ADDRESS"
    );
    if (item) {
      location.value = item.description;
    }
  };
  const handleImage = () => {
    const item = dataDetail.value?.subTasks.find(
      (item: any) => item.workEffortTypeId === "TAKE_PHOTO"
    );
    if (item) {
      idImage.value = item.id;
      image.value = item.attachments;
    }
  };
  const getCheckinById = async (id: string) => {
    try {
      const response = await getOpportunityById(id);
      dataDetail.value = response;
      dataDetail.value.pro;
      handleLocation();
      handleImage();
      handleCurrentIndex();
    } catch (error) {
      throw error;
    }
  };
  const handleCurrentIndex = () => {
    if (dataDetail.value?.processPipeline?.length) {
      const index = dataDetail.value.processPipeline.findIndex((item: any) => {
        if (item.id === dataDetail.value?.processStatus) {
          return true; 
        }
        return false;
      });
      curentIndex.value = index;
    } else {
      console.log("Process pipeline is empty or undefined");
    }
  };

  const handleUpdateProcessStatus = async (
    workEffortId: string,
    processStatus: string,
    performerId: string
  ) => {
    try {
      const response = await updateWorkEffortProcessStatus(
        workEffortId,
        processStatus,
        performerId
      );
    } catch (error) {
      throw error;
    }
  };
  return {
    dataCheckin,
    setDataCheckin,
    addCheckin,
    dataDetail,
    getCheckinById,
    image,
    idImage,
    location,
    handleUpdateProcessStatus,
    setCurentIndex,
    curentIndex,
  };
});
