<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full animate-popup"
    >
      <h2 class="font-bold text-lg">Thanh toán 1 phần</h2>
      <div>
        <span>Tổng thanh toán: </span>
        <span class="font-bold">{{
          formatCurrency(totalPrice + orderStore.shippingFee)
        }}</span>
      </div>
      <div class="flex items-center gap-2 my-1">
        <div
          v-for="percent in listPercent"
          :key="percent"
          @click="setPartialAmount(percent)"
          :class="{
            'bg-primary text-white': selectedPercent === percent,
            'border-primary border text-primary': selectedPercent !== percent,
          }"
          class="px-2 rounded cursor-pointer"
        >
          {{ percent }}%
        </div>
      </div>
      <div>
        <label for="paymentAmount" class="block mb-2"
          ><PERSON>hậ<PERSON> số tiền thanh toán:</label
        >
        <input
          id="paymentAmount"
          type="text"
          v-model="formattedPaymentAmount"
          @input="removeTrailingZeros"
          class="border border-gray-300 rounded px-3 py-2 w-full focus:outline-none"
          placeholder="Nhập số tiền"
        />
        <div v-if="errorMessage" class="text-red-500 text-sm mt-1">
          {{ errorMessage }}
        </div>
      </div>
      <div class="flex justify-end space-x-4 mt-4 text-sm">
        <button @click="cancel" class="px-2 py-1 bg-gray-200 rounded">
          Đóng
        </button>
        <button
          @click="confirm"
          class="px-2 py-1 bg-primary text-white rounded text-sm"
          :disabled="!isValidAmount"
        >
          Đồng ý
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["title", "message", "data"]);
const emit = defineEmits(["confirm", "cancel"]);

const isVisible = ref(true);

const orderStore = useOrderStore();
const totalPrice = computed(() => orderStore.orderDetail?.remainTotal || 0);
const listPercent = ref([30, 50, 70, 90]);
const errorMessage = ref("");
const isValidAmount = ref(true);

const formattedPaymentAmount = ref(formatCurrencyV2(orderStore.paymentAmount));

watch(
  () => orderStore.paymentAmount,
  (newValue) => {
    formattedPaymentAmount.value = formatCurrencyV2(newValue);
  }
);

const confirm = () => {
  if (
    orderStore.paymentAmount <= totalPrice.value + orderStore.shippingFee &&
    orderStore.paymentAmount > 0
  ) {
    if (isValidAmount.value) {
      localStorage.setItem("paymentAmount", orderStore.paymentAmount);
      emit("confirm");
      isVisible.value = false;
    }
  } else {
    useNuxtApp().$toast.warning(
      "Vui lòng nhập số tiền thanh toán lớn hơn 0 và nhỏ hơn tổng thanh toán"
    );
  }
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const selectedPercent = ref(null);

const setPartialAmount = (percent) => {
  selectedPercent.value = percent;
  orderStore.paymentAmount =
    ((totalPrice.value + orderStore.shippingFee) * percent) / 100;
};

const removeTrailingZeros = (event) => {
  const cleanedValue = event.target.value.replace(/[^\d]/g, "");

  orderStore.paymentAmount = parseInt(cleanedValue) || 0;

  formattedPaymentAmount.value = formatCurrencyV2(orderStore.paymentAmount);
};
</script>

<style scoped></style>
