<template>
  <!-- Modal Backdrop with Blur Effect -->
  <Transition
    name="modal"
    enter-active-class="transition-all duration-300 ease-out"
    leave-active-class="transition-all duration-200 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[100] p-4 text-sm"
      @click.self="closeModalEditCustomer"
    >
      <!-- Modal Container -->
      <Transition
        name="modal-content"
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-200 ease-in"
        enter-from-class="opacity-0 scale-95 translate-y-4"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-95 translate-y-4"
      >
        <div
          class="bg-white w-full max-w-2xl shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-300 ease-in-out max-h-[90vh] flex flex-col"
        >
          <!-- Header -->
          <div class="bg-primary px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <button
                @click="closeModalEditCustomer"
                class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 group"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 transform group-hover:-translate-x-1 transition-transform duration-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                <span class="text-sm font-medium">Trở lại</span>
              </button>
            </div>

            <h2
              class="text-xl font-bold text-white flex items-center space-x-2"
            >
              <span>Sửa thông tin khách hàng</span>
            </h2>

            <button
              @click="handleUpdateCustomer(formData.id)"
              :disabled="isLoading || !isFormValid"
              type="button"
              class="bg-white text-primary px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
            >
              <svg
                v-if="isLoading"
                class="animate-spin h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <span>{{ isLoading ? "Đang cập nhật..." : "Cập nhật" }}</span>
            </button>
          </div>
          <!-- Form Content -->
          <div class="flex-1 overflow-y-auto">
            <form class="p-6 space-y-6">
              <!-- Personal Information Section -->
              <div class="space-y-4">
                <h3
                  class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <span>Thông tin cá nhân</span>
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Name Field -->
                  <div class="space-y-2">
                    <label
                      class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                    >
                      <span>Họ và tên</span>
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model="formData.name"
                        type="text"
                        placeholder="Nhập họ và tên"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.name,
                        }"
                      />
                      <div
                        v-if="errors.name"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.name }}
                      </div>
                    </div>
                  </div>

                  <!-- Phone Field -->
                  <div class="space-y-2">
                    <label
                      class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                    >
                      <span>Điện thoại</span>
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <input
                        v-model="formData.phone"
                        type="tel"
                        placeholder="Nhập số điện thoại"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.phone,
                        }"
                      />
                      <div
                        v-if="errors.phone"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.phone }}
                      </div>
                    </div>
                  </div>

                  <!-- Birth Date Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Ngày sinh</label
                    >
                    <flatpickr
                      v-model="formData.birthDate"
                      :config="datePickerConfig"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                      placeholder="Chọn ngày sinh"
                    />
                  </div>

                  <!-- Email Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Email</label
                    >
                    <div class="relative">
                      <input
                        v-model="formData.email"
                        type="email"
                        placeholder="Nhập email"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                        :class="{
                          'border-red-500 focus:ring-red-500': errors.email,
                        }"
                      />
                      <div
                        v-if="errors.email"
                        class="absolute -bottom-5 left-0 text-xs text-red-500"
                      >
                        {{ errors.email }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Address Information Section -->
              <div class="space-y-4">
                <h3
                  class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Thông tin địa chỉ</span>
                </h3>

                <div class="grid grid-cols-1 gap-4">
                  <!-- Address Field -->
                  <div class="space-y-2">
                    <label class="text-sm font-semibold text-gray-700"
                      >Địa chỉ</label
                    >
                    <input
                      v-model="formData.address"
                      type="text"
                      placeholder="Nhập địa chỉ"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import flatpickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
import { format } from "date-fns";

// Composables
const { updateCustomer } = useCustomer();
const authStore = useAuthStore();
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { $sdk } = useNuxtApp();

// Props & Emits
const emits = defineEmits(["closeModalEditCustomer", "updateUserData"]);
const props = defineProps({
  customer: {
    type: Object,
    required: true,
  },
  isManagerCustomer: Boolean,
});

// Reactive Data
const isLoading = ref(false);
const formData = ref<any>({
  id: "",
  name: "",
  phone: "",
  birthDate: "",
  address: "",
  email: "",
  gender: "",
});

const errors = ref({
  name: "",
  phone: "",
  email: "",
});

// Date Picker Config
const datePickerConfig = {
  locale: Vietnamese,
  dateFormat: "Y-m-d",
  allowInput: true,
};

// Validation Rules
const validateName = (value: string) => {
  if (!value || value.trim().length === 0) {
    return "Họ và tên là bắt buộc";
  }
  if (value.trim().length < 2) {
    return "Họ và tên phải có ít nhất 2 ký tự";
  }
  return "";
};

const validatePhone = (value: string) => {
  if (!value || value.trim().length === 0) {
    return "Số điện thoại là bắt buộc";
  }
  const phoneRegex = /^[0-9]{10,11}$/;
  if (!phoneRegex.test(value.replace(/\s/g, ""))) {
    return "Số điện thoại không hợp lệ";
  }
  return "";
};

const validateEmail = (value: string) => {
  if (value && value.trim().length > 0) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return "Email không hợp lệ";
    }
  }
  return "";
};

// Form Validation Computed
const isFormValid = computed(() => {
  return (
    formData.value.name &&
    formData.value.name.trim().length >= 2 &&
    formData.value.phone &&
    validatePhone(formData.value.phone) === "" &&
    validateEmail(formData.value.email) === ""
  );
});

// Watchers for real-time validation
watch(
  () => formData.value.name,
  (newValue) => {
    errors.value.name = validateName(newValue);
  }
);

watch(
  () => formData.value.phone,
  (newValue) => {
    errors.value.phone = validatePhone(newValue);
  }
);

watch(
  () => formData.value.email,
  (newValue) => {
    errors.value.email = validateEmail(newValue);
  }
);

// Watch for customer prop changes
watch(
  () => props.customer,
  (newCustomer) => {
    if (newCustomer) {
      formData.value = { ...newCustomer };
      // Reset errors when loading new customer data
      errors.value = {
        name: "",
        phone: "",
        email: "",
      };
    }
  },
  { immediate: true }
);

// Methods
const closeModalEditCustomer = () => {
  emits("closeModalEditCustomer", false);
};

const handleUpdateCustomer = async (id: any) => {
  // Validate form before submission
  errors.value.name = validateName(formData.value.name);
  errors.value.phone = validatePhone(formData.value.phone);
  errors.value.email = validateEmail(formData.value.email);

  if (!isFormValid.value) {
    return;
  }

  isLoading.value = true;

  try {
    const { name, phone, birthDate, address, email } = formData.value;

    // Validate phone number using SDK
    const res = $sdk.order.validatePhoneNumber(phone);
    if (!res) {
      useNuxtApp().$toast?.warning("Số điện thoại không hợp lệ");
      return;
    }

    // Format birth date if provided
    let formattedBirthDate = birthDate;
    if (birthDate) {
      try {
        formattedBirthDate = format(
          new Date(birthDate),
          "yyyy-MM-dd'T'HH:mm:ss"
        );
      } catch (error) {
        console.warn("Invalid birth date format:", birthDate);
      }
    }

    const requestData = {
      name,
      phone,
      birthDate: formattedBirthDate,
      address,
      email,
    };

    const response = await updateCustomer(id, requestData, "");

    if (props.isManagerCustomer) {
      customerStore.handleGetCustomer(props.customer?.id);
    } else {
      orderStore.getCustomerId(props.customer?.id);
    }

    closeModalEditCustomer();
    emits("updateUserData", requestData);

    return response;
  } catch (error) {
    console.error("Error updating customer:", error);
    useNuxtApp().$toast?.error(
      "Có lỗi xảy ra khi cập nhật thông tin khách hàng"
    );
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes for transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(1rem);
}
</style>
