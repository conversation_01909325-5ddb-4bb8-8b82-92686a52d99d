<template>
  <div class="py-1">
    <div class="">
      <div class="flex items-center justify-start mb-2">
        <div @click="handleClickBack" class="cursor-pointer mr-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-3"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </div>
        <div class="text-primary font-semibold text-sm">
          {{
            payment?.methodDescription || payment?.methodCode || paymentMethod
          }}
        </div>
      </div>
    </div>
    <div v-if="!isLoading" class="flex items-center justify-center">
      <img
        :src="payment?.qrCode || payment.qrCodeUrl || payment.payUrl"
        alt="QR Code"
        class="w-56 h-56 md:w-72 md:h-72 object-contain"
        loading="lazy"
      />
      <!-- <img
        v-if="
          (payment.qrCodeUrl || payment.qrCode || payment.payUrl) &&
          paymentMethod !== 'payon'
        "
        :src="
          paymentMethod === 'transfer'
            ? `${payment.qrCodeUrl || payment.payUrl}`
            : `${payment.qrCodeUrl || payment.qrCode}`
        "
        alt="QR Code"
        class="md:w-[40%] md:h-[40%]"
      /> -->
    </div>
    <div v-else>
      <div class="flex items-center justify-center animate-pulse">
        <div class="bg-secondary h-56 w-56 md:h-72 md:w-72 rounded-lg"></div>
      </div>
    </div>
    <div
      v-if="paymentMethod === 'payon'"
      class="flex items-center justify-center text-primary font-bold h-[80px]"
    >
      Đã tạo thanh toán mPos thành công
    </div>
    <!-- Error Message - Centered -->
    <div
      v-if="isExpired"
      class="flex flex-col items-center justify-center space-y-4 py-8"
    >
      <div
        class="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md mx-auto"
      >
        <div class="flex items-center space-x-3">
          <svg
            class="w-5 h-5 text-red-500 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <p class="text-sm text-red-700 font-medium">
            Giao dịch đã hết hạn hoặc bị hủy. Vui lòng tạo lại giao dịch mới.
          </p>
        </div>
      </div>

      <button
        @click="createNewPayment"
        class="flex items-center gap-2 px-4 py-2 text-white bg-primary rounded-lg font-medium hover:bg-primary/90 transition-colors"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Tạo mã thanh toán mới
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps([
  "payment",
  "paymentMethod",
  "isExpired",
  "isLoading",
]);
const emits = defineEmits(["backQr", "createNewPayment"]);

const handleClickBack = () => {
  emits("backQr");
};
const createNewPayment = () => {
  emits("createNewPayment");
};
</script>
