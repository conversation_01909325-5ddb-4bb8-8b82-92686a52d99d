<template>
  <div>
    <div v-if="!isPageDetailReturnOrder" v-for="product in products">
      <ItemMobile :product="product"></ItemMobile>
    </div>
    <div v-else v-for="product in productReturnDetail">
      <ItemMobile :product="product"></ItemMobile>
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const props = defineProps(["isPageDetailReturnOrder"]);
const returnStore = returnOrderStore();
const products = computed(() => orderStore.orderDetail?.orderItemProfiles);
const productReturnDetail = computed(
  () => returnStore.orderReturnDetail?.activeOrderItemProfiles
);
</script>
