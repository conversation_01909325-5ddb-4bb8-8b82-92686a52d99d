import { format, parseISO, isValid } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";

export function formatTimestamp(timestamp: string | number): string {
  if (typeof timestamp === "string") {
    const date = parseISO(timestamp);
    if (!isValid(date)) {
      console.error("Invalid timestamp:", timestamp);
      return "N/A";
    }
    return format(date, "dd/MM/yyyy hh:mm", { locale: vi });
  } else if (typeof timestamp === "number") {
    const date = new Date(timestamp);
    if (!isValid(date)) {
      console.error("Invalid timestamp:", timestamp);
      return "N/A";
    }
    return format(date, "dd/MM/yyyy hh:mm", { locale: vi });
  } else {
    console.error("Invalid timestamp:", timestamp);
    return "N/A";
  }
}

// Helper function to convert timestamp to Vietnam timezone
function toVietnamTime(timestamp: string | number): Date | null {
  // Check for null, undefined, empty string
  if (!timestamp && timestamp !== 0) {
    return null;
  }

  const date =
    typeof timestamp === "string" ? parseISO(timestamp) : new Date(timestamp);

  // Check if the date is valid
  if (!isValid(date)) {
    return null;
  }

  return toZonedTime(date, "Asia/Ho_Chi_Minh");
}

export function formatTimestampV2(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "dd/MM/yyyy", { locale: vi });
}

export function formatTimestampV3(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "hh:mm dd/MM/yyyy", { locale: vi });
}

export function formatTimestampV4(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "yyyy-MM-dd", { locale: vi });
}

export function formatTimestampV5(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "MM/dd", { locale: vi });
}

export function formatTimestampV6(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "eeee", { locale: vi });
}

export function formatTimestampV7(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "hh:mm-dd/MM", { locale: vi });
}

export function formatTimestampV8(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "hh:mm", { locale: vi });
}

export function formatTimeWithMoment(timestamp: string | number): string {
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "h:mm a", { locale: vi });
}

export function formatTime24hWithMoment(timestamp: string | number): string {
  // Định dạng thời gian theo "HH:mm" (24 giờ)
  const vietnamDate = toVietnamTime(timestamp);
  if (!vietnamDate) return "N/A";
  return format(vietnamDate, "HH:mm", { locale: vi });
}
