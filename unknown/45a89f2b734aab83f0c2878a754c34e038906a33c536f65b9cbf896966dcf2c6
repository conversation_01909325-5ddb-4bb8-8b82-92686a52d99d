<template>
  <!-- Use Teleport to render modal at root level to avoid nested modals -->
  <Teleport to="body">
    <!-- Modal Backdrop with Blur Effect -->
    <Transition
      name="modal"
      enter-active-class="transition-all duration-300 ease-out"
      leave-active-class="transition-all duration-200 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[110] p-4 text-sm"
        @click.self="handleCloseModal"
      >
        <!-- Modal Container -->
        <Transition
          name="modal-content"
          enter-active-class="transition-all duration-300 ease-out"
          leave-active-class="transition-all duration-200 ease-in"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div
            class="bg-white w-full max-w-2xl shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-300 ease-in-out max-h-[90vh] flex flex-col"
          >
            <!-- Header -->
            <div class="bg-primary px-6 py-4 flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <button
                  @click="handleCloseModal"
                  class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 group"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 transform group-hover:-translate-x-1 transition-transform duration-200"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  <span class="text-sm font-medium">Trở lại</span>
                </button>
              </div>

              <h2
                class="text-xl font-bold text-white flex items-center space-x-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span>Thêm địa chỉ giao hàng</span>
              </h2>

              <button
                @click="handleSaveShippingAddress"
                :disabled="isLoading || !isFormValid"
                type="button"
                class="bg-white text-primary px-4 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
              >
                <svg
                  v-if="isLoading"
                  class="animate-spin h-4 w-4"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>{{ isLoading ? "Đang lưu..." : "Lưu địa chỉ" }}</span>
              </button>
            </div>
            <!-- Form Content -->
            <div class="flex-1 overflow-y-auto">
              <form class="p-6 space-y-2">
                <!-- Contact Information Section -->
                <div class="space-y-2">
                  <h3
                    class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    <span>Thông tin liên hệ</span>
                  </h3>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name Field -->
                    <div class="space-y-2">
                      <label
                        class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                      >
                        <span>Họ và tên</span>
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="relative">
                        <input
                          v-model="name"
                          type="text"
                          placeholder="Nhập họ và tên"
                          class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                          :class="{
                            'border-red-500 focus:ring-red-500': errors.name,
                          }"
                        />
                        <div
                          v-if="errors.name"
                          class="absolute -bottom-5 left-0 text-xs text-red-500"
                        >
                          {{ errors.name }}
                        </div>
                      </div>
                    </div>

                    <!-- Phone Field -->
                    <div class="space-y-2">
                      <label
                        class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                      >
                        <span>Điện thoại</span>
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="relative">
                        <input
                          v-model="phone"
                          type="tel"
                          placeholder="Nhập số điện thoại"
                          class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                          :class="{
                            'border-red-500 focus:ring-red-500': errors.phone,
                          }"
                        />
                        <div
                          v-if="errors.phone"
                          class="absolute -bottom-5 left-0 text-xs text-red-500"
                        >
                          {{ errors.phone }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Address Information Section -->
                <div class="space-y-2">
                  <h3
                    class="text-lg font-semibold text-gray-800 flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <span>Thông tin địa chỉ</span>
                  </h3>

                  <div class="grid grid-cols-1 gap-4">
                    <!-- Address Field -->
                    <div class="space-y-2">
                      <label
                        class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                      >
                        <span>Địa chỉ chi tiết</span>
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="relative">
                        <input
                          v-model="address"
                          type="text"
                          placeholder="Nhập địa chỉ chi tiết (số nhà, tên đường...)"
                          class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                          :class="{
                            'border-red-500 focus:ring-red-500': errors.address,
                          }"
                        />
                        <div
                          v-if="errors.address"
                          class="absolute -bottom-5 left-0 text-xs text-red-500"
                        >
                          {{ errors.address }}
                        </div>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <!-- Province Field -->
                      <div class="space-y-2">
                        <label
                          class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                        >
                          <span>Tỉnh/Thành phố</span>
                          <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                          <select
                            v-model="selectedCity"
                            @change="fetchDistricts"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                            :class="{
                              'border-red-500 focus:ring-red-500':
                                errors.selectedCity,
                            }"
                          >
                            <option value="">Chọn tỉnh/thành phố</option>
                            <option
                              v-for="province in provinces"
                              :key="province.geoId"
                              :value="province.geoId"
                            >
                              {{ province.geoName }}
                            </option>
                          </select>
                          <div
                            v-if="errors.selectedCity"
                            class="absolute -bottom-5 left-0 text-xs text-red-500"
                          >
                            {{ errors.selectedCity }}
                          </div>
                        </div>
                      </div>

                      <!-- District Field -->
                      <div class="space-y-2">
                        <label
                          class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                        >
                          <span>Quận/Huyện</span>
                          <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                          <select
                            v-model="selectedDistrict"
                            @change="fetchWards"
                            :disabled="!selectedCity"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            :class="{
                              'border-red-500 focus:ring-red-500':
                                errors.selectedDistrict,
                            }"
                          >
                            <option value="">Chọn quận/huyện</option>
                            <option
                              v-for="district in districts"
                              :key="district.geoId"
                              :value="district.geoId"
                            >
                              {{ district.geoName }}
                            </option>
                          </select>
                          <div
                            v-if="errors.selectedDistrict"
                            class="absolute -bottom-5 left-0 text-xs text-red-500"
                          >
                            {{ errors.selectedDistrict }}
                          </div>
                        </div>
                      </div>

                      <!-- Ward Field -->
                      <div class="space-y-2">
                        <label
                          class="text-sm font-semibold text-gray-700 flex items-center space-x-1"
                        >
                          <span>Phường/Xã</span>
                          <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                          <select
                            v-model="selectedWard"
                            :disabled="!selectedDistrict"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-white text-base outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            :class="{
                              'border-red-500 focus:ring-red-500':
                                errors.selectedWard,
                            }"
                          >
                            <option value="">Chọn phường/xã</option>
                            <option
                              v-for="ward in wards"
                              :key="ward.geoId"
                              :value="ward.geoId"
                            >
                              {{ ward.geoName }}
                            </option>
                          </select>
                          <div
                            v-if="errors.selectedWard"
                            class="absolute -bottom-5 left-0 text-xs text-red-500"
                          >
                            {{ errors.selectedWard }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
<script setup>
// Composables
const { createShippingAddress } = useOrderStore();
const { updateOrderCustomer } = useOrder();
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { $sdk } = useNuxtApp();
const route = useRoute();

// Props & Emits
const props = defineProps(["customer", "isManagerCustomer", "isPageSale"]);
const emits = defineEmits(["closeModalCreateShippingAddress"]);

// Reactive Data
const isLoading = ref(false);
const name = ref("");
const phone = ref("");
const address = ref("");

// Location Data
const provinces = ref([]);
const districts = ref([]);
const wards = ref([]);
const selectedCity = ref("");
const selectedDistrict = ref("");
const selectedWard = ref("");

// Form Validation
const errors = ref({
  name: "",
  phone: "",
  address: "",
  selectedCity: "",
  selectedDistrict: "",
  selectedWard: "",
});

// Validation Rules
const validateName = (value) => {
  if (!value || value.trim().length === 0) {
    return "Họ và tên là bắt buộc";
  }
  if (value.trim().length < 2) {
    return "Họ và tên phải có ít nhất 2 ký tự";
  }
  return "";
};

const validatePhone = (value) => {
  if (!value || value.trim().length === 0) {
    return "Số điện thoại là bắt buộc";
  }
  const phoneRegex = /^[0-9]{10,11}$/;
  if (!phoneRegex.test(value.replace(/\s/g, ""))) {
    return "Số điện thoại không hợp lệ";
  }
  return "";
};

const validateAddress = (value) => {
  if (!value || value.trim().length === 0) {
    return "Địa chỉ chi tiết là bắt buộc";
  }
  return "";
};

const validateLocation = () => {
  const locationErrors = {};

  if (!selectedCity.value) {
    locationErrors.selectedCity = "Vui lòng chọn tỉnh/thành phố";
  }
  if (!selectedDistrict.value) {
    locationErrors.selectedDistrict = "Vui lòng chọn quận/huyện";
  }
  if (!selectedWard.value) {
    locationErrors.selectedWard = "Vui lòng chọn phường/xã";
  }

  return locationErrors;
};

// Form Validation Computed
const isFormValid = computed(() => {
  return (
    name.value &&
    name.value.trim().length >= 2 &&
    phone.value &&
    validatePhone(phone.value) === "" &&
    address.value &&
    address.value.trim().length > 0 &&
    selectedCity.value &&
    selectedDistrict.value &&
    selectedWard.value
  );
});

// Watchers for real-time validation
watch(name, (newValue) => {
  errors.value.name = validateName(newValue);
});

watch(phone, (newValue) => {
  errors.value.phone = validatePhone(newValue);
});

watch(address, (newValue) => {
  errors.value.address = validateAddress(newValue);
});

watch(selectedCity, (newValue) => {
  if (newValue) {
    errors.value.selectedCity = "";
  }
});

watch(selectedDistrict, (newValue) => {
  if (newValue) {
    errors.value.selectedDistrict = "";
  }
});

watch(selectedWard, (newValue) => {
  if (newValue) {
    errors.value.selectedWard = "";
  }
});
// Methods
const handleCloseModal = () => {
  emits("closeModalCreateShippingAddress", false);
};

const getProvinces = async () => {
  try {
    const response = await $sdk.user.getProvinces();
    provinces.value = response;
  } catch (error) {
    console.error("Error fetching provinces:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách tỉnh/thành phố");
  }
};

const fetchDistricts = async () => {
  if (!selectedCity.value) return;

  try {
    const response = await $sdk.user.getDistricts(selectedCity.value);
    districts.value = response;
    selectedDistrict.value = "";
    wards.value = [];
    selectedWard.value = "";
    // Clear district and ward errors when city changes
    errors.value.selectedDistrict = "";
    errors.value.selectedWard = "";
  } catch (error) {
    console.error("Error fetching districts:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách quận/huyện");
  }
};

const fetchWards = async () => {
  if (!selectedDistrict.value) return;

  try {
    const response = await $sdk.user.getWards(selectedDistrict.value);
    wards.value = response;
    selectedWard.value = "";
    // Clear ward error when district changes
    errors.value.selectedWard = "";
  } catch (error) {
    console.error("Error fetching wards:", error);
    useNuxtApp().$toast?.error("Không thể tải danh sách phường/xã");
  }
};

const handleSaveShippingAddress = async () => {
  // Validate form before submission
  errors.value.name = validateName(name.value);
  errors.value.phone = validatePhone(phone.value);
  errors.value.address = validateAddress(address.value);

  // Validate location fields
  if (!selectedCity.value) {
    errors.value.selectedCity = "Vui lòng chọn tỉnh/thành phố";
  }
  if (!selectedDistrict.value) {
    errors.value.selectedDistrict = "Vui lòng chọn quận/huyện";
  }
  if (!selectedWard.value) {
    errors.value.selectedWard = "Vui lòng chọn phường/xã";
  }

  if (!isFormValid.value) {
    return;
  }

  isLoading.value = true;

  try {
    // Validate phone number using SDK
    const res = $sdk.order.validatePhoneNumber(phone.value);
    if (!res) {
      useNuxtApp().$toast?.warning("Số điện thoại không hợp lệ");
      return;
    }

    const data = {
      name: name.value,
      phone: phone.value,
      address: address.value,
      province_code: selectedCity.value,
      district_code: selectedDistrict.value,
      ward_code: selectedWard.value,
      address_default: true,
    };

    const response = await createShippingAddress(props.customer?.id, data);

    if (props.isPageSale) {
      await updateOrderCustomer(
        route.query.orderId,
        props.customer?.id,
        response?.data?.id
      );
      await orderStore.updateOrder(orderStore.orderDetail?.id);
    }

    if (props.isManagerCustomer) {
      customerStore.handleGetShippingInfo(props.customer?.id);
    } else {
      orderStore.getOrderById(route.query.orderId);
    }

    // Success notification
    handleCloseModal();
  } catch (error) {
    console.error("Error creating shipping address:", error);
    useNuxtApp().$toast?.error("Có lỗi xảy ra khi thêm địa chỉ giao hàng");
  } finally {
    isLoading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  getProvinces();
});
</script>

<style scoped>
/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes for transitions */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(1rem);
}
</style>
