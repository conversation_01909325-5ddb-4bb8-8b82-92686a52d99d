<template>
  <div
    class="grid auto-cols-max grid-flow-col gap-2 mb-4 md:grid-cols-4 overflow-x-auto"
  >
    <div
      class="border hover:cursor-pointer relative w-28 md:w-full flex flex-col items-center justify-center"
      v-for="product in dataProduct(products)"
      :key="product.id"
      @click="addToCart(product)"
    >
      <div class="border-0 p-2">
        <div class="lg:w-[120px] lg:h-[120px] w-[50px] h-[50px]">
          <img
            class="object-contain w-full h-full"
            :src="product.featuredImage"
            loading="lazy"
          />
        </div>
        <div class="">
          <div
            class="text-justify text-xs font-medium lg:text-sm text-black line-clamp-2 overflow-hidden"
          >
            {{ product.title }}
          </div>
          <div class="font-medium flex items-center cursor-pointer pt-4">
            <div class="flex flex-col justify-center">
              <div class="">
                <span
                  class="text-sm overflow-hidden whitespace-nowrap overflow-ellipsis max-w-[130px]"
                >
                  {{ formatCurrency(product.price) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="isModalOpen">
    <ModalProductDetailDiary
      :isOpen="isModalOpen"
      :productId="productId"
      @close="closeModal"
    />
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
const props = defineProps({
  products: {
    type: Array,
    required: true,
  },
});
const { updateQuantityProductInOrder } = useOrder();
const route = useRoute();
const saleStore = useSale();
const isModalOpen = ref(false);
const productId = ref("");
const openModal = () => {
  isModalOpen.value = true;
};
const closeModal = () => {
  isModalOpen.value = false;
};

const dataProduct = (product) => {
  return product.slice(0, 10);
};
const addToCart = async (product) => {
  if (product.subType !== "VARIABLE") {
    const existingProduct = props.orderProducts?.find(
      (p) => p.orderLineItem.variant.id === product.id
    );
    if (existingProduct) {
      try {
        const currentOrderId = route.params.id;
        if (!currentOrderId) {
          return;
        }
        await updateQuantityProductInOrder(
          currentOrderId,
          existingProduct.id,
          existingProduct.orderLineItem.currentQuantity + 1
        );

        closeModal();
      } catch (error) {
        console.log("Error adding product to order");
        throw error;
      }
    } else {
      try {
        const currentOrderId = route.params.id;
        await saleStore.addProductToCart(currentOrderId, product);
        closeModal();
      } catch (error) {
        console.log("Error adding product to order");
        throw error;
      }
    }
  } else {
    productId.value = product.id;
    openModal();
  }
};
</script>
