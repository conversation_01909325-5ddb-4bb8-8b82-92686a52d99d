import { ref } from "vue";

export default function useLogger() {
  const loading = ref(false);

  // Placeholder functions - SQLite logging has been removed
  const writeLog = async (log: any) => {
    // SQLite logging functionality has been removed
    console.log("Log would have been written:", log);
    return { message: "Logging disabled - SQLite removed" };
  };

  const getLogs = async (page = 1, pageSize = 10) => {
    // SQLite logging functionality has been removed
    console.log("Logs would have been fetched");
    return { logs: [], total: 0 };
  };

  return {
    writeLog,
    getLogs,
    loading,
  };
}
