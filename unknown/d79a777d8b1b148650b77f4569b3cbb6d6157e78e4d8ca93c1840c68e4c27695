export default function useComhub() {
  const $sdk = useNuxtApp().$sdk;
  const shareOrder = async (
    dataRequest: any,
    oAId: string,
    templateType: string
  ) => {
    try {
      const response = await $sdk.comhub.sendZns(
        dataRequest,
        oAId,
        templateType
      );
      useNuxtApp().$toast.success(
        `${
          templateType === "SEND_ORDER_INFO"
            ? "Gửi thông tin đơn hàng thành công"
            : "Gửi đánh giá thành công"
        }`
      );
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.error(error?.response?.data?.message);
      throw error;
    }
  };
  const sendMessage = async (
    OAId: string,
    content: string,
    contentType: string,
    senderId: string,
    receiveContactIds: [string]
  ) => {
    try {
      const response = await $sdk.comhub.sendMessage(
        OAId,
        content,
        contentType,
        senderId,
        receiveContactIds
      );
      useNuxtApp().$toast.success("<PERSON><PERSON><PERSON> tin nhắn thành công");
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getTemplate = async (type: string) => {
    try {
      const response = await $sdk.omnigateway.getTemplate(type);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getInfoChat = async () => {
    try {
      const response = await $sdk.omnigateway.getInfoChatApp();
      // const parsedData = JSON.parse(response.data || "[]");
      localStorage.setItem("appId", JSON.stringify(response?.data || "[]"));
    } catch (error) {
      throw error;
    }
  };
  const closeTopic = async (topicId: string, updatedBy: string) => {
    try {
      const res = await $sdk.crm.closeTopic(topicId, updatedBy);
      return res;
    } catch (error) {
      throw error;
    }
  };
  return {
    shareOrder,
    sendMessage,
    getTemplate,
    getInfoChat,
    closeTopic,
  };
}
