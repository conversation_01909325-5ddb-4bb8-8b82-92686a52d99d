<template>
  <div class="bg-white p-2 md:rounded-xl">
    <div class="font-bold text-primary my-2">Thông tin thanh toán</div>
    <!-- Thông tin khách hàng -->
    <div class="flex items-center gap-1 text-sm w-full truncate">
      <div
        v-if="dataOrder[0]?.order?.ownerName"
        class="flex items-center gap-1"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
            />
          </svg>
        </span>
        <span>{{ dataOrder[0]?.order?.ownerName }}</span>
      </div>
      <div
        v-if="dataOrder[0]?.order?.ownerPhone"
        class="flex items-center gap-1"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
            />
          </svg>
        </span>
        <span>{{ dataOrder[0]?.order?.ownerPhone }}</span>
      </div>
      <div
        v-if="dataOrder[0]?.order?.ownerEmail"
        class="flex items-center gap-1"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
            />
          </svg>
        </span>
        <span>{{ dataOrder[0]?.order?.ownerEmail }}</span>
      </div>
    </div>
    <!-- cái bảng -->
    <table class="w-full mt-2">
      <thead>
        <tr class="bg-secondary">
          <th class="px-2 py-2 text-left font-semibold">Đơn hàng</th>
          <th class="px-2 py-2 text-right font-semibold">Cần thanh toán</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="order in dataPayment?.items" :key="order?.orderId">
          <td class="px-2 py-2 font-semibold">
            {{ order?.orderId }}
          </td>
          <td class="px-2 py-2 text-red-600 text-right">
            {{ formatCurrency(order?.totalAmount) }}
          </td>
        </tr>
        <!--  -->
        <tr class="border-t">
          <td class="px-2 py-2 font-bold">Tổng thanh toán</td>
          <td class="px-2 py-2 text-red-600 font-bold text-right">
            {{ formatCurrency(dataPayment?.totalAmount) }}
          </td>
        </tr>
      </tbody>
    </table>
    <div class="px-2">
      <span class="font-semibold">Nội dung thanh toán: </span>
      <span>{{ dataPayment?.orderInfo }}</span>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["dataOrder", "dataPayment"]);
</script>
