<template>
  <div
    v-if="dataOrder"
    :class="
      isManagerCustomer
        ? 'relative bg-white border mb-2'
        : 'relative bg-white mb-2'
    "
  >
    <div class="space-y-2">
      <div class="bg-card p-2">
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-1">
            <div class="flex items-center">
              <span class="font-semibold text-sm">#{{ dataOrder.id }}</span>
              <span :class="statusClass">{{
                dataOrder.statusDescription
              }}</span>
              <div
                v-if="dataOrder?.order?.customAttribute?.subType"
                class="text-sm"
              >
                <div
                  v-if="
                    dataOrder?.order?.customAttribute?.subType === 'RETURN_FULL'
                  "
                  class="font-semibold text-red-400"
                >
                  Trả toàn bộ
                </div>
                <div
                  v-else-if="
                    dataOrder?.order?.customAttribute?.subType ===
                    'EXCHANGE_ORDER'
                  "
                  class="font-semibold text-primary"
                >
                  Đơ<PERSON> đổi
                </div>
                <div v-else class="font-semibold text-orange-300">
                  Trả một phần
                </div>
              </div>
            </div>
          </div>
          <div
            @click="handleNavigate"
            class="flex gap-1 items-center text-sm text-primary cursor-pointer"
          >
            Chi tiết
          </div>
        </div>
        <div
          v-if="dataOrder?.order?.fulfillmentStatus"
          class="flex items-center text-sm gap-2 mb-1"
        >
          <span v-tippy="getFFMStatusText(dataOrder?.order?.fulfillmentStatus)">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"
              />
            </svg>
          </span>
          <span
            :class="getFFMStatusClass(dataOrder?.order?.fulfillmentStatus)"
            class="truncate"
          >
            {{ getFFMStatusText(dataOrder?.order?.fulfillmentStatus) }}
          </span>
        </div>
        <div class="text-sm w-full flex items-center justify-between gap-3">
          <div class="flex items-center gap-1">
            <div class="flex gap-1 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                />
              </svg>
              <span class="text-gray-500">{{
                dataOrder?.order?.ownerName
              }}</span>
            </div>
            <div class="flex gap-1 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                />
              </svg>
              <span class="text-gray-500">
                {{ dataOrder?.order?.ownerPhone }}
              </span>
            </div>
          </div>
          <div class="flex gap-1 items-center">
            <div class="flex items-center gap-1">
              <div class="font-semibold">Tạo:</div>
              <div class="text-gray-500">
                {{ employeeSale }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="text-sm w-full py-2 flex items-center justify-between gap-3"
        >
          <div class="flex items-center gap-[2px] text-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            <span class="text-gray-500">{{
              formatTimestampV7(dataOrder.order?.orderDate)
            }}</span>
          </div>
          <div class="flex gap-1 items-center">
            <div class="flex items-center gap-1">
              <div class="font-semibold">Tư vấn:</div>
              <div class="text-gray-500">
                {{ employee }}
              </div>
            </div>
          </div>
        </div>
        <ul class="list-disc list-inside">
          <li
            class="text-gray-700 text-sm sm:text-base flex items-center gap-2"
            v-for="product in dataOrder.activeOrderItemProfiles"
            :key="product.id"
          >
            <ProductSimpleCard :product="product.orderLineItem" />
          </li>
        </ul>

        <div
          class="flex items-center justify-between text-sm border-t mt-2 pt-2"
        >
          Tổng tiền:
          <span class="font-semibold">
            {{ formattedTotalPrice }}
          </span>
        </div>
        <div class="flex items-center justify-between text-sm">
          <span>Đã thanh toán: </span>
          <span class="text-green-500 font-semibold">
            {{ formatCurrency(dataOrder?.totalAlreadyPaid || 0) }}</span
          >
        </div>
        <div class="flex items-center justify-between text-sm">
          Còn nợ:
          <span class="text-red-500 font-semibold">
            {{ formatCurrency(dataOrder?.remainTotal || 0) }}
          </span>
        </div>
        <div
          v-if="
            dataOrder?.order?.customAttribute?.exportVatInvoiceStatus ===
            'INVOICE_PUBLISHED'
          "
          class="flex items-center justify-between text-sm"
          @click="toogleExportInvoice"
        >
          Mã hóa đơn:
          <span class="font-semibold">
            {{ numberOfInvoice?.value }}
          </span>
        </div>
        <!-- <div class="flex items-center justify-between text-sm">
          Trạng thái thanh toán:
          <div :class="paymentStatusClass">
            {{ dataOrder.financialStatusDescription }}
          </div>
        </div> -->
        <!-- action  icon -->
        <div
          v-if="!isManagerCustomer"
          class="flex justify-between items-center text-sm"
        >
          <!-- action order -->
          <div class="border-t mt-2 pt-2 w-full flex justify-between">
            <div class="flex items-center gap-[2px] text-sm"></div>
            <div class="flex items-center gap-2">
              <ZaloZnsMobile
                :diary="dataOrder"
                :isButtonSendMessage="false"
                :isNotDraft="true"
                :data="data"
              ></ZaloZnsMobile>
            </div>
          </div>
        </div>
      </div>
    </div>
    <DetailOrderDialog
      v-if="isOpenDetailDialog"
      :order="dataOrder"
      @cancel="toogleDetailOrder"
    ></DetailOrderDialog>
    <ExportInvoicePopup
      v-if="isOpenExportInvoice"
      :order="dataOrder"
      @confirm="toogleExportInvoice"
      @cancel="toogleExportInvoice"
    ></ExportInvoicePopup>
  </div>
</template>

<script setup lang="ts">
const ExportInvoicePopup = defineAsyncComponent(
  () => import("~/components/dialog/ExportInvoicePopup.vue")
);
const DetailOrderDialog = defineAsyncComponent(
  () => import("~/components/dialog/DetailOrderDialog.vue")
);
import { computed, ref } from "vue";
const { searchEmployes } = useOrder();
const props = defineProps(["dataOrder", "isManagerCustomer", "data"]);
const isOpen = ref<Boolean>(false);

const statusClass = computed(() =>
  getOrderStatusClass(props.dataOrder?.status, "semibold")
);
const paymentStatusClass = computed(() =>
  getPaymentStatusClass(props.dataOrder?.financialStatusDescription, "simple")
);
const formattedTotalPrice = computed(() =>
  formatCurrency(props.dataOrder?.order?.totalPrice?.amount)
);
const { getInvoicesOfOrder } = useInvoice();
const numberOfInvoice = ref();
const handleView = async () => {
  if (
    props.dataOrder?.order?.customAttribute?.exportVatInvoiceStatus !==
    "INVOICE_PUBLISHED"
  ) {
    return;
  }
  try {
    const response = await getInvoicesOfOrder(props.dataOrder?.id);
    if (response?.length >= 0) {
      numberOfInvoice.value = response[0]?.attributes?.find(
        (invoice: any) => invoice.name === "INV_NO"
      );
    }
  } catch (error) {
    throw error;
  }
};
const isOpenExportInvoice = ref(false);
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
// Import utilities
import {
  getOrderStatusClass,
  getPaymentStatusClass,
  getFFMStatusClass,
  getFFMStatusText,
} from "~/utils/statusHelpers";
const employee = ref();
const diaryStore = useDiariesStore();

const handleSearchEmployee = async (idEmployee: string) => {
  if (!idEmployee) return;
  // Lấy danh sách từ localStorage
  const existingList = JSON.parse(localStorage.getItem("listEmployee") || "[]");

  // Tìm trong danh sách nhân viên hiện tại
  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );

  if (res) {
    // Nếu tìm thấy trong danh sách hiện tại, cập nhật giá trị
    employee.value = res?.name;
  } else {
    // Nếu không có trong danh sách nhân viên, kiểm tra trong localStorage
    const localEmployee = existingList.find(
      (item: any) => item.id === idEmployee
    );

    if (localEmployee) {
      // Nếu tìm thấy trong localStorage, cập nhật giá trị
      employee.value = localEmployee.name;
    } else {
      // Nếu không tìm thấy, gọi API để tìm kiếm
      const data: any = {
        keyword: idEmployee,
        positionShortName: "",
      };

      try {
        const res = await searchEmployes(data);

        if (res && res.length > 0) {
          // Cập nhật giá trị `employee` từ kết quả API
          employee.value = res[0]?.name;

          // Thêm kết quả mới vào danh sách `localStorage`
          existingList.push(res[0]);
          localStorage.setItem("listEmployee", JSON.stringify(existingList));
        }
      } catch (error) {
        console.error("Lỗi khi gọi API searchEmployes:", error);
      }
    }
  }
};
const employeeSale = ref();
const handleSearchEmployeeSale = async (idEmployee: string) => {
  if (!idEmployee) return;

  // Lấy danh sách từ localStorage
  const existingList = JSON.parse(localStorage.getItem("listEmployee") || "[]");

  // Tìm trong danh sách nhân viên hiện tại
  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );

  if (res) {
    // Nếu tìm thấy trong danh sách hiện tại, cập nhật giá trị
    employeeSale.value = res?.name;
  } else {
    // Nếu không có trong danh sách nhân viên, kiểm tra trong localStorage
    const localEmployee = existingList.find(
      (item: any) => item.id === idEmployee
    );

    if (localEmployee) {
      // Nếu tìm thấy trong localStorage, cập nhật giá trị
      employeeSale.value = localEmployee.name;
    } else {
      // Nếu không tìm thấy, gọi API để tìm kiếm
      const data: any = {
        keyword: idEmployee,
        positionShortName: "",
      };
      try {
        const res = await searchEmployes(data);
        if (res && res.length > 0) {
          // Cập nhật giá trị `employee` từ kết quả API
          employeeSale.value = res[0]?.name;

          // Thêm kết quả mới vào danh sách `localStorage`
          existingList.push(res[0]);
          localStorage.setItem("listEmployee", JSON.stringify(existingList));
        }
      } catch (error) {
        throw error;
      }
    }
  }
};
const { printOrderHTML } = useOrder();
import printJS from "print-js";

const handlePrintOrder = async () => {
  try {
    const response = await printOrderHTML(props.dataOrder?.id);
    const data = response.data;
    const printContainer = document.createElement("div");
    printContainer.id = "print-test";
    printContainer.innerHTML = `${data}`;

    printContainer.style.width = "80mm";
    printContainer.style.overflow = "visible";
    printContainer.style.height = "auto";

    document.body.appendChild(printContainer);

    printJS({
      printable: "print-test",
      type: "html",
      scanStyles: true,
      maxWidth: 80 * 3.78,
      targetStyles: ["*"],
    });

    document.body.removeChild(printContainer);
  } catch (error) {
    console.error("Error printing the order:", error);
    throw error;
  }
};

onMounted(async () => {
  await Promise.allSettled([
    handleSearchEmployee(props.dataOrder?.order?.salePartyId),
    handleSearchEmployeeSale(props.dataOrder?.order?.createdBy),
    handleView(),
  ]);
});
const route = useRoute();
const emits = defineEmits(["handleLoading"]);
const isOpenDetailDialog = ref(false);
const toogleDetailOrder = () => {
  isOpenDetailDialog.value = false;
};
const handleNavigate = async () => {
  if (props.isManagerCustomer) {
    isOpenDetailDialog.value = true;
    return;
  }

  let timeout;
  let isEmitted = false;

  // Đặt timeout để emit nếu quá 200ms
  timeout = setTimeout(() => {
    isEmitted = true;
    emits("handleLoading", true); // Emit trạng thái loading
  }, 200);

  try {
    if (props.dataOrder?.order?.orderGroup === "RETURN_ORDER") {
      await navigateTo(
        `/order/return/detail?orderId=${props.dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
      );
      return;
    }
    if (props.dataOrder?.status === "DRAFT") {
      await navigateTo(
        `/diary?orderId=${props.dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
      );
    } else {
      await navigateTo(
        `/sale?orderId=${props.dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
      );
    }
  } catch (error) {
    console.error("Navigation error:", error);
  } finally {
    // Xóa timeout nếu navigate hoàn thành trước 200ms
    clearTimeout(timeout);

    // Nếu đã emit, thông báo kết thúc loading
    if (isEmitted) {
      emits("handleLoading", false);
    }
  }
};
// Functions moved to utilities
</script>
