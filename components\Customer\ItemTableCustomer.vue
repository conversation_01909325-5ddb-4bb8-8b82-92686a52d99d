<template>
  <!-- STT -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
    <div
      class="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full"
    >
      <span class="text-xs font-medium">{{ index + 1 }}</span>
    </div>
  </td>

  <!-- Customer ID -->
  <td class="px-6 py-4 whitespace-nowrap text-sm">
    <NuxtLink
      :to="`customer/detail?orgId=${route.query.orgId}&storeId=${route.query.storeId}&customerId=${customer?.id}`"
      class="text-primary hover:text-primary/80 font-medium transition-colors duration-200 hover:underline"
    >
      #{{ customer?.id }}
    </NuxtLink>
  </td>

  <!-- Customer Name -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <div class="flex-shrink-0 h-10 w-10">
        <div
          class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        </div>
      </div>
      <div class="ml-4">
        <div class="text-sm font-medium text-gray-900 capitalize">
          {{ customer?.name }}
        </div>
        <div v-if="customer?.email" class="text-sm text-gray-500">
          {{ customer?.email }}
        </div>
      </div>
    </div>
  </td>

  <!-- Phone -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-400 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
      <span class="text-sm text-gray-900">{{ customer?.phone }}</span>
    </div>
  </td>

  <!-- Birth Date -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
    <div v-if="customer?.birthDate" class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-400 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      </svg>
      <span>{{ formatTimestampV2(customer?.birthDate) }}</span>
    </div>
    <span v-else class="text-gray-400 text-xs">Chưa cập nhật</span>
  </td>

  <!-- Email -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
    <div v-if="customer?.email" class="flex items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-400 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
      <span class="truncate max-w-[200px]">{{ customer?.email }}</span>
    </div>
    <span v-else class="text-gray-400 text-xs">Chưa cập nhật</span>
  </td>

  <!-- Address -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
    <div v-if="customer?.address" class="flex items-start">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-400 mr-2 mt-0.5 flex-shrink-0"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
        />
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
        />
      </svg>
      <span class="truncate max-w-[200px]">{{ customer?.address }}</span>
    </div>
    <span v-else class="text-gray-400 text-xs">Chưa cập nhật</span>
  </td>

  <!-- Checkbox -->
  <td class="px-6 py-4 whitespace-nowrap text-center">
    <input
      type="checkbox"
      class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2 cursor-pointer"
      v-model="isChecked"
    />
  </td>
</template>
<script setup>
const props = defineProps(["customer", "index"]);
const route = useRoute();
const isChecked = ref(false);
const customerStore = useCustomerStore();
watch(
  () => isChecked.value,
  (newVal, oldVal) => {
    if (newVal) {
      customerStore.addListCustomerAction(props.customer);
    } else {
      customerStore.removeCustomerListAction(props.customer);
    }
  }
);
watch(
  () => customerStore.isCheckGlobal,
  (newVal, oldVal) => {
    if (newVal) {
      isChecked.value = true;
    } else {
      isChecked.value = false;
    }
  }
);
</script>
