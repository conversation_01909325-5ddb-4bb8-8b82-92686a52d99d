<template>
  <div
    class="w-full h-full animate-pulse bg-white space-y-2 my-2 border max-h-[115px] p-2 rounded-lg"
  >
    <div class="flex items-center justify-between">
      <div class="bg-gray-200 w-1/2 text-gray-200 border rounded">a</div>
      <div class="bg-gray-200 w-[50px] text-gray-200 border rounded">b</div>
    </div>
    <div class="border-b mx-6 my-4"></div>
    <div class="bg-gray-200 w-1/2 text-gray-200 border rounded">1</div>
    <div class="bg-gray-200 w-3/4 text-gray-200 border rounded">1</div>
  </div>
</template>
<script setup lang="ts"></script>



