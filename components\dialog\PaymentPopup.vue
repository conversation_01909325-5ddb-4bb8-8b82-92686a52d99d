<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-lg w-full animate-popup"
    >
      <div class="flex items-center justify-between">
        <div></div>
        <div class="font-bold">Lịch sử thanh toán</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[200px] overflow-y-auto">
        <div
          v-if="listPayment?.length > 0"
          v-for="payment in listPayment"
          :key="payment?.paymentId"
        >
          <div class="border-b py-1">
            <div class="text-sm font-semibold">
              <span class="font-semibold">Thời gian: </span>
              <span>{{ formatTimestampV3(payment?.transactionDate) }}</span>
            </div>
            <div class="text-sm">
              <span class="font-semibold">Phương thức: </span>
              <span
                >{{ payment?.methodDescription }} -
                {{ payment?.statusDescription }}</span
              >
            </div>
            <div class="text-sm">
              <span class="font-semibold">Mã giao dịch: </span>
              <span>{{ payment?.paymentId }}</span>
            </div>
            <div class="text-sm">
              <span class="font-semibold">Số tiền: </span>
              <span>{{ formatCurrency(payment?.totalAmount) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const props = defineProps(["orderDetail"]);
const { paymentsByOrders } = usePayment();
const listPayment = ref();
const getPayment = async () => {
  try {
    const response = await paymentsByOrders([props.orderDetail?.id]);
    listPayment.value = response;
  } catch (error) {
    console.error(error);
  }
};
onMounted(async () => {
  await getPayment();
});
</script>

<style scoped></style>
