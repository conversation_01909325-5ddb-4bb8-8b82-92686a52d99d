<template>
  <div
    class="flex items-center justify-between md:max-w-[900px] mt-4 w-full overflow-x-auto"
  >
    <div
      v-for="(step, index) in dataProcessPineLine?.processPipeline"
      :key="index"
      class="relative flex items-center w-full"
    >
      <div
        @click="handleClickStep(step, index)"
        class="flex items-center justify-center w-8 h-8 rounded-full z-10 cursor-pointer"
        :class="{
          'bg-primary text-white': index <= currentStep,
          'bg-gray-300 text-gray-500': index > currentStep,
        }"
      >
        <span v-if="index <= currentStep" class="bg- rounded-xl font-bold">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="size-4"
          >
            <path
              fill-rule="evenodd"
              d="M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z"
              clip-rule="evenodd"
            />
          </svg>
        </span>
        <span v-else>{{ index + 1 }}</span>
      </div>
      <div
        v-if="index < dataProcessPineLine?.processPipeline.length - 1"
        class="absolute top-1/2 left-11 md:left-[100px] w-full"
      >
        <div
          class="h-1"
          :class="{
            'bg-primary': index < currentStep,
            'bg-gray-300': index >= currentStep,
          }"
          style="left: 50%; transform: translateX(calc(-50% + 1rem))"
        ></div>
      </div>
    </div>
  </div>
  <div class="flex mt-2 text-sm w-full md:max-w-[900px] overflow-x-auto">
    <div
      v-for="(step, index) in dataProcessPineLine?.processPipeline"
      :key="index"
      class="flex-1 justify-center"
      :class="{
        'text-primary': index <= currentStep,
        'text-gray-500': index > currentStep,
      }"
    >
      {{ step.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
const props = defineProps({
  dataProcessPineLine: Object,
});
//  const { dataProcessPineLine } = defineProps(["dataProcessPineLine"]);
const checkinStore = useCheckinStore();
const route = useRoute();
const currentStep = computed(() => checkinStore.curentIndex || 0);
const handleClickStep = async (step: any, index: number) => {

  checkinStore.curentIndex = index;
  await checkinStore.handleUpdateProcessStatus(
    props.dataProcessPineLine?.id,
    step?.id,
    ""
  );
  await checkinStore.getCheckinById(route.params.id as string);
};

</script>
