/**
 * Transaction Helper Utilities
 * Centralized functions for handling transaction data formatting and display
 */

import {
  Transaction,
  TransactionStatus,
  PaymentGatewayType,
  PaymentConfirmStatus,
  TransactionPurpose,
} from "~/types/Transaction";

// Status display mappings
const TRANSACTION_STATUS_CLASSES: Record<TransactionStatus, string> = {
  [TransactionStatus.PENDING]:
    "bg-yellow-100 text-yellow-800 rounded-full px-2 py-1 text-sm",
  [TransactionStatus.SUCCESS]:
    "bg-green-200 text-green-600 rounded-full px-2 py-1 text-sm",
  [TransactionStatus.FAILED]:
    "bg-red-100 text-red-800 rounded-full px-2 py-1 text-sm",
  [TransactionStatus.CANCELLED]:
    "bg-gray-100 text-gray-800 rounded-full px-2 py-1 text-sm",
  [TransactionStatus.PROCESSING]:
    "bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-sm",
} as const;

const TRANSACTION_STATUS_TEXT: Record<TransactionStatus, string> = {
  [TransactionStatus.PENDING]: "Đang chờ",
  [TransactionStatus.SUCCESS]: "Hoàn thành",
  [TransactionStatus.FAILED]: "Thất bại",
  [TransactionStatus.CANCELLED]: "Đã hủy",
  [TransactionStatus.PROCESSING]: "Đang xử lý",
} as const;

const PAYMENT_CONFIRM_STATUS_CLASSES: Record<PaymentConfirmStatus, string> = {
  [PaymentConfirmStatus.PENDING]:
    "bg-yellow-100 text-yellow-800 rounded-full px-2 py-1 text-sm",
  [PaymentConfirmStatus.CONFIRMED]:
    "bg-green-100 text-green-800 rounded-full px-2 py-1 text-sm",
  [PaymentConfirmStatus.REJECTED]:
    "bg-red-100 text-red-800 rounded-full px-2 py-1 text-sm",
  [PaymentConfirmStatus.MANUAL_REVIEW]:
    "bg-orange-100 text-orange-800 rounded-full px-2 py-1 text-sm",
} as const;

const PAYMENT_CONFIRM_STATUS_TEXT: Record<PaymentConfirmStatus, string> = {
  [PaymentConfirmStatus.PENDING]: "Chờ xác nhận",
  [PaymentConfirmStatus.CONFIRMED]: "Đã xác nhận",
  [PaymentConfirmStatus.REJECTED]: "Từ chối",
  [PaymentConfirmStatus.MANUAL_REVIEW]: "Xem xét thủ công",
} as const;

const PAYMENT_GATEWAY_TYPE_TEXT: Record<PaymentGatewayType, string> = {
  [PaymentGatewayType.BANK_TRANSACTION]: "Giao dịch ngân hàng",
  [PaymentGatewayType.OTHER_PAYMENT_GATEWAY]: "Cổng thanh toán khác",
} as const;

const TRANSACTION_PURPOSE_TEXT: Record<TransactionPurpose, string> = {
  [TransactionPurpose.SALE_ORDER]: "Thanh toán đơn hàng",
  [TransactionPurpose.COLLECT_FROM_GATEWAY]: "Rút tiền từ cổng thanh toán",
  [TransactionPurpose.OTHER]: "Khác",
} as const;

/**
 * Get CSS classes for transaction status
 * @param status - The transaction status
 * @returns CSS class string
 */
export const getTransactionStatusClass = (
  status: TransactionStatus
): string => {
  return (
    TRANSACTION_STATUS_CLASSES[status] ||
    "bg-gray-100 text-gray-800 rounded-full px-2 py-1 text-sm"
  );
};

/**
 * Get display text for transaction status
 * @param status - The transaction status
 * @returns Vietnamese display text
 */
export const getTransactionStatusText = (status: TransactionStatus): string => {
  return TRANSACTION_STATUS_TEXT[status] || status;
};

/**
 * Get CSS classes for payment confirmation status
 * @param status - The payment confirmation status
 * @returns CSS class string
 */
export const getPaymentConfirmStatusClass = (
  status: PaymentConfirmStatus
): string => {
  return (
    PAYMENT_CONFIRM_STATUS_CLASSES[status] ||
    "bg-gray-100 text-gray-800 rounded-full px-2 py-1 text-sm"
  );
};

/**
 * Get display text for payment confirmation status
 * @param status - The payment confirmation status
 * @returns Vietnamese display text
 */
export const getPaymentConfirmStatusText = (
  status: PaymentConfirmStatus
): string => {
  return PAYMENT_CONFIRM_STATUS_TEXT[status] || status;
};

/**
 * Get display text for payment gateway type
 * @param type - The payment gateway type
 * @returns Vietnamese display text
 */
export const getPaymentGatewayTypeText = (type: PaymentGatewayType): string => {
  return PAYMENT_GATEWAY_TYPE_TEXT[type] || type;
};

/**
 * Get display text for transaction purpose
 * @param purpose - The transaction purpose
 * @returns Vietnamese display text
 */
export const getTransactionPurposeText = (
  purpose: TransactionPurpose
): string => {
  return TRANSACTION_PURPOSE_TEXT[purpose] || purpose;
};

/**
 * Format transaction amount with currency
 * @param amount - The amount to format
 * @param currencyCode - The currency code (default: VND)
 * @returns Formatted amount string
 */
export const formatTransactionAmount = (
  amount: number,
  currencyCode: string = "VND"
): string => {
  const formatter = new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
  return formatter.format(amount);
};

/**
 * Get transaction type based on amount and purpose
 * @param transaction - The transaction object
 * @returns Transaction type for UI display
 */
export const getTransactionType = (
  transaction: Transaction
): "income" | "expense" => {
  if (transaction.purpose === TransactionPurpose.SALE_ORDER) {
    return "income";
  }
  if (transaction.purpose === TransactionPurpose.COLLECT_FROM_GATEWAY) {
    return "expense";
  }
  // For other purposes, determine by amount sign or other logic
  return transaction.amount > 0 ? "income" : "expense";
};

/**
 * Format transaction date for display
 * @param date - The date to format
 * @returns Formatted date string
 */
export const formatTransactionDate = (date: Date): string => {
  return new Date(date).toLocaleDateString("vi-VN", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

/**
 * Format transaction time for display
 * @param date - The date to format
 * @returns Formatted time string
 */
export const formatTransactionTime = (date: Date): string => {
  return new Date(date).toLocaleTimeString("vi-VN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * Get bank name from bank code
 * @param bankCode - The bank code
 * @returns Bank display name
 */
export const getBankName = (bankCode: string): string => {
  const bankNames: Record<string, string> = {
    VCB: "Vietcombank",
    TCB: "Techcombank",
    VTB: "Vietinbank",
    BIDV: "BIDV",
    ACB: "ACB",
    MB: "MB Bank",
    VPB: "VPBank",
    TPB: "TPBank",
    STB: "Sacombank",
    EIB: "Eximbank",
    SHB: "SHB",
    OCB: "OCB",
    MSB: "MSB",
    CAKE: "CAKE by VPBank",
    UBANK: "Ubank by VPBank",
  };
  return bankNames[bankCode] || bankCode;
};

/**
 * Mask account number for security
 * @param accountNumber - The account number to mask
 * @returns Masked account number
 */
export const maskAccountNumber = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 4) return accountNumber;
  const visibleStart = accountNumber.slice(0, 4);
  const visibleEnd = accountNumber.slice(-4);
  const maskedMiddle = "*".repeat(Math.max(0, accountNumber.length - 8));
  return `${visibleStart}${maskedMiddle}${visibleEnd}`;
};

/**
 * Check if transaction is successful
 * @param transaction - The transaction object
 * @returns Boolean indicating success
 */
export const isTransactionSuccessful = (transaction: Transaction): boolean => {
  return (
    transaction.status === TransactionStatus.SUCCESS &&
    transaction.paymentConfirmStatus === PaymentConfirmStatus.CONFIRMED
  );
};

/**
 * Get transaction icon based on gateway type
 * @param gatewayType - The payment gateway type
 * @returns SVG path for icon
 */
export const getTransactionIcon = (gatewayType: PaymentGatewayType): string => {
  switch (gatewayType) {
    case PaymentGatewayType.BANK_TRANSACTION:
      return "M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z";
    case PaymentGatewayType.OTHER_PAYMENT_GATEWAY:
      return "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z";
    default:
      return "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z";
  }
};
