<template>
  <div class="space-y-2">
    <!-- Order Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Order Details -->
      <div class="space-y-1">
        <h3 class="text-sm font-bold text-gray-900 flex items-center gap-1">
          <div class="w-2 h-2 bg-primary rounded-full"></div>
          Thông tin đơn hàng
        </h3>
        <div class="space-y-1.5">
          <div class="flex justify-between items-center py-0.5">
            <span class="text-gray-600 text-sm">Mã đơn:</span>
            <span class="font-medium text-gray-900 text-sm">{{
              orderDetails?.data?.id
            }}</span>
          </div>
          <div class="flex justify-between items-center py-0.5">
            <span class="text-gray-600 text-sm">Tổng đơn:</span>
            <span class="font-bold text-primary text-sm">
              {{ formatCurrency(orderDetails?.data?.order?.totalPrice.amount) }}
            </span>
          </div>
          <div class="flex justify-between items-center py-0.5">
            <span class="text-gray-600 text-sm">Cần thanh toán:</span>
            <span class="font-bold text-red-600">
              {{
                paymentAmount > 0
                  ? formatCurrency(paymentAmount)
                  : formatCurrency(orderDetails?.data?.remainTotal)
              }}
            </span>
          </div>
        </div>
      </div>

      <!-- Customer Information -->
      <div class="space-y-2">
        <h3 class="text-sm font-bold text-gray-900 flex items-center gap-1">
          <div class="w-2 h-2 bg-primary rounded-full"></div>
          Thông tin ví khách hàng
        </h3>
        <div class="space-y-1.5">
          <div class="flex justify-between items-center py-0.5">
            <span class="text-gray-600 text-sm">Tên khách hàng:</span>
            <span class="font-medium text-gray-900 text-sm">
              {{ orderDetails?.data?.order?.ownerName || "Khách lẻ" }}
            </span>
          </div>
          <div class="flex justify-between items-center py-0.5">
            <span class="text-gray-600 text-sm">Số dư ví:</span>
            <span class="font-bold text-green-600 text-sm">
              {{ formatCurrency(paymentWallet?.balance || 0) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Print Button -->
      <div class="flex justify-end items-start">
        <button
          @click="handlePrintOrder"
          :disabled="isLoading"
          class="bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-lg flex items-center gap-1.5 transition-colors duration-200 shadow-sm hover:shadow-md text-sm"
        >
          <div
            v-if="isLoading"
            class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
          ></div>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
            />
          </svg>
          <span class="font-medium">
            In đơn hàng
            {{ getPrintCount(orderDetails?.data?.order?.customAttributes) }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import printJS from "print-js";

const props = defineProps(["orderDetails", "paymentAmount", "paymentWallet"]);
const emits = defineEmits(["updatePrintOrder"]);

const { printOrderHTML } = useOrder();
const isLoading = ref(false);

// Import utilities
import { getPrintCount } from "~/utils/orderHelpers";
const handlePrintOrder = async () => {
  isLoading.value = true;
  const url = useRequestURL();

  const baseUrl = `${url.origin}/thanh-toan?orderId=${
    props.orderDetails?.data?.id
  }&orgId=${url.searchParams.get("orgId")}&storeId=${url.searchParams.get(
    "storeId"
  )}`;
  try {
    const response = await printOrderHTML(
      props.orderDetails?.data?.id,
      "Chưa thanh toán",
      baseUrl
    );

    const data = response.data;
    emits("updatePrintOrder");

    printJS({
      printable: data,
      type: "raw-html",
      scanStyles: false,
      style: `
        @page { margin: 0; } /* Xóa margin mặc định của trang in */
        body { margin: 0; } /* Đảm bảo body không có margin thừa */
      `,
    });
  } catch (error) {
    console.error("Error printing the order:", error);
  } finally {
    isLoading.value = false;
  }
};
</script>
