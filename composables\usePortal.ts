export default function usePortal() {
  const $sdk = useNuxtApp().$sdk;
  const getDynamicForm = async (dataRequest: any) => {
    try {
      const response = await $sdk.portal.getDynamicForm(dataRequest);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateDynamicForm = async (dataRequest: any) => {
    try {
      const response = await $sdk.portal.updateDynamicForm(dataRequest);
      return response;
    } catch (error) {
      throw error;
    }
  };
  // TAG
  const createTag = async (
    title: string,
    createBy: string,
    partnerId?: string
  ) => {
    try {
      const response = await $sdk.portal.createTag(title, createBy, partnerId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  // search tag
  const searchTag = async (
    partnerId?: string,
    title?: string,
    id?: string,
    search?: string
  ) => {
    try {
      const response = await $sdk.portal.searchTag(
        partnerId,
        title,
        id,
        search
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getConnectorByResource = async (
    resourceId: string,
    resourceType: string,
    type: string
  ) => {
    try {
      const response = await $sdk.crm.getConnectorByResource(
        resourceId,
        resourceType,
        type
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createConnector = async (
    resourceId: string,
    resourceType: string,
    description: string,
    type: string,
    createdBy: string
  ) => {
    try {
      const response = await $sdk.crm.createConnector(
        resourceId,
        resourceType,
        description,
        type,
        createdBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const addTag = async (
    connectorId: string,
    tagTitle: string,
    tagId: string,
    addedBy: string
  ) => {
    try {
      const response = await $sdk.crm.addTag(
        connectorId,
        tagTitle,
        tagId,
        addedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const updateConnectorDescription = async (
    connectorId: string,
    description: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.crm.updateConnectorDescription(
        connectorId,
        description,
        updatedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getTags = async (connectorId: string) => {
    try {
      const response = await $sdk.crm.getTags(connectorId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const removeTag = async (
    connectorId: string,
    tagId: string,
    removedBy: string
  ) => {
    try {
      const response = await $sdk.crm.removeTag(connectorId, tagId, removedBy);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getUrlWarehousePortal = (sku: string, id: string) => {
    const url = $sdk.portal.redirectLink(sku, id);
    return url;
  };
  const getImageProducrUrl = (
    parentId: string,
    parentType: string,
    width?: number,
    height?: number
  ) => {
    const url = $sdk.portal.imageProduct(parentId, parentType, width, height);
    return url;
  };
  const completeOrder = async (orderId: string, byUser: string) => {
    try {
      const response = await $sdk.portal.completeOrder(orderId, byUser);
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getPackageBox = async () => {
    try {
      const response = await $sdk.portal.packageBoxes();
      return response;
    } catch (error) {
      throw error;
    }
  };
  const confirmExport = async (orderId: string, updateBy: string) => {
    try {
      const response = await $sdk.portal.confirmExport(orderId, updateBy);
      if (response?.status === 1.0) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const confirmPackage = async (
    orderId: string,
    packageBoxId: string,
    byUser: string
  ) => {
    try {
      const response = await $sdk.portal.confirmPackage(
        orderId,
        packageBoxId,
        byUser
      );
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const handlePackage = async (orderId: string, byUser: string) => {
    try {
      const response = await $sdk.portal.handlePackage(orderId, byUser);
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const shipmentParameter = async (orderId: string) => {
    try {
      const response = await $sdk.portal.shipmentParameter(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const connectShipment = async (orderId: string, byUser: string) => {
    try {
      const response = await $sdk.portal.connectShipment(orderId, byUser);
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const ffmStage = async (orderId: string) => {
    try {
      const response = await $sdk.portal.ffmStage(orderId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const completeCancelFFMOrder = async (
    orderId: string,
    note: string,
    reason: string
  ) => {
    try {
      const response = await $sdk.portal.completeCancelFFMOrder(
        orderId,
        note,
        reason
      );
      if (response?.status === 1) {
        useNuxtApp().$toast.success(response?.message);
      } else {
        useNuxtApp().$toast.warning(response?.message);
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    getDynamicForm,
    updateDynamicForm,
    createTag,
    searchTag,
    getConnectorByResource,
    createConnector,
    addTag,
    updateConnectorDescription,
    getTags,
    removeTag,
    getUrlWarehousePortal,
    getImageProducrUrl,
    completeOrder,
    getPackageBox,
    confirmExport,
    confirmPackage,
    handlePackage,
    shipmentParameter,
    connectShipment,
    ffmStage,
    completeCancelFFMOrder,
  };
}
