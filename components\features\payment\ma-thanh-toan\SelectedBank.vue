<template>
  <div>
    <div
      class="bg-white rounded-lg shadow-lg p-2 max-w-sm w-full animate-popup h-[100%]"
      @click.stop
    >
      <div class="flex items-center justify-between pb-2">
        <div class="font-bold"><PERSON><PERSON><PERSON> ngân hàng</div>
      </div>
      <div class="h-full overflow-y-auto">
        <div class="sticky top-0 bg-white z-10">
          <div class="relative">
            <input
              type="text"
              v-model="keyword"
              class="border w-full rounded p-2 outline-none text-base"
              placeholder="Tìm kiếm theo tên ngân hàng"
            />
            <!-- loading -->
            <div v-if="isLoading" class="absolute right-2 top-2">
              <svg
                class="animate-spin h-4 w-4 text-primary"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Item -->
        <div class="grid grid-cols-4 gap-4 my-2">
          <div
            v-for="bank in dataBank"
            class="border rounded items-center justify-center flex"
            @click="handleChooseBank(bank)"
          >
            <NuxtImg
              :src="bank?.appLogo"
              alt=""
              class="w-12 h-12 object-contain bg-white m-2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import debounce from "lodash/debounce";
const props = defineProps(["dataUserBank", "dataQrCode", "dataPayment"]);
const emit = defineEmits(["confirm", "cancel", "handleSelectedBank"]);
const isVisible = ref(true);
const keyword = ref("");
const value = ref();
const dataBank = ref();
const isAndroid = ref(false);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const { getAndroidBank, getIosBank } = usePayment();

// Hàm phát hiện nền tảng
const detectPlatform = () => {
  const userAgent = navigator?.userAgent;
  if (/iPad|iPhone|iPod/.test(userAgent)) return "iOS";
  if (/android/i.test(userAgent)) return "Android";
  if (/windows/i.test(userAgent)) return "Windows";
  return "unknown";
};

// Hàm xử lý device
const handleDevice = async () => {
  const platform = detectPlatform();
  if (platform === "Android") {
    isAndroid.value = true;
    dataBank.value = await getAndroidBank();
    value.value = "Android";
  } else if (platform === "iOS") {
    isAndroid.value = false;
    dataBank.value = await getIosBank();
    value.value = "iOS";
  } else {
    value.value = platform;
    dataBank.value = null;
  }
};
const isLoading = ref(false);
// Hàm tìm kiếm có debounce
const searchBank = debounce(async (newKeyword) => {
  isLoading.value = true;
  if (!newKeyword) {
    dataBank.value = isAndroid.value
      ? await getAndroidBank()
      : await getIosBank();
    isLoading.value = false;
    return;
  }
  const filteredBanks = (
    await (isAndroid.value ? getAndroidBank() : getIosBank())
  ).filter(
    (bank) =>
      bank.bankName.toLowerCase().includes(newKeyword.toLowerCase()) ||
      bank.appName.toLowerCase().includes(newKeyword.toLowerCase()) ||
      bank.appId.toLowerCase().includes(newKeyword.toLowerCase())
  );
  dataBank.value = filteredBanks;
  isLoading.value = false;
}, 300);

// Theo dõi thay đổi của keyword
watch(keyword, (newValue) => {
  searchBank(newValue);
});
const handleChooseBank = (bank) => {
  const bankCode = props.dataUserBank?.description.split(" ")[0];
  if (bank) {
    const url = `${bank?.deeplink}&ba=${
      props.dataUserBank?.gwPartnerCode
    }@${bankCode}&am=${
      props.dataQrCode?.totalAmount || props.dataPayment?.totalAmount
    }&tn=${`inv${props.dataPayment?.id}inv`}`;
    console.log("deeplink", url);
    window.location.href = url;
  } else {
    useNuxtApp().$toast.warning("Vui lòng chọn ngân hàng để thanh toán");
  }
};
onMounted(async () => {
  await handleDevice();
});
</script>
