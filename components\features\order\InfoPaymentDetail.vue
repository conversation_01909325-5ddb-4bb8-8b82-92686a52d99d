<template>
  <div class="md:col-span-3 col-span-6 bg-white p-2">
    <h2 class=" font-bold text-primary mb-2">
      Thông tin thanh toán
    </h2>
    <div class="space-y-2 text-sm">
      <div>
        <span class="text-gray-600 font-medium">Tiền hàng: </span>
        <span class="text-gray-900"
          >{{ formatCurrency(orderDetail?.order?.subtotalPrice?.amount) }}
        </span>
        <span class="text-gray-900">
          <span></span>
        </span>
      </div>

      <div v-if="listVoucher?.length > 0">
        <span class="text-gray-600 font-medium">Mã giảm giá: </span>
        <span class="text-gray-900">
          {{ formatCurrency(orderDetail?.order?.discountTotalPrice?.amount) }}
        </span>
      </div>
      <div v-for="voucher in listVoucher">
        <div class="text-sm">
          {{
            `${voucher?.voucherCode} (${formatCurrency(voucher?.value.amount)})`
          }}
        </div>
      </div>
      <div v-if="orderDetail?.order?.totalShippingPrice?.amount">
        <span class="text-gray-600 font-medium">Phí ship: </span>
        <span class="text-gray-900">
          <span>
            {{ formatCurrency(orderDetail?.order?.totalShippingPrice?.amount) }}
          </span>
        </span>
      </div>
      <div v-if="orderDetail?.order?.totalVAT?.amount">
        <span class="text-gray-600 font-medium">VAT (0%): </span>
        <span class="text-gray-900">
          <span
            >{{ formatCurrency(orderDetail?.order?.totalVAT?.amount) }}
          </span>
        </span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">Tổng thanh toán: </span>
        <span class="text-gray-900 font-normal">
          <span>{{
            formatCurrency(orderDetail?.order?.totalPrice?.amount)
          }}</span>
        </span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">Đã thanh toán: </span>
        <span class="font-normal">
          <span> {{ formatCurrency(orderDetail?.totalAlreadyPaid) }}</span>
        </span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">Còn nợ: </span>
        <span class="text-red-600">{{
          formatCurrency(
            orderDetail?.order?.totalPrice?.amount -
              orderDetail?.totalAlreadyPaid
          )
        }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const listVoucher = computed(
  () => orderStore.orderDetail?.order?.discountApplications
);
</script>
