<template>
  <div class="h-screen flex flex-col">
    <!-- Header with <PERSON> Button -->
    <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0 mx-2">
      <div class="px-4">
        <div class="flex items-center justify-between h-12">
          <button
            @click="handleEditOrder"
            class="inline-flex items-center gap-2 px-3 py-1.5 text-primary border border-primary bg-white hover:bg-primary hover:text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md text-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
              />
            </svg>
            <span><PERSON><PERSON><PERSON><PERSON> chỉnh đơn hàng</span>
          </button>

          <div class="hidden md:flex items-center gap-2">
            <div class="w-2 h-2 bg-primary rounded-full"></div>
            <h1 class="text-lg font-bold text-gray-900">Thanh toán đơn hàng</h1>
          </div>
        </div>
      </div>
    </div>
    <!-- Main Content -->
    <div class="flex-1 min-h-0">
      <!-- Payment Content -->
      <div v-if="!isPayment" class="h-full">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center h-full">
          <div class="text-center">
            <LoadingSpinner />
            <p class="mt-4 text-gray-600 font-medium">
              Đang tải thông tin thanh toán...
            </p>
          </div>
        </div>

        <!-- Payment Component -->
        <div v-else-if="orderDetails" class="h-full">
          <Payment
            :orderDetails="orderDetails"
            :dataPaymentMethod="dataPaymentMethod"
            :paymentAmount="paymentAmount"
          />
        </div>
      </div>

      <!-- Payment Success State -->
      <div
        v-if="isPayment"
        class="flex items-center justify-center min-h-[60vh]"
      >
        <div class="text-center">
          <div
            class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 max-w-md mx-auto"
          >
            <div
              class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                class="w-10 h-10 text-green-600"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"
                />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Thanh toán thành công!
            </h2>
            <p class="text-gray-600 mb-6">
              Đơn hàng của bạn đã được thanh toán hoàn tất.
            </p>
            <button
              @click="handleEditOrder"
              class="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200"
            >
              Quay lại trang bán hàng
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Thanh toán",
});
useHead({
  title: "Thanh toán",
  meta: [
    {
      name: "description",
      content: "Thanh toán",
    },
  ],
});
const paymentAmount = ref();
const route = useRoute();
const orderDetails = ref<any>(null);
const isPayment = ref<Boolean>(false);
const { fetchOrderDetails, updateStatusApproved } = useOrder();
const { dataPaymentMethod, getPaymentMethodTypes } = usePayment();
const fetchOrderData = async (orderId: string) => {
  try {
    // Thử lấy chi tiết đơn hàng
    try {
      orderDetails.value = await fetchOrderDetails(orderId);
      if (orderDetails.value.data.remainTotal === 0) {
        isPayment.value = true;
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
    }

    // Thử lấy thông tin ví khách hàng
    // try {
    //   await paymentStore.handleGetCustomerWallet(
    //     orderDetails.value?.data?.order?.ownerPartyId,
    //     "wallet"
    //   );
    // } catch (error) {
    //   console.error("Error fetching customer wallet:", error);
    // }
  } catch (error) {
    throw error;
  }
};

const paymentStore = usePaymentStore();
const isLoading = ref(false);
onMounted(async () => {
  paymentAmount.value = JSON.parse(
    localStorage.getItem("paymentAmount") || "0"
  );
  isPayment.value = false;
  isLoading.value = true;
  await Promise.allSettled([
    getPaymentMethodTypes(),
    paymentStore.getDataPaymentOrder(route.query.orderId as string),
    fetchOrderData(route.query.orderId as string),
  ]);
  isLoading.value = false;
});
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/payment`) {
    paymentAmount.value = JSON.parse(
      localStorage.getItem("paymentAmount") || "0"
    );
  }
  next();
});
watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    if (newVal) {
      isPayment.value = false;
      isLoading.value = true;
      await Promise.allSettled([
        getPaymentMethodTypes(),
        paymentStore.getDataPaymentOrder(newVal as string),
        await fetchOrderData(newVal as string),
      ]);
      isLoading.value = false;
    }
  }
);
const handleEditOrder = () => {
  navigateTo(
    `/sale?orderId=${route.query.orderId}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
  );
};
// Import utilities
import { updatePrintCount } from "~/utils/orderHelpers";

const handleUpdateQuantityPrintOrder = () => {
  if (orderDetails.value?.data?.order) {
    updatePrintCount(orderDetails.value.data.order);
  }
};
</script>
