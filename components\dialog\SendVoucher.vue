<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">Gửi tin nhắn đến khách hàng</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 md:size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[350px] overflow-y-auto">
        <div>
          <textarea
            rows="3"
            id="note"
            class="py-1 px-2 w-full text-base rounded outline-none border bg-secondary"
            placeholder="Nội dung tin nhắn..."
            v-model="message"
          ></textarea>
        </div>
      </div>
      <div class="flex items-center justify-end mt-2">
        <button
          @click="handleConfirm"
          class="bg-primary text-white px-2 py-1 rounded"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const message = ref();
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const { createTopic } = useCustomer();
const cusomerStore = useCustomerStore();
const listCustomerAction = computed(() => cusomerStore.listCustomerAction);
const handleConfirm = () => {
  emit("confirm", message.value);
};
</script>
