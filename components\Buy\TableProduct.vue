<template>
  <div>
    <!-- Th<PERSON><PERSON> lớp để bao bọc bảng và cho phép cuộn -->
    <div class="overflow-auto max-h-[60svh]">
      <table class="w-full border-collapse">
        <thead>
          <tr class="text-center text-sm bg-gray-100 sticky top-0 z-1">
            <th class="w-1/24 font-bold justify-center items-center flex py-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                />
              </svg>
            </th>
            <th class="w-1/12">Ảnh</th>
            <th :class="isPageDetailReturnOrder ? 'w-5/12' : 'w-4/12'">
              Tên sản phẩm
            </th>
            <th class="w-2/12 text-center">Đơn giá</th>
            <th class="w-1/12">Số lượng</th>
            <th v-if="isPageSale" class="w-2/12 text-center">Giảm giá</th>

            <th class="w-2/12">
              <div class="inline-flex items-center gap-1">
                <span>Thành tiền</span>
                <span v-tippy="'Chưa bao gồm VAT'" class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                    />
                  </svg>
                </span>
              </div>
            </th>
          </tr>
        </thead>

        <tbody v-if="!isPageDetailReturnOrder">
          <!-- Row mẫu cho từng sản phẩm -->
          <tr
            v-for="(product, index) in products"
            :key="product?.id"
            class="bg-white border-b"
          >
            <ItemTableCard
              :product="product"
              :isPageSale="isPageSale"
              :isDisable="orderDetail?.status === 'CANCELLED'"
            ></ItemTableCard>
          </tr>
          <!-- sản phẩm disable -->
          <tr
            v-for="(product, index) in dataListProductDiary"
            :key="product?.id"
            class="bg-white border-b"
          >
            <ItemTableCard
              :isProductDraft="true"
              :product="product"
              :isPageSale="isPageSale"
            ></ItemTableCard>
          </tr>
        </tbody>
        <tbody v-else>
          <!-- Row mẫu cho từng sản phẩm -->
          <tr
            v-for="(product, index) in productReturnDetail"
            :key="product?.id"
            class="bg-white border-b"
          >
            <ItemTableCard
              :product="product"
              :isPageSale="isPageSale"
              :isPageDetailReturnOrder="isPageDetailReturnOrder"
            ></ItemTableCard>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["isPageSale", "isPageDetailReturnOrder"]);
const orderStore = useOrderStore();
const returnStore = returnOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const products = computed(
  () => orderStore.orderDetail?.activeOrderItemProfiles
);
const productReturnDetail = computed(
  () => returnStore.orderReturnDetail?.activeOrderItemProfiles
);
const dataListProductDiary = computed(() => orderStore.dataListProductDiary);
</script>
