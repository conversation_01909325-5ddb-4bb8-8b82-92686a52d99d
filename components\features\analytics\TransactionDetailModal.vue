<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click.self="$emit('close')"
  >
    <!-- Modal Container - Card Style -->
    <div
      class="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[95vh] overflow-hidden"
    >
      <!-- Header with Gradient -->
      <div class="bg-primary text-white p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div
              class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold">Chi tiết giao dịch</h2>
            </div>
          </div>
          <button
            @click="$emit('close')"
            class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Content with Cards -->
      <div class="overflow-y-auto max-h-[calc(95vh-150px)]">
        <div class="p-6 space-y-4">
          <!-- Hero Summary Card -->
          <div
            class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
          >
            <div class="p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3 mb-2">
                  <span class="text-sm font-medium text-gray-600">
                    Số tiền giao dịch
                  </span>
                </div>
                <div
                  :class="
                    getTransactionStatusClass(
                      transaction?.status || TransactionStatus.PENDING
                    )
                  "
                >
                  {{
                    getTransactionStatusText(
                      transaction?.status || TransactionStatus.PENDING
                    )
                  }}
                </div>
              </div>
              <div class="flex items-center justify-between">
                <p class="text-3xl font-bold text-green-500">
                  {{ formatCurrency(transaction?.amount || 0) }}
                </p>
                <div class="text-sm text-gray-600">
                  {{ transaction?.timeTransaction }}
                </div>
              </div>
            </div>
          </div>

          <!-- Info Cards Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Transaction Info Card -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 p-4"
            >
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">
                  Thông tin giao dịch
                </h3>
              </div>
              <div class="space-y-4">
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Mã giao dịch</span>
                  <span class="text-sm font-mono font-medium">{{
                    transaction?.transactionId
                  }}</span>
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Mã tham chiếu</span>
                  <span class="text-sm font-mono font-medium">{{
                    transaction?.extId
                  }}</span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-gray-600">Mục đích</span>
                  <span class="text-sm font-medium">{{
                    getTransactionPurposeText(
                      transaction?.purpose || TransactionPurpose.OTHER
                    )
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Bank Info Card -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 p-6"
            >
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">
                  Thông tin ngân hàng
                </h3>
              </div>
              <div class="space-y-4">
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Ngân hàng</span>
                  <span class="text-sm font-medium">{{
                    getBankName(transaction?.bankCode || "")
                  }}</span>
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Số tài khoản</span>
                  <span class="text-sm font-mono font-medium">{{
                    transaction?.bankAccountNumber
                  }}</span>
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Cổng thanh toán</span>
                  <span class="text-sm font-medium">{{
                    transaction?.gateway
                  }}</span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-gray-600">Loại cổng</span>
                  <span class="text-sm font-medium">{{
                    getPaymentGatewayTypeText(
                      transaction?.paymentGatewayType ||
                        PaymentGatewayType.BANK_TRANSACTION
                    )
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Payment Info Card -->
            <div
              class="bg-white rounded-xl shadow-sm border border-gray-100 p-6"
            >
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">
                  Thông tin thanh toán
                </h3>
              </div>
              <div class="space-y-4">
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Mã thanh toán</span>
                  <span class="text-sm font-mono font-medium">{{
                    transaction?.paymentId
                  }}</span>
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Số tiền thanh toán</span>
                  <span class="text-sm font-semibold">{{
                    formatTransactionAmount(
                      transaction?.paymentAmount || 0,
                      transaction?.currencyCode || "VND"
                    )
                  }}</span>
                </div>
                <div
                  class="flex justify-between items-center py-2 border-b border-gray-100"
                >
                  <span class="text-sm text-gray-600">Trạng thái xác nhận</span>
                  <span
                    :class="
                      getPaymentConfirmStatusClass(
                        transaction?.paymentConfirmStatus ||
                          PaymentConfirmStatus.PENDING
                      )
                    "
                  >
                    {{
                      getPaymentConfirmStatusText(
                        transaction?.paymentConfirmStatus ||
                          PaymentConfirmStatus.PENDING
                      )
                    }}
                  </span>
                </div>
                <div class="flex justify-between items-center py-2">
                  <span class="text-sm text-gray-600">Đơn vị tiền tệ</span>
                  <span class="text-sm font-medium">{{
                    transaction?.currencyCode
                  }}</span>
                </div>
              </div>
            </div>

            <!-- Description Card -->
            <div
              v-if="transaction?.description"
              class="bg-white rounded-xl shadow-sm border border-gray-100 p-6"
            >
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-orange-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">
                  Mô tả giao dịch
                </h3>
              </div>
              <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-sm text-gray-900">
                  {{ transaction?.description }}
                </p>
              </div>
            </div>
          </div>

          <!-- Additional Info (if exists) -->
          <div
            v-if="
              transaction?.customAttributes &&
              Object.keys(transaction.customAttributes).length > 0
            "
            class="bg-white rounded-xl shadow-sm border border-gray-100 p-6"
          >
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900">
                Thuộc tính tùy chỉnh
              </h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="(value, key) in transaction?.customAttributes"
                :key="key"
                class="flex justify-between items-center py-2 border-b border-gray-100"
              >
                <span class="text-sm text-gray-600">{{ key }}</span>
                <span class="text-sm font-medium">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <!-- <div
        class="bg-white border-t border-gray-200 px-6 py-4 flex justify-between items-center"
      >
        <div class="text-sm text-gray-500">
          Cập nhật lần cuối:
          {{ formatTransactionDate(transaction?.timeTransaction) }}
        </div>
        <div class="flex gap-3">
          <button
            @click="$emit('close')"
            class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            Đóng
          </button>
          <button
            class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium"
          >
            Xuất PDF
          </button>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Transaction } from "~/types/Transaction";
import {
  TransactionStatus,
  PaymentConfirmStatus,
  PaymentGatewayType,
  TransactionPurpose,
} from "~/types/Transaction";
import {
  getTransactionStatusClass,
  getTransactionStatusText,
  getPaymentConfirmStatusClass,
  getPaymentConfirmStatusText,
  getPaymentGatewayTypeText,
  getTransactionPurposeText,
  formatTransactionAmount,
  formatTransactionDate,
  formatTransactionTime,
  getBankName,
} from "~/utils/transactionHelpers";

interface Props {
  isOpen: boolean;
  transaction: Transaction | null;
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  transaction: null,
});

const emit = defineEmits<{
  close: [];
}>();

// Close modal on Escape key
onMounted(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === "Escape" && props.isOpen) {
      emit("close");
    }
  };

  document.addEventListener("keydown", handleEscape);

  onUnmounted(() => {
    document.removeEventListener("keydown", handleEscape);
  });
});

// Prevent body scroll when modal is open
watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }
);

onUnmounted(() => {
  document.body.style.overflow = "";
});
</script>
