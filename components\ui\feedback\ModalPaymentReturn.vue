<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
      @click.stop
    >
      <!-- Header -->
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">Thông báo</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>

      <!-- Nội dung -->
      <div class="max-h-[350px] overflow-y-auto">
        Trình duyệt hiện tại không hỗ trợ tải ảnh, vui lòng mở bằng trình duyệt
        ngoài.
        <br />
        <!-- Hiển thị nếu trong Zalo WebView -->

        <div v-if="isAndroid" class="mt-2">
          <p>
            Bạn đang sử dụng trình duyệt không hỗ trợ tốt. Để có trải nghiệm tốt
            hơn, vui lòng
            <a :href="chromeLink" class="text-primary underline">
              mở trong Chrome </a
            >.
          </p>
        </div>
        <div v-else class="mt-2">
          <p>
            Bạn đang mở trang này trong Zalo. Để có trải nghiệm tốt nhất, vui
            lòng
            <a
              :href="`x-safari-${curDomain}`"
              target="_blank"
              class="text-primary underline"
            >
              mở safari </a
            >.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const isZaloApp = ref(false);
const curDomain = ref("");

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const isAndroid = ref(false);
const isChrome = ref(false);
const chromeLink = ref("");

onMounted(() => {
  // https://sale.dev.longvan.vn/
  curDomain.value = window.location.href;

  const userAgent = navigator.userAgent || navigator.vendor;
  isZaloApp.value = /Zalo/i.test(userAgent);

  isAndroid.value = /Android/i.test(userAgent);
  isChrome.value =
    /Chrome/i.test(userAgent) && !/Edge|OPR|SamsungBrowser/i.test(userAgent);

  // Cách mở Chrome trên Android
  if (isAndroid.value) {
    chromeLink.value = `intent://${curDomain.value.replace(
      /^https?:\/\//,
      ""
    )}#Intent;scheme=https;package=com.android.chrome;end;`;
  }
});
</script>
