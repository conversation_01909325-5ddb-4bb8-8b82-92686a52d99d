<template>
  <div class="h-full flex flex-col">
    <!-- Table Header (Fixed) -->
    <div
      class="flex-shrink-0 bg-white border-b border-gray-200 sticky top-0 z-10"
    >
      <div
        class="grid grid-cols-11 gap-2 px-4 py-3 text-sm font-medium text-gray-700"
      >
        <div class="col-span-2">Mã giao dịch</div>
        <div class="col-span-2">PTTT</div>
        <div class="col-span-2">Số tiền</div>
        <div class="col-span-2">Tr<PERSON>ng thái</div>
        <div class="col-span-2">Th<PERSON>i gian</div>
        <div class="col-span-1">Chi tiết</div>
      </div>
    </div>

    <!-- Table Body (Scrollable) -->
    <div
      class="flex-1 overflow-y-auto"
      style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
    >
      <div class="divide-y divide-gray-200 bg-white">
        <div
          v-for="transaction in transactions"
          :key="transaction?.id"
          class="grid grid-cols-11 gap-2 px-4 py-3 hover:bg-gray-50 transition-colors"
        >
          <!-- Transaction ID -->
          <div class="col-span-2 flex items-center">
            <div class="min-w-0">
              <div class="text-sm font-medium text-gray-900 truncate">
                {{ transaction.transactionId }}
              </div>
              <div class="text-xs text-gray-500 truncate">
                {{ transaction.extId }}
              </div>
              <div
                class="text-xs text-gray-500 truncate"
                v-if="transaction.description"
              >
                {{ transaction.description }}
              </div>
            </div>
          </div>

          <!-- Bank Info -->
          <div class="col-span-2 flex items-center">
            <div class="min-w-0">
              <div class="text-sm font-medium text-gray-900">
                {{ getBankName(transaction.bankCode) }}
              </div>
              <div class="text-xs text-gray-500 truncate">
                {{ maskAccountNumber(transaction.bankAccountNumber) }}
              </div>
              <div class="text-xs text-gray-500">
                {{ transaction.gateway }}
              </div>
            </div>
          </div>

          <!-- Amount -->
          <div class="col-span-2 flex items-center">
            <div>
              <div
                class="text-sm font-semibold"
                :class="
                  transaction.type === 'income'
                    ? 'text-green-600'
                    : 'text-green-600'
                "
              >
                {{ transaction.type === "income" ? "+" : "+"
                }}{{
                  formatTransactionAmount(
                    transaction.amount,
                    transaction.currencyCode
                  )
                }}
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="col-span-2 flex items-center">
            <div class="space-y-1">
              <span :class="getTransactionStatusClass(transaction.status)">
                {{ getTransactionStatusText(transaction.status) }}
              </span>
            </div>
          </div>

          <!-- Time -->
          <div class="col-span-2 flex items-center">
            <div>
              <div class="text-sm text-gray-900">
                {{ transaction.timeTransaction }}
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="col-span-1 flex items-center">
            <div class="flex items-center gap-1">
              <button
                @click="$emit('view-detail', transaction)"
                class="p-1 text-gray-400 hover:text-primary rounded transition-colors"
                v-tippy="'Xem chi tiết'"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div
        v-if="!transactions.length"
        class="flex items-center justify-center h-full min-h-[400px]"
      >
        <div class="text-center py-12">
          <svg
            class="w-12 h-12 text-gray-400 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Chưa có giao dịch nào
          </h3>
          <p class="text-gray-500">
            Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn
          </p>
        </div>
      </div>
    </div>
    <!-- Pagination (Fixed at bottom) -->
    <div
      v-if="transactions.length"
      class="flex-shrink-0 bg-white border-t border-gray-200 px-4 py-3"
      style="min-height: 60px"
    >
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Hiển thị <span class="font-medium">{{ startItem }}</span> đến
          <span class="font-medium">{{ endItem }}</span> trong tổng số
          <span class="font-medium">{{ totalItems }}</span> giao dịch
        </div>
        <div class="flex items-center gap-2">
          <button
            @click="$emit('page-change', currentPage - 1)"
            :disabled="currentPage <= 1"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
          >
            Trước
          </button>
          <span class="px-3 py-2 text-sm">
            Trang {{ currentPage }} / {{ totalPages }}
          </span>
          <button
            @click="$emit('page-change', currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white"
          >
            Sau
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Transaction } from "~/types/Transaction";
import {
  getTransactionStatusClass,
  getTransactionStatusText,
  getPaymentConfirmStatusClass,
  getPaymentConfirmStatusText,
  formatTransactionAmount,
  formatTransactionDate,
  formatTransactionTime,
  getBankName,
  maskAccountNumber,
} from "~/utils/transactionHelpers";

interface Props {
  transactions: Transaction[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}
const props = defineProps([
  "transactions",
  "currentPage",
  "totalPages",
  "totalItems",
  "itemsPerPage",
]);

const emit = defineEmits<{
  "view-detail": [transaction: Transaction];
  "page-change": [page: number];
}>();

const startItem = computed(
  () => (props.currentPage - 1) * props.itemsPerPage + 1
);
const endItem = computed(() =>
  Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
);
</script>

<style scoped>
/* Custom scrollbar styles for webkit browsers */
.flex-1.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
