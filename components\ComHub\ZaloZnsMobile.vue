<template>
  <div class="py-1 flex gap-2 items-center justify-end">
    <!-- :class="diary?.status !== 'COMPLETED' ? ' disabled ' : ''" -->

    <div
      v-if="diary?.status === 'COMPLETED'"
      @click="handleNavigate"
      class="border rounded text-primary border-primary px-3 py-[10px]"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M8.25 9.75h4.875a2.625 2.625 0 0 1 0 5.25H12M8.25 9.75 10.5 7.5M8.25 9.75 10.5 12m9-7.243V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185Z"
        />
      </svg>
    </div>
    <div
      @click="handlePayment"
      class="border border-primary rounded text-primary px-3 py-[10px]"
    >
      <!-- Thanh toán -->
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
        />
      </svg>
    </div>
    <!-- in hóa đơn -->
    <div
      v-if="!isButtonSendMessage"
      @click="handlePrintOrder"
      class="border border-primary rounded text-primary px-3 py-2 flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
        />
      </svg>
      <span class="ml-1">{{
        getPrintCount(diary?.order?.customAttributes)
      }}</span>
    </div>
    <div
      v-if="diary?.status !== 'CANCELLED' && diary?.status !== 'DRAFT'"
      @click="handleOpenZnsPopUp('SEND_ORDER_RATE')"
      class="bg-white rounded text-primary border border-primary px-3 py-[10px]"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"
        />
      </svg>
    </div>
    <div
      v-if="diary?.status !== 'CANCELLED' && diary?.status !== 'DRAFT'"
      @click="handleOpenZnsPopUp('SEND_ORDER_INFO')"
      class="border border-primary w-[37px] h-[37px] rounded"
    >
      <img src="~/assets/images/zalo.jpg" alt="" class="p-1" loading="lazy" />
    </div>
    <!-- <div
      v-if="diary?.status !== 'CANCELLED' && diary?.status !== 'DRAFT'"
      class="bg-white rounded text-primary border border-primary px-3 py-[10px]"
      v-tippy="'Sử lí FFM đơn hàng'"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
        />
      </svg>
    </div> -->
    <div
      v-if="diary?.remainTotal === 0 && settingOrg?.isExportInvoice"
      class="bg-white rounded text-primary border border-primary px-3 py-[10px]"
      @click="toogleExportInvoice"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="size-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
        />
      </svg>
    </div>
  </div>
  <SendVoucher
    v-if="isOpenTopicPopup"
    @cancel="handleToogleMessagePopup"
    @confirm="handleSendMessage"
  ></SendVoucher>
  <ZaloZns
    v-if="isOpenZnsPopup"
    @cancel="handleOpenZnsPopUp"
    @confirm="handleSendZns"
    :orderDetail="diary"
    :typeSendZns="typeSendZns"
  ></ZaloZns>
  <ConfirmDialog
    v-if="isAlert"
    :title="'Thông báo'"
    :message="'Đơn đang ở trạng thái nháp bạn có muốn thanh toán'"
    @confirm="confirm"
    @cancel="cancel"
  />
  <CancelOrderPopup
    v-if="isCancelOrderPopup"
    :order="diary"
    :title="'Hủy đơn hàng'"
    :text="`Bạn đang hủy đơn hàng ${diary?.id} vui lòng chọn lý do`"
    :reasonText="'Lý do hủy đơn'"
    :dataReason="dataReason"
    @cancel="toogleCancelOrder"
    @confirm="handleConfirmCancelOrder"
  >
  </CancelOrderPopup>
  <div v-if="isLoading">
    <LoadingSpinner />
  </div>
  <ExportInvoicePopup
    v-if="isOpenExportInvoice"
    :order="diary"
    @confirm="toogleExportInvoice"
    @cancel="toogleExportInvoice"
  ></ExportInvoicePopup>
  <WaringWareHouse
    v-if="isAlertWareHouse"
    @cancel="isAlertWareHouse = false"
  ></WaringWareHouse>
</template>
<script setup lang="ts">
const WaringWareHouse = defineAsyncComponent(
  () => import("~/components/dialog/WaringWareHouse.vue")
);
const route = useRoute();
const ordersStore = useOrdersStore();
const props = defineProps([
  "diary",
  "isButtonSendMessage",
  "isNotDraft",
  "data",
]);
const isOpenTopicPopup = ref(false);
const isAlertWareHouse = ref(false);
const handleToogleMessagePopup = () => {
  if (props.diary?.order?.ownerPartyId) {
    isOpenTopicPopup.value = !isOpenTopicPopup.value;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const { createTopic } = useCustomer();

const handleCreateTopic = async (message: string) => {
  if (props.diary?.order?.ownerPartyId) {
    await createTopic(
      "674fcd11b1538b122be026d0",
      props.diary?.order?.ownerPartyId,
      message
    );
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
  isOpenTopicPopup.value = false;
};
// send message
const { sendMessage, shareOrder } = useComhub();
const handleSendMessage = async (message: string) => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  const zaloId = appId.find((app: any) => app.name === "ZNS");
  if (!zaloId) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  if (!props.diary?.order?.ownerPartyId) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    isOpenTopicPopup.value = false;
    return;
  }

  try {
    await sendMessage(zaloId?.id, message, "TEXT", "SYSTEM", [
      props.diary?.order?.ownerPartyId,
    ]);
  } catch (error) {
    console.error("Error sending message:", error);
  }

  isOpenTopicPopup.value = false;
};
const isOpenZnsPopup = ref(false);
const isLoading = ref(false);
const typeSendZns = ref();

const handleOpenZnsPopUp = (type: string) => {
  if (props.diary?.order?.ownerPartyId) {
    // ordersStore.tooltip = null;
    isOpenZnsPopup.value = !isOpenZnsPopup.value;
    typeSendZns.value = type;
  } else {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
  }
};
const handleSendZns = async (data: any) => {
  try {
    isLoading.value = true;

    await shareOrder(
      data?.templateData,
      data?.app?.apps[0]?.id,
      data?.templateType
    );
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;

    throw error;
  }
  isOpenZnsPopup.value = false;
};
//
const diaryStore = useDiariesStore();
const { printOrderHTML, cancelOrder } = useOrder();
import printJS from "print-js";
import type { Auth } from "~/types/Auth";

const handlePrintOrder = async () => {
  const url = useRequestURL();

  const baseUrl = `${url.origin}/thanh-toan?orderId=${
    props.diary?.id
  }&orgId=${url.searchParams.get("orgId")}&storeId=${url.searchParams.get(
    "storeId"
  )}`;
  try {
    const response = await printOrderHTML(
      props.diary?.id,
      "Chưa thanh toán",
      baseUrl
    );

    const data = response.data;

    printJS({
      printable: data,
      type: "raw-html",
      scanStyles: false,
      style: `
        @page { margin: 0; } /* Xóa margin mặc định của trang in */
        body { margin: 0; } /* Đảm bảo body không có margin thừa */
      `,
    });

    if (props.isNotDraft) {
      ordersStore.updateQuantityPrintOrder(props?.diary);
    } else {
      diaryStore.updateQuantityPrintOrder(props?.diary);
    }
  } catch (error) {
    console.error("Error printing the order:", error);
  } finally {
    isLoading.value = false;
  }
};
// Import utilities
import { getPrintCount } from "~/utils/orderHelpers";
const isAlert = ref(false);
const handlePayment = async () => {
  const res = await handleCheckWarehouse();
  if (!res) {
    const dataSettingOrg = props.data as any;
    const arrRoles = dataSettingOrg?.find(
      (org: any) => org?.storeId === orgId.value
    );
    if (arrRoles?.rolesExportInvoice?.length) {
      const arrRolesDefault = ["SALE_ADMIN", "SALE_MANAGER"];
      const isRoleSaleAdmin = auth.value?.user?.roles?.filter((role: any) =>
        arrRolesDefault?.includes(role)
      );
      if (!isRoleSaleAdmin?.length) {
        isAlertWareHouse.value = true;
        return;
      }
    }
  }
  // /payment?orderId=${dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}
  if (props.diary?.status === "DRAFT") {
    console.log("đang là đơn nháp");
    isAlert.value = true;
    //
    return;
  }
  if (props.diary?.remainTotal > 0) {
    navigateTo(
      `/payment?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  } else {
    useNuxtApp().$toast.warning(
      "Không thể thanh toán,số tiền cần thanh toán là 0"
    );
  }
};
const cancel = () => {
  isAlert.value = false;
};
const { fetchOrderDetails, updateStatusApproved } = useOrder();

const confirm = async () => {
  if (props.diary?.remainTotal > 0) {
    isLoading.value = true;
    await updateStatusApproved(props.diary?.id);
    navigateTo(
      `/payment?orderId=${props.diary?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    isLoading.value = false;
  } else {
    useNuxtApp().$toast.warning(
      "Không thể thanh toán,số tiền cần thanh toán là 0"
    );
  }
};
//
const isCancelOrderPopup = ref();
const toogleCancelOrder = () => {
  isCancelOrderPopup.value = !isCancelOrderPopup.value;
};
const handleConfirmCancelOrder = async (reason: string) => {
  try {
    const auth = useCookie("auth").value as unknown as Auth;
    const data = {
      reason: "CUSTOMER",
      updatedBy: auth?.user?.id,
      note: reason,
      orderType: "SALES",
    };
    const response = await cancelOrder(props.diary?.id, data);
    useNuxtApp().$toast.success("Hủy đơn hàng thành công");
    if (props.isNotDraft) {
      ordersStore.upDateCancelOrderStatus(props?.diary, reason);
    } else {
      diaryStore.upDateCancelOrderStatus(props?.diary, reason);
    }
  } catch (error) {
    throw error;
  } finally {
    ordersStore.tooltip = null;
    toogleCancelOrder();
  }
};
const handleNavigate = async () => {
  navigateTo(
    `/order/return?orderReturnId=${props.diary?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};
const dataReason = [
  { name: "Chọn lý do hủy" },
  {
    name: "Khách hàng yêu cầu",
  },
  {
    name: "Thông tin chưa hợp lệ",
  },
  {
    name: "Không đủ hàng trong kho",
  },
  {
    name: "Không thanh toán đơn hàng",
  },
  {
    name: "Khác",
  },
];
const isOpenExportInvoice = ref(false);
const auth = useCookie("auth") as any;

// Use tab-isolated context instead of cookies
const { orgId } = useTabContext();

const settingOrg = computed(() => {
  const dataSettingOrg = props.data as any;
  return dataSettingOrg?.find((org: any) => org?.storeId === orgId.value);
});
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
const { getInventoryV2 } = useWarehouse();

const handleCheckWarehouse = async () => {
  const dataSettingOrg = props.data as any;
  const result = dataSettingOrg?.find(
    (org: any) => org?.storeId === orgId.value
  );
  if (result?.isExportInvoiceForProduct) {
    const data = <any>[];
    props.diary?.activeOrderItemProfiles?.map(async (item: any) => {
      const sku = item.orderLineItem.variant.sku;
      const test = {
        productId: item?.orderLineItem.variant?.product?.id,
        variantId:
          item?.orderLineItem?.variant?.id ===
          item.orderLineItem.variant?.product?.id
            ? ""
            : item.orderLineItem.variant?.id,
        sku: item.orderLineItem.variant?.sku,
      };
      data.push(test);
    });
    const res = await getInventoryV2(
      props.diary?.order?.customAttribute?.facilityId,
      data
    );

    if (res?.length) {
      for (const item of res) {
        if (item?.orderAble < 5) {
          return false;
        }
      }
    }
    return true;
  } else {
    return true;
  }
};
</script>
