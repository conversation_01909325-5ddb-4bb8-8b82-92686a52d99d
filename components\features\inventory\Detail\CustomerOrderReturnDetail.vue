<template>
  <div>
    <div class="px-2 py-1 bg-white rounded">
      <span class="text-md font-bold text-primary"><PERSON><PERSON><PERSON><PERSON> h<PERSON>ng</span>
      <div class="p-3 mt-1 border flex flex-col relative bg-primary/10">
        <div
          class="flex justify-end cursor-pointer absolute top-[5px] right-[5px]"
        ></div>

        <div class="text-sm space-y-1.5">
          <div class="flex items-center gap-2">
            <div class="space-y-1.5">
              <div class="flex gap-2 items-center">
                <span
                  ><svg
                    class="w-[20px]"
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="currentColor"
                      d="M10 14q-1.25 0-2.125-.875T7 11q0-.55.175-1.037t.525-.888q-.1-.25-.15-.525T7.5 8q0-.95.513-1.687T9.35 5.225q.5-.575 1.175-.9T12 4q.8 0 1.475.325t1.175.9q.825.35 1.338 1.088T16.5 8q0 .275-.05.55t-.15.525q.35.4.525.888T17 11q0 1.25-.875 2.125T14 14zm-6 8v-2.8q0-.85.438-1.562T5.6 16.55q1.55-.775 3.15-1.162T12 15q1.65 0 3.25.388t3.15 1.162q.725.375 1.163 1.088T20 19.2V22z"
                    ></path>
                  </svg>
                </span>
                <div>
                  <span class="capitalize">{{
                    orderDetail?.order?.ownerName
                  }}</span>
                </div>
                <div>
                  | <span class="">{{ orderDetail?.order?.ownerPhone }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["orderDetail"]);
</script>
