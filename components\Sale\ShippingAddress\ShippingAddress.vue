<template>
  <div class="shipping-address-card group">
    <!-- Address Card -->
    <div
      class="relative bg-white border border-gray-200 rounded-xl p-4 transition-all duration-200 hover:border-primary hover:shadow-md cursor-pointer"
      :class="{
        'border-primary shadow-md ring-2 ring-primary/20':
          shippingAddress?.address_default,
        'hover:bg-gray-50': !shippingAddress?.address_default,
      }"
      @click="handleDefaultAddress()"
    >
      <!-- Default Badge -->
      <div
        v-if="shippingAddress?.address_default"
        class="absolute -top-2 -right-2 bg-primary text-white text-xs px-3 py-1 rounded-full font-semibold  z-10"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3 w-3 inline mr-1"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        Mặc định
      </div>

      <!-- Content -->
      <div class="flex items-start justify-between">
        <!-- Address Information -->
        <div class="flex-1 space-y-3">
          <!-- Contact Info -->
          <div
            class="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0"
          >
            <div class="flex items-center space-x-2">
              <div
                class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <div>
                <p class="font-semibold text-gray-800">
                  {{ shippingAddress.name }}
                </p>
                <p class="text-sm text-gray-600">{{ shippingAddress.phone }}</p>
              </div>
            </div>
          </div>

          <!-- Address Details -->
          <div class="flex items-start space-x-2">
            <div
              class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mt-0.5"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-gray-800 font-medium">
                {{ shippingAddress.address }}
              </p>
              <p class="text-sm text-gray-600 mt-1">
                {{ fullAddress }}
              </p>
            </div>
          </div>

          <!-- Set as Default Hint -->
          <div
            v-if="!shippingAddress?.address_default"
            class="text-xs text-gray-500 italic"
          >
            Nhấp để đặt làm địa chỉ mặc định
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col space-y-2 ml-4">
          <!-- Edit Button -->
          <button
            @click.stop="handleEdit()"
            class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200 group/edit"
            title="Chỉnh sửa địa chỉ"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 group-hover/edit:scale-110 transition-transform duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  <ModalEditShippingAddress
    v-if="isShowEdit"
    :shippingAddress="shippingAddress"
    @closeModalEditShippingAddress="closeModalEditShippingAddress"
    :customer="customer"
    :isPageSale="isPageSale"
  ></ModalEditShippingAddress>
  <div>
    <ConfirmDialog
      v-if="isDialog"
      :title="'Xác nhận Địa chỉ mặc định'"
      :message="'Bạn có muốn làm địa chỉ mặc định?'"
      @confirm="confirmSetDefault"
      @cancel="cancelSetDefault"
    />
  </div>
</template>

<script setup lang="ts">
import type { Auth } from "~/types/Auth";

// Props
const props = defineProps(["shippingAddress", "customer", "isPageSale"]);

// Composables
const orderStore = useOrderStore();
const customerStore = useCustomerStore();
const { updateOrderCustomer } = useOrder();

// Reactive Data
const isShowEdit = ref<Boolean>(false);
const isDialog = ref<Boolean>(false);

// Computed Properties
const fullAddress = computed(() => {
  const { ward, district, province, ward_name, district_name, province_name } =
    props.shippingAddress;

  // Use name fields if available, otherwise use code fields
  const wardText = ward_name || ward || "";
  const districtText = district_name || district || "";
  const provinceText = province_name || province || "";

  return [wardText, districtText, provinceText].filter(Boolean).join(", ");
});
const confirmSetDefault = async () => {
  const auth = useCookie("auth").value as unknown as Auth;
  const data = {
    name: props.shippingAddress.name,
    phone: props.shippingAddress.phone,
    address: props.shippingAddress.address,
    province_code: props.shippingAddress.province_code,
    district_code: props.shippingAddress.district_code,
    ward_code: props.shippingAddress.ward_code,
    address_default: true,
  };
  await orderStore.updateShippingAddress(
    props.customer.id,
    props.shippingAddress.id,
    auth.user.id,
    data
  );
  // Update customer store
  customerStore.handleUpdateShippingAddressDefault(props.shippingAddress?.id);
  if (props.isPageSale) {
    await updateOrderCustomer(
      orderStore?.orderDetail?.id,
      props.customer?.id,
      props.shippingAddress?.id
    );
    await orderStore.updateOrder(orderStore?.orderDetail?.id);
  }
  isDialog.value = false;
};

const cancelSetDefault = () => {
  isDialog.value = false;
};

const handleEdit = () => {
  isShowEdit.value = true;
};

const closeModalEditShippingAddress = (value: boolean) => {
  isShowEdit.value = value;
};

const handleDefaultAddress = () => {
  if (!props.shippingAddress?.address_default) {
    isDialog.value = true;
  }
};
</script>

<style scoped>
/* Card hover effects */
.shipping-address-card .group:hover {
  transform: translateY(-1px);
}

/* Default address styling */
.shipping-address-card .border-primary {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 51, 234, 0.05) 100%
  );
}

/* Button hover effects */
.group\/edit:hover svg,
.group\/delete:hover svg {
  transform: scale(1.1);
}

/* Smooth transitions */
.shipping-address-card * {
  transition: all 0.2s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .shipping-address-card .flex-col {
    flex-direction: row;
    gap: 0.5rem;
  }
}
</style>
