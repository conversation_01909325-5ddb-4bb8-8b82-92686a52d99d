<template>
  <TableLoading
    :item-count="itemCount"
    :columns="transactionColumns"
    :grid-class="gridClass"
    :table-row-class="tableRowClass"
    :show-mobile="showMobile"
    :show-desktop="showDesktop"
  />
</template>

<script setup lang="ts">
import TableLoading from './TableLoading.vue';

interface Props {
  itemCount?: number;
  showMobile?: boolean;
  showDesktop?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  itemCount: 5,
  showMobile: true,
  showDesktop: true,
});

// Transaction-specific configuration
const transactionColumns = [
  {
    class: 'col-span-2',
    width: 'w-3/4',
    hasSecondary: true,
    secondaryWidth: 'w-1/2',
    hasTertiary: true,
    tertiaryWidth: 'w-2/3',
  },
  {
    class: 'col-span-2',
    width: 'w-2/3',
    hasSecondary: true,
    secondaryWidth: 'w-1/3',
    hasTertiary: true,
    tertiaryWidth: 'w-1/2',
  },
  {
    class: 'col-span-2',
    width: 'w-1/2',
  },
  {
    class: 'col-span-2',
    width: 'w-3/5',
  },
  {
    class: 'col-span-2',
    width: 'w-4/5',
  },
  {
    class: 'col-span-1',
    width: 'w-8',
  },
];

const gridClass = 'grid grid-cols-11 gap-2';
const tableRowClass = 'hover:bg-gray-50 transition-colors';
</script>
