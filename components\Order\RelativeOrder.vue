<template>
  <div class="pb-1 mx-2">
    <div class="text-primary font-semibold text-sm"><PERSON><PERSON> sách đơn liên quan</div>

    <div class="flex flex-wrap items-center gap-x-2 gap-y-1">
      <span
        v-for="order in orderRelate"
        :key="order?.order_id"
        class="font-semibold cursor-pointer underline"
        @click="handleNavigate(order)"
      >
        {{ order?.order_id }}
        <span v-if="order?.type === 'POS_SALE'">{{ `(Đơn gốc)` }}</span>
        <span v-if="order?.type === 'POS_RETURN'">{{ `(Đơn trả)` }}</span>
      </span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["orderRelate"]);
const route = useRoute();
const handleNavigate = (order) => {
  console.log("đơn gốc", order);
  if (order?.type === "POS_SALE") {
    navigateTo(
      `/sale?orderId=${order?.order_id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    return;
  }
  if (order?.type === "POS_RETURN") {
    navigateTo(
      `/order/return/detail?orderId=${order?.order_id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
    return;
  }
};
</script>
