<template>
  <div class="col-span-3 bg-white rounded p-2 border-b">
    <h2 class=" font-bold text-primary">Thông tin hoàn tiền</h2>
    <div class="">
      <div class="flex items-center justify-between">
        <span class="">Tiền hàng: </span>
        <span class="font-semibold text-primary">
          {{ formatCurrency(orderDetail?.order?.subtotalPrice?.amount) }}
        </span>
      </div>
      <div class="flex items-center justify-between">
        <span class="">Phí trả hàng: </span>
        <span class="font-semibold text-red-600">
          {{ formatCurrency(orderDetail?.order?.feeReturn.amount || 0) }}
        </span>
      </div>
      <div class="flex items-center justify-between">
        <span class="">Tổng trả lại: </span>
        <span class="text-primary font-semibold">
          {{ formatCurrency(orderDetail?.order?.currentTotalPrice?.amount) }}
        </span>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps(["orderDetail"]);
</script>
