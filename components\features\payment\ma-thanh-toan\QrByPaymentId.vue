<template>
  <div class="bg-white rounded flex flex-col md:h-[65svh]">
    <div class="md:mx-10 mx-2 mt-4">
      <div
        class="p-3 flex gap-2 items-center cursor-pointer rounded bg-secondary-light"
      >
        <img
          class="w-8 h-8 object-contain mr-2 md:w-6 md:h-6 rounded"
          src="https://placehold.co/20"
          alt=""
          loading="lazy"
        />
        <span
          v-if="
            selectedPaymentMethod?.description === 'casso' ||
            dataQrCode?.methodCode === 'casso'
          "
          class="font-semibold text-base"
        >
          <PERSON><PERSON><PERSON><PERSON> kho<PERSON>n
        </span>
        <span v-else class="font-semibold text-base">{{
          selectedPaymentMethod?.description || dataQrCode?.methodCode
        }}</span>
      </div>
    </div>

    <!-- Thông báo hiển thị -->
    <div
      v-if="dataQrCode?.methodCode && dataQrCode?.methodCode !== 'casso'"
      class="mt-2 mx-10 bg-yellow-50 p-1 px-[50px] rounded"
    >
      <div
        v-if="route.query?.gatewayStatusCode === '1'"
        class="flex items-center space-x-2"
      >
        <span
          class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></span>
        <span>
          Hệ thống đang thực hiện giao dịch. Vui lòng đợi trong ít phút
        </span>
      </div>
      <div
        v-if="
          route.query?.gatewayStatusCode === '-1' ||
          (dataQrCode?.statusCode === '-1' &&
            route.query?.gatewayStatusCode !== '1')
        "
        class="flex items-center space-x-2"
      >
        <span>Thanh toán đã bị hủy, vui lòng tạo lại thanh toán mới</span>
      </div>
      <div
        v-if="!route.query?.gatewayStatusCode && dataQrCode?.statusCode === '1'"
        class="flex items-center space-x-2"
      >
        <span>Thanh toán chưa hoàn tất</span>
      </div>
    </div>

    <!-- Nội dung chính có flex-1 để đẩy nút xuống -->

    <!-- Nút quay lại sát mép dưới -->
    <div
      v-if="route.query?.gatewayStatusCode !== '1'"
      class="p-2 cursor-pointer flex items-center md:justify-end justify-center mt-auto gap-3 mx-2 md:mx-10"
    >
      <div @click="handleClickBack">
        <button
          class="bg-white text-primary border border-primary px-10 py-1 rounded"
        >
          Quay lại
        </button>
      </div>
      <!-- tiếp tục thanh toán -->
      <div
        v-if="
          dataQrCode?.methodCode &&
          dataQrCode?.methodCode !== 'casso' &&
          !route.query?.gatewayStatusCode &&
          dataQrCode?.statusCode === '1'
        "
        @click="handleOpenPaymentMethod"
      >
        <button class="bg-primary text-white px-5 py-1 rounded">
          Tiếp tục thanh toán
        </button>
      </div>
      <!-- thanh toán lại -->
      <div
        v-if="
          dataQrCode?.methodCode &&
          dataQrCode?.methodCode !== 'casso' &&
          dataQrCode?.statusCode === '-1'
        "
        @click="handleCreatePayment"
      >
        <button class="bg-primary text-white px-5 py-1 rounded">
          Thanh toán lại
        </button>
      </div>
    </div>
    <!-- 00 -->
    <div class=" ">
      <div v-if="dataQrCode" class="">
        <div
          v-if="dataQrCode?.methodCode && dataQrCode?.methodCode !== 'casso'"
        ></div>

        <div v-else>
          <div class="block md:hidden">
            <div class="">
              <InfoBankPaymentId
                :dataQrCode="dataQrCode"
                :dataPayment="dataPayment"
              ></InfoBankPaymentId>
            </div>
          </div>
          <div class="md:block hidden">
            <div class="flex items-center justify-center">
              <img
                :src="dataQrCode?.qrCodeUrl || dataQrCode?.qrCode"
                alt="QR Code"
                class="md:w-[35%] md:h-[35%]"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps([
  "dataQrCode",
  "selectedPaymentMethod",
  "dataPayment",
]);
const emits = defineEmits(["clickBack", "createPaymentAgain"]);
const route = useRoute();
const handleClickBack = () => {
  emits("clickBack");
};
const handleOpenPaymentMethod = () => {
  window.location.href = props.dataQrCode?.qrCode;
};
const handleCreatePayment = () => {
  emits("createPaymentAgain", props.dataQrCode);
};
</script>
