<template>
  <div class="bg-gray-50 min-h-screen mx-2">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="px-2 py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-semibold text-gray-900">Chấm công</h1>
          <button
            @click="handleCreateCheckin"
            class="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              class="w-5 h-5"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
              />
            </svg>
            Chấm công
          </button>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="py-4">
      <div v-if="loading" class="flex justify-center items-center py-20">
        <LoadingSpinner />
      </div>

      <div v-else class="max-w-4xl mx-auto">
        <!-- Time Keeping List -->
        <div class="bg-white rounded-lg border border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">
              Lịch sử chấm công
            </h2>
          </div>

          <div ref="scrollContainer" class="max-h-[70vh] overflow-y-auto p-4">
            <CardTimeKeeping
              v-if="!isManager"
              :dataCheckin="timeKeepingStore.dataTimeKeeping"
            />
            <CardTimeKeepingManager
              v-else
              :dataCheckin="timeKeepingStore.dataTimeKeeping"
            />

            <!-- Loading more indicator -->
            <div
              v-if="
                loadingMore ||
                (hasMore && timeKeepingStore.dataTimeKeeping.length > 0)
              "
              class="flex justify-center py-4"
            >
              <div class="flex items-center gap-2 text-gray-500">
                <svg
                  class="animate-spin w-4 h-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span class="text-sm">{{
                  loadingMore ? "Đang tải thêm..." : "Cuộn để tải thêm"
                }}</span>
              </div>
            </div>

            <!-- End of list indicator -->
            <div
              v-if="!hasMore && timeKeepingStore.dataTimeKeeping.length > 0"
              class="text-center py-4"
            >
              <p class="text-sm text-gray-500">Đã hiển thị tất cả dữ liệu</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal -->
    <ModalTimeKeeping v-if="isCloseModel" @isClose="isClose" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { Auth } from "~/types/Auth";

useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chấm công",
});
const scrollContainer = ref(null);
const loading = ref(false);
const loadingMore = ref(false);
const isCloseModel = ref(false);

const timeKeepingStore = useTimeKeepingStore();
const route = useRoute();

const { getListWorkEfforts, getListWorkEffortsV1 } = useTimeKeeping();

const handleCreateCheckin = () => {
  isCloseModel.value = true;
};

const isClose = (value: boolean) => {
  isCloseModel.value = value;
};

const auth = useCookie("auth").value as unknown as Auth;

// Stats computed properties
const todayCount = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return timeKeepingStore.dataTimeKeeping.filter((item: any) => {
    const itemDate = new Date(item.createdStamp);
    return itemDate >= today && itemDate < tomorrow;
  }).length;
});

const weekCount = computed(() => {
  const today = new Date();
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + 1);
  startOfWeek.setHours(0, 0, 0, 0);

  return timeKeepingStore.dataTimeKeeping.filter((item: any) => {
    const itemDate = new Date(item.createdStamp);
    return itemDate >= startOfWeek;
  }).length;
});

const totalCount = computed(() => {
  return timeKeepingStore.dataTimeKeeping.length;
});

const hasMore = ref(true);
const options = reactive({
  performerId: auth.user.id,
  workEffortTypeId: "",
  source: "TIMEKEEPING",
  pageNumber: 1,
  pageSize: 5,
  attributes: {
    storeId: route.query.storeId as string,
  },
});
const getData = async () => {
  try {
    if (!hasMore.value || loadingMore.value) return;

    loadingMore.value = true;
    console.log("Loading page:", options.pageNumber);

    const response = await getListWorkEffortsV1(options);

    if (response.data && response.data.length > 0) {
      response.data.forEach((element: any) => {
        timeKeepingStore.addTimeKeping(element);
      });
      options.pageNumber++;

      // Check if we got less data than requested (end of data)
      if (response.data.length < options.pageSize) {
        hasMore.value = false;
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error("Error loading more data:", error);
    hasMore.value = false;
  } finally {
    loadingMore.value = false;
  }
};
useInfiniteScroll(
  scrollContainer,
  async () => {
    if (hasMore.value && !loading.value && !loadingMore.value) {
      await getData();
    }
  },
  {
    distance: 50, // load more when 50px to the bottom
    throttle: 500, // throttle scroll events
  }
);
// check role
const isManager = ref<Boolean>();
const rolesToCheck = [
  "SALE_OP",
  "SUPP_ADMIN",
  "ORG_ADMIN",
  "SALE_MANAGER",
  "SUPP_OP",
];

const checkUserRoles = () => {
  if (auth?.user?.roles && Array.isArray(auth.user.roles)) {
    auth.user.roles.forEach((role) => {
      if (rolesToCheck.includes(role)) {
        isManager.value = true;
        console.log("Đăng nhập bằng role", role);
      }
    });
  } else {
    isManager.value = false;
    console.log("user login bằng role khác");
  }
};
// check role
onMounted(async () => {
  loading.value = true;
  checkUserRoles();

  // Reset data before loading
  timeKeepingStore.setDataTimeKeeping([]);
  options.pageNumber = 1;
  hasMore.value = true;

  if (isManager.value) {
    const attributes = {
      storeId: route.query.storeId as string,
    };
    await getListWorkEfforts(
      auth.user.id,
      "",
      "TIMEKEEPING",
      0,
      10,
      attributes
    );
  } else {
    const attributes = {
      storeId: route.query.storeId as string,
      createdBy: auth.user.id,
    };
    await getListWorkEfforts(
      auth.user.id,
      "",
      "TIMEKEEPING",
      0,
      10,
      attributes
    );
  }
  loading.value = false;
});
</script>
