<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <h2 class="text-lg font-bold mb-2 text-center">Thông báo</h2>
      <p class="mb-6">Topic đang mở bạn có muốn đóng hay trao đổi tiếp</p>
      <div class="flex w-full gap-2">
        <div
          @click="navigate"
          class="flex-1 px-2 py-1 text-primary border-primary border rounded text-center cursor-pointer"
        >
          Trao đổi tiếp
        </div>
        <div
          @click="closeTopic"
          class="flex-1 px-2 py-1 bg-primary text-white rounded text-center cursor-pointer"
        >
          Đóng topic
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";

const emit = defineEmits(["closeTopic", "navigate", "cancel"]);
const isVisible = ref(true);

const closeTopic = () => {
  emit("closeTopic");
  isVisible.value = false;
};

const navigate = () => {
  emit("navigate");
  isVisible.value = false;
};
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
</script>

<style scoped></style>
