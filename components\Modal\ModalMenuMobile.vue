<!-- components/Modal.vue -->
<template>
  <Transition name="fade">
    <div
      v-if="isOpen"
      class="fixed z-[999999999] inset-0 flex items-center justify-start bg-black bg-opacity-50"
      @click="close"
    >
      <Transition name="slide">
        <div
          v-if="isOpen"
          class="bg-white h-svh w-80 px-4 py-4 relative flex flex-col"
          @click.stop
        >
          <!-- Header -->
          <div class="flex justify-end">
            <button
              @click="close"
              class="text-lg font-bold text-gray-600 hover:text-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <!-- Menu -->
          <div class="flex-1 overflow-y-auto mt-4 max-h-[calc(100vh-200px)]">
            <div v-for="(item, index) in menuItems" :key="index" class="mb-2">
              <div
                class="flex items-center justify-between p-2 hover:bg-gray-100 rounded cursor-pointer"
                @click="
                  item.childItems
                    ? toggleChildItems(index)
                    : handleNavigate(item)
                "
              >
                <div class="flex items-center gap-4">
                  <span v-html="item.icon"></span>
                  <div class="text-lg font-semibold">{{ item.name }}</div>
                </div>
                <svg
                  v-if="item.childItems"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="w-5 h-5 transition-transform"
                  :class="{ 'rotate-180': expanded[index] }"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                  />
                </svg>
              </div>
              <Transition name="slide-child">
                <div
                  v-if="item.childItems"
                  v-show="expanded[index]"
                  class="ml-6 mt-1"
                >
                  <div
                    v-for="childItem in item.childItems"
                    :key="childItem.name"
                    class="mb-1"
                  >
                    <NuxtLink
                      class="text-md text-gray-700 hover:text-gray-900 flex items-center gap-2"
                      :to="childItem.to + `?orgId=${orgId}&storeId=${storeId}`"
                      @click="close"
                    >
                      <span v-html="childItem.icon"></span>
                      <span>{{ childItem.name }}</span>
                    </NuxtLink>
                  </div>
                </div>
              </Transition>
            </div>
          </div>

          <!-- Footer -->
          <div class="mt-4 border-t border-gray-200 pt-4">
            <div class="flex flex-col gap-2">
              <NuxtLink
                :to="`/org`"
                class="flex items-center gap-2 text-lg font-semibold text-gray-700 hover:text-gray-900"
                @click="close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6h1.5m-1.5 3h1.5m-1.5 3h1.5M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
                  />
                </svg>
                <span>{{ orgId }}</span>
              </NuxtLink>
              <NuxtLink
                :to="`/dashboard?orgId=${orgId}`"
                class="flex items-center gap-2 text-lg font-semibold text-gray-700 hover:text-gray-900"
                @click="close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                  />
                </svg>
                <span>Kênh bán hàng</span>
              </NuxtLink>
              <div
                @click="logout()"
                class="flex items-center gap-2 text-lg font-semibold text-red-600 hover:text-red-800 cursor-pointer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"
                  />
                </svg>
                <span>Đăng xuất</span>
              </div>
            </div>
          </div>
        </div>
      </Transition>
      <LoadingSpinner v-if="isLoading" />
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { menuItems } from "@/utils/dataMenuLeft";

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

// Use tab-isolated context instead of cookies
const { storeId, orgId } = useTabContext();
const emit = defineEmits(["close"]);
const { logout } = useAuth();
const isLoading = ref(false);
const expanded = ref<boolean[]>([]);

const close = () => {
  emit("close");
};

const handleNavigate = async (item: any) => {
  let isLongRunning = false;
  const timeout = setTimeout(() => {
    isLongRunning = true;
    isLoading.value = true;
  }, 200);

  try {
    console.log(
      "Navigating to:",
      `${item.to}?orgId=${orgId.value}&storeId=${storeId.value}`
    );
    await navigateTo(
      `${item.to}?orgId=${orgId.value}&storeId=${storeId.value}`
    );
    close();
  } finally {
    clearTimeout(timeout);
    if (isLongRunning) {
      isLoading.value = false;
    }
  }
};

const toggleChildItems = (index: number) => {
  if (expanded.value[index] === undefined) {
    expanded.value[index] = false;
  }
  expanded.value[index] = !expanded.value[index];
};
</script>

<style scoped>
/* Animation cho lớp nền đen (fade) */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

/* Animation cho phần trắng (slide) */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
}

/* Animation cho menu con (slide xuống, không opacity) */
.slide-child-enter-active,
.slide-child-leave-active {
  transition: transform 0.2s ease;
}

.slide-child-enter-from,
.slide-child-leave-to {
  transform: translateY(-10px);
}

.slide-child-enter-to,
.slide-child-leave-from {
  transform: translateY(0);
}
</style>
