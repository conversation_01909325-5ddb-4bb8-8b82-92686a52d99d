@tailwind base;
@tailwind components;
@tailwind utilities;
html,
body {
  padding: 0;
  margin: 0;
  /* font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif; */
  /* font-family: 'Instrument Serif', serif !important; */
}
@layer utilities {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

@layer components {
  .textPrimary {
    color: #364152;
  }

  .wrapper-danh-sach-don-hang {
    height: 100%;
  }

  .tooltip {
    @apply invisible;
  }

  .has-tooltip:hover .tooltip {
    @apply visible;
  }
  .textShadow {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-[#3F51B5] to-[#3F51B5];
    /* @apply text-transparent bg-clip-text bg-gradient-to-r from-[#0277BD] to-[#1D8BCD]; */
  }

  .textShadowBlack {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-[#131313] to-[#131415];
  }

  .boxShadow01 {
    box-shadow: -5px 0 5px #0000001f, 5px 0 5px #0000000f, 0 5px 5px #00000008;
  }

  .boxShadow03 {
    box-shadow: 0px 2px 6px 0px #0000001f;
  }

  .imgPayment {
    width: 50px !important;
    height: 30px !important;
    /* min-width: 50px !important;
    min-height: 30px !important;
    max-width: 50px !important;  */
  }

  .activeItem {
    border-color: #3f51b5 !important;
    background-color: #e8eaf6 !important;
    color: black !important;
    box-shadow: #0000001f;
  }

  .defaultItem {
    border-color: red !important;
    color: black !important;
  }

  .textGradient {
    background: #3f51b5;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;

    /* Light Shadow */

    text-shadow: 2px 2px 8px rgba(18, 18, 18, 0.08);
  }
  .btnGradient {
    background-color: #3f51b5 !important;
    color: #fff;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-radius: 6px;
    font-weight: 600;
    border: 1px solid #3f51b5;
    background: #3f51b5 !important;
  }
  .bgPrimary {
    background: #3f51b5;
    background-color: #3f51b5;
  }
  .bgReport {
    background: #3f51b5;
  }
  .shadowReport {
    box-shadow: 0px 2px 6px rgba(20, 20, 21, 0.14);
  }
  .activeMenu {
    @apply text-white bgPrimary rounded-md hover:text-white focus:text-white focus:no-underline;
  }

  .activeMenu_02 {
    @apply text-white bg-[#3F51B5] rounded-[4px] hover:text-white focus:text-white focus:no-underline duration-300 delay-100 ease-in-out;
  }
}

::-webkit-scrollbar {
  width: 9px !important;
  scrollbar-width: thin;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  /* background: #3F51B5; */
  background: #0d47a1 !important;
}
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
@media (max-width: 640px) {
  .width-230 {
    width: 230px !important;
  }

  .flexCol {
    display: flex;
    flex-flow: column;
    gap: 0.5rem;
  }

  .flexWrap {
    display: flex;
    flex-wrap: wrap;
  }

  .justifyCenter {
    display: flex;
    justify-content: center;
  }

  .widthPercent100 {
    width: 100%;
  }

  .gridColumns {
    display: grid;
    grid-column: span 2 / span 2;
  }

  .text13 {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .width-100 {
    width: 100px !important;
  }
}

@media (max-width: 1200px) {
  .disBlock {
    display: block !important;
  }

  .disNone {
    display: none !important;
  }

  .width-150 {
    width: 150px !important;
  }

  .width-100 {
    width: 100px !important;
  }

  .gridCol2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 1024px) {
  /* .widthFull{
    width: 100%;
  } */

  .flexCol {
    display: flex;
    flex-flow: column;
  }

  .itemStart {
    display: flex;
    align-items: flex-start !important;
  }

  .boxShadow02 {
    box-shadow: none !important;
  }
}
.content-wrapper {
  /* width: calc(100% - 238px); */
  height: 100%;
  position: fixed;
  background: #fff;
  /* left: 238px; */
  /* padding: 20px; */
}
.bgPrimary {
  /* background: linear-gradient(90deg, #0277bd 0%, #1d8bcd 100%); */
  background: #3f51b5;
}
.textPrimary {
  color: #3f51b5;
}
.borderRadius-0 {
  border-radius: 0px 4px 4px 0px;
}

.borderRadius-1 {
  border-radius: 4px 0px 0px 4px;
}

.borderRadius-2 {
  border-radius: 4px 4px 0px 0px;
}

.boxShadow02 {
  box-shadow: -8px 0 6px -6px #0000001f;
}
