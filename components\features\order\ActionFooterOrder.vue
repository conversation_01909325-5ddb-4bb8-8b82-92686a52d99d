<template>
  <div class="cursor-pointer relative">
    <svg
      @click="toogleOpenTooltip"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      stroke="currentColor"
      class="size-6"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"
      />
    </svg>
    <!-- action -->
    <div
      v-if="isOpenToolTip"
      class="absolute -top-[80px] -left-[120px] bg-white rounded"
    >
      <div
        @click="toogleCancelOrder"
        class="flex items-center justify-center py-1 px-2 rounded-t hover:bg-primary hover:text-white border border-primary"
        :class="
          order?.status === 'DRAFT' ||
          order?.status === 'CANCELLED' ||
          order?.financialStatusDescription === 'Đã thanh toán'
            ? ' disabled'
            : ''
        "
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"
            />
          </svg>
        </span>
        <span>Hủy đơn</span>
      </div>
      <div
        @click="handlePrintOrder"
        class="flex items-center justify-center py-1 px-2 hover:bg-primary hover:text-white border border-primary"
      >
        <div class="flex items-center mr-1">
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
              />
            </svg>
          </span>
          <span class="space-x-1">{{
            handleGetPrint(order?.order?.customAttributes)
          }}</span>
        </div>
        <div>In</div>
      </div>
      <div
        @click="handleReturnOrder"
        class="flex items-center justify-center py-1 px-2 rounded-b hover:bg-primary hover:text-white border border-primary"
        :class="order?.status !== 'COMPLETED' ? ' disabled ' : ''"
      >
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M8.25 9.75h4.875a2.625 2.625 0 0 1 0 5.25H12M8.25 9.75 10.5 7.5M8.25 9.75 10.5 12m9-7.243V21.75l-3.75-1.5-3.75 1.5-3.75-1.5-3.75 1.5V4.757c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0c1.1.128 1.907 1.077 1.907 2.185Z"
            />
          </svg>
        </span>
        <span>Đổi trả hàng</span>
      </div>
    </div>
    <!--  -->
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </div>
  <CancelOrderPopup
    v-if="isCancelOrderPopup"
    :order="diary"
    :title="'Hủy đơn hàng'"
    :text="`Bạn đang hủy đơn hàng ${diary?.id} vui lòng chọn lý do`"
    :reasonText="'Lý do hủy đơn'"
    :dataReason="dataReason"
    @cancel="toogleCancelOrder"
    @confirm="handleConfirmCancelOrder"
  >
  </CancelOrderPopup>
</template>
<script setup>
const CancelOrderPopup = defineAsyncComponent(() =>
  import("~/components/ui/feedback/CancelOrderPopup.vue")
);
const isOpenToolTip = ref(false);
const props = defineProps(["order"]);
const toogleOpenTooltip = () => {
  isOpenToolTip.value = !isOpenToolTip.value;
};
//
const { cancelOrder } = useOrder();
const isCancelOrderPopup = ref();
const toogleCancelOrder = () => {
  isCancelOrderPopup.value = !isCancelOrderPopup.value;
};
const orderStore = useOrderStore();
const handleConfirmCancelOrder = async (reason) => {
  try {
    const auth = useCookie("auth").value;
    const data = {
      reason: "CUSTOMER",
      updatedBy: auth?.user?.id,
      note: reason,
      orderType: "SALES",
    };
    const response = await cancelOrder(props.order?.id, data);
    orderStore.updateStatusOrderDetail(reason);

    useNuxtApp().$toast.success("Hủy đơn hàng thành công");
  } catch (error) {
    throw error;
  } finally {
    toogleOpenTooltip();
    toogleCancelOrder();
  }
};

const { printOrderHTML } = useOrder();
import printJS from "print-js";
const isLoading = ref(false);
const route = useRoute();
const handlePrintOrder = async () => {
  isLoading.value = true;
  const url = useRequestURL();

  const baseUrl = `${url.origin}/thanh-toan?orderId=${
    props.order?.id
  }&orgId=${url.searchParams.get("orgId")}&storeId=${url.searchParams.get(
    "storeId"
  )}`;
  try {
    const response = await printOrderHTML(
      props.order?.id,
      "Chưa thanh toán",
      baseUrl
    );

    const data = response.data;

    printJS({
      printable: data,
      type: "raw-html",
      scanStyles: false,
      style: `
        @page { margin: 0; } /* Xóa margin mặc định của trang in */
        body { margin: 0; } /* Đảm bảo body không có margin thừa */
      `,
    });
    orderStore.updateQuantityPrintOrder();
  } catch (error) {
    console.error("Error printing the order:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleGetPrint = (Items) => {
  const isPrint = Items.find((item) => item.key === "printTimes");
  if (isPrint) {
    return `(${isPrint?.value})`;
  } else {
    return "(0)";
  }
};
const handleReturnOrder = () => {
  navigateTo(
    `/order/return?orderReturnId=${props.order?.id}&orgId=${route.query?.orgId}&storeId=${route.query?.storeId}`
  );
};
const dataReason = [
  { name: "Chọn lý do hủy" },
  {
    name: "Khách hàng yêu cầu",
  },
  {
    name: "Thông tin chưa hợp lệ",
  },
  {
    name: "Không đủ hàng trong kho",
  },
  {
    name: "Không thanh toán đơn hàng",
  },
  {
    name: "Khác",
  },
];
</script>
<style scoped>
.disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>
