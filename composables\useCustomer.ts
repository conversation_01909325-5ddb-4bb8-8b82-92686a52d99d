export default function useCustomer() {
  const route = useRoute();
  const router = useRouter();
  const { $sdk } = useNuxtApp();

  const keyword = ref(route.query.keyword || "");
  const currentPage = ref<number>(parseInt(route.query.page as string) || 1);
  const startDate = ref(route.query.startDate || "");
  const endDate = ref(route.query.endDate || "");
  const selectedLevel = ref(route.query.selectedLevel || null);
  const customerId = ref(route.query.customerId || "");
  const name = ref("");
  const phone = ref("");
  const email = ref("");
  const address = ref("");
  const gender = ref("");
  const birthDate = ref("");
  const loading = ref(false);
  const itemsPerPage = 20;
  const { fetchListSellOrder } = useOrder();

  const fetchListCustomer = async (requestData: any) => {
    try {
      const response = await $sdk.user.searchCustomer(requestData);
      return response;
    } catch (error) {
      console.error("Error fetching customer info:", error);
      throw error;
    }
  };

  const createCustomer = async (requestData: any, createBy: string) => {
    try {
      const response = await $sdk.user.createCustomerV2(requestData, createBy);
      return response;
    } catch (error) {
      console.error("Error creating customer:", error);
      throw error;
    }
  };

  const updateCustomer = async (
    id: string,
    requestData: any,
    createdBy: string
  ) => {
    try {
      const response = await $sdk.user.updateCustomer(
        id,
        requestData,
        createdBy
      );
      useNuxtApp().$toast.success("Cập nhật thông tin khách hàng thành công");
      return response;
    } catch (error) {
      console.log("error", error);
      useNuxtApp().$toast.error(`${error}`);
      throw error;
    }
  };

  const getCustomerById = async (customerId: string) => {
    try {
      const response = await $sdk.user.getCustomerById(customerId);
      return response;
    } catch (error) {
      console.error("Error fetching customer info:", error);
      throw error;
    }
  };
  const customerStore = useCustomerStore();
  // const { data: listCustomerResponse, refresh: fetchCustomer } = useAsyncData(
  //   async () => {
  //     loading.value = true;

  //     const requestData = {
  //       keyword: keyword.value,
  //       startCreatedDate: startDate.value || null,
  //       endCreatedDate: endDate.value || null,
  //       currentPage: currentPage.value,
  //       memberLevel: selectedLevel.value || null,
  //       pageSize: itemsPerPage,
  //     };
  //     const response = await fetchListCustomer(requestData);
  //     customerStore.listCustomer = response?.content;
  //     loading.value = false;
  //     return response;
  //   }
  // );
  const fetchCustomerV2 = async (requestData: any) => {
    try {
      const response = await fetchListCustomer(requestData);
      if (response?.content?.length > 0) {
        customerStore.handleAddCustomer(response.content);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Lỗi khi gọi API fetchCustomerV2: ", error);
      return false;
    }
  };
  // // scroll customer
  // const scrollCustomer = async () => {
  //   if (currentPage.value < totalPages.value) {
  //     currentPage.value++;
  //     await updateQueryParams();
  //     const hasMoreData = await fetchCustomerV2();
  //     return hasMoreData;
  //   } else {
  //     return false;
  //   }
  // };

  const handleCreateCustomer = async () => {
    // Biểu thức regex để kiểm tra số điện thoại có độ dài từ 9-11 số và bắt đầu bằng số
    const phoneRegex = /^[0-9]{9,11}$/;

    if (!phoneRegex.test(phone.value)) {
      useNuxtApp().$toast.error(
        "Số điện thoại không hợp lệ. Vui lòng nhập từ 9 đến 11 chữ số."
      );
      return; // Dừng hàm nếu số điện thoại không hợp lệ
    }

    const requestData = {
      name: name.value,
      phone: phone.value,
      email: email.value,
      birthDate: birthDate.value
        ? new Date(birthDate.value).toISOString()
        : null,
    };

    try {
      const response = await createCustomer(requestData, "");
      useNuxtApp().$toast.success("Tạo khách hàng mới thành công: ");
      return response;
    } catch (error: any) {
      useNuxtApp().$toast.error(error);
      throw error;
    }
  };
  const getTopicByCustomerId = async (
    filterTopicRequest: any,
    pageSize: number,
    currentPage: number
  ) => {
    try {
      const response = await $sdk.crm.getTopicByCustomerId(
        filterTopicRequest,
        pageSize,
        currentPage
      );
      return response;
    } catch (error) {
      throw error;
    }
  };

  const getCustomerWallet = async (customerId: string, type: string) => {
    try {
      const response = await $sdk.accounting.getCustomerWallet(
        customerId,
        type
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createTopic = async (
    socialAppId: String,
    customerId: string,
    message: string
  ) => {
    try {
      const response = await $sdk.crm.createTopic(
        socialAppId,
        customerId,
        message
      );
      if (response) {
        useNuxtApp().$toast.success("Tạo trao đổi mới thành công");
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getVatInfoByOwnerPartyId = async (ownerPartyId: string) => {
    try {
      const response = await $sdk.user.getVatInfoByOwnerPartyId(ownerPartyId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const createVatInfo = async (
    company: string,
    taxCode: string,
    invoiceReceiveEmail1: string,
    ownerPartyId: string,
    address: string,
    createdBy: string
  ) => {
    try {
      console.log("createdBy", createdBy);
      const response = await $sdk.user.createVatInfo(
        company,
        taxCode,
        invoiceReceiveEmail1,
        ownerPartyId,
        address,
        createdBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    keyword,
    selectedLevel,
    startDate,
    endDate,
    currentPage,
    itemsPerPage,
    loading,
    name,
    phone,
    email,
    birthDate,
    address,
    gender,
    getCustomerById,
    handleCreateCustomer,
    updateCustomer,
    fetchListCustomer,
    createCustomer,
    fetchCustomerV2,
    getCustomerWallet,
    getTopicByCustomerId,
    createTopic,
    getVatInfoByOwnerPartyId,
    createVatInfo,
  };
}
