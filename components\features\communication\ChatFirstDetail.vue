<template>
  <div class="md:h-[90vh] h-[93svh] w-full relative">
    <div
      v-if="selectedRoom"
      class="flex flex-col flex-auto border-l bg-white h-[93vh] col-span-1"
    >
      <!-- Chat header -->
      <div
        class="chat-header md:px-6 px-2 py-4 flex flex-row flex-none justify-between items-center shadow-sm"
      >
        <div class="flex items-center gap-1">
          <div @click="handleBack" class="back-button lg:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </div>
          <div
            class="w-10 h-10 bg-gray-300 text-white flex justify-center items-center rounded-full"
          >
            <span class="text-lg font-bold">{{
              selectedRoom?.customer?.name?.charAt(0)?.toUpperCase()
            }}</span>
          </div>
          <div class="ml-4 text-sm">
            <p class="font-bold text-primary">
              {{ selectedRoom?.customer?.name || "Ẩn danh" }}
            </p>
            <p class="text-gray-500 font-bold">
              Chủ đề: {{ selectedRoom?.name }}
            </p>
          </div>
        </div>
        <div
          v-if="selectedRoom?.status !== 'CLOSED'"
          @click="toogleCloseTopic"
          class="text-primary font-semibold text-xs cursor-pointer"
        >
          Đóng chủ đề
        </div>
      </div>
      <!-- phần nội dung -->
      <div ref="chatBodyRef" class="p-4 flex-1 overflow-y-scroll bg-[#fff]">
        <div
          v-for="(message, index) in messages"
          :key="message?.createdBy?.name"
          class="flex flex-col space-y-1 mb-1 text-sm"
        >
          <div
            :class="
              selectedRoom?.customer?.id !== message.updatedBy
                ? 'flex-row-reverse'
                : 'flex-row'
            "
            class="flex items-center gap-2"
          >
            <div>
              <div
                v-if="
                  index === 0 ||
                  message?.createdBy?.name !==
                    messages[index - 1]?.createdBy?.name ||
                  (message?.createdStamp &&
                    messages[index - 1]?.createdStamp &&
                    differenceInHours(
                      new Date(message.createdStamp),
                      new Date(messages[index - 1].createdStamp)
                    ) >= 1)
                "
                class="space-x-1"
              >
                <span class="font-semibold">
                  {{ message?.createdBy?.name || "Ẩn danh" }}
                </span>
                <span class="text-xs">{{
                  formatTime24hWithMoment(message?.createdStamp)
                }}</span>
              </div>
            </div>
          </div>
          <!-- phần nội dung chat -->
          <div
            :class="
              selectedRoom?.customer?.id !== message.updatedBy
                ? 'flex-row-reverse'
                : 'flex-row'
            "
            class="flex items-center gap-2"
          >
            <div class="">
              <!-- phần tên -->
              <div
                :class="
                  selectedRoom?.customer?.id !== message.updatedBy
                    ? 'bg-myChat text-right px-2 py-1  rounded-2xl max-w-lg'
                    : 'bg-secondary text-left px-2 py-1 rounded-2xl max-w-lg'
                "
              >
                <!-- Hiển thị hình ảnh -->
                <template
                  v-if="
                    message.format === 'ATTACHMENT' && message.attachmentUrl
                  "
                >
                  <a :href="message.attachmentUrl" target="_blank">
                    Tệp đính kèm
                  </a>
                </template>
                <template v-else-if="message.format === 'HTML'">
                  <div v-html="message.content"></div>
                </template>
                <!-- Hiển thị tin nhắn văn bản -->
                <template v-else>
                  <p class="leading-relaxed text-left">
                    {{ message?.content }}
                  </p>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div ref="mySection" class="h-0"></div>
      </div>
      <div class="flex items-center justify-center mb-4">
        <button
          @click="joinRoom"
          v-if="selectedRoom?.status !== 'CLOSED' && selectedRoom?.roomId"
          class="bg-primary text-white px-4 py-2 rounded"
        >
          Tham gia Chat
        </button>
      </div>
    </div>
    <!-- <div v-else class="h-[93vh] flex items-center justify-center">
      <div class="text-red-500 bg-red-100 p-6">
        Vui lòng chọn một phòng để hiển thị lịch sử trao đổi
      </div>
    </div> -->
    <!--  -->
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
    <ConfirmDialog
      v-if="isModalCloseTopic"
      :title="`Bạn có chắc chắn muốn đóng chủ đề`"
      :message="'Khi bạn đóng chủ đề, bạn sẽ không thể nhận được thông báo từ chủ đề này nữa.'"
      @cancel="toogleCloseTopic"
      @confirm="handleConfirmCloseOrder"
    >
    </ConfirmDialog>
  </div>
</template>

<script setup>
import { differenceInHours } from "date-fns";

const auth = useCookie("auth").value;
const props = defineProps(["selectedRoom"]);
const emits = defineEmits(["joinRoom", "closeTopicFirst", "back"]);
const route = useRoute();
const { getMessages } = useChat();
const messages = ref();
const { $matrixClient } = useNuxtApp();
const matrixUserId = ref();
const isLoading = ref();
const showModal = ref(false);
const handleConfirm = async () => {
  console.log("đang đóng chủ đề");
  emits("closeTopicFirst");
  showModal.value = false;
};
const handleGetMessage = async () => {
  let loadingTimer;
  if (route.query.topicId) {
    try {
      loadingTimer = setTimeout(() => {
        isLoading.value = true;
      }, 200);
      messages.value = [];
      const response = await getMessages(route.query.topicId, 40, 0);
      messages.value = response.data;
      // Xử lý matrixUserId
      if (localStorage.getItem("matrixUserId")) {
        const extractedId = localStorage
          .getItem("matrixUserId")
          .match(/(?<=@)[^:]+/);
        if (extractedId) {
          matrixUserId.value = extractedId[0];
        } else {
          console.log("No match found in matrixUserId");
        }
      }
    } catch (error) {
      messages.value = [];
      throw error;
    } finally {
      clearTimeout(loadingTimer);
      isLoading.value = false;
    }
  }
};

onMounted(async () => {
  await handleGetMessage();
  scrollToBottom();
});
watch(
  () => route.query.topicId,
  async (newTopicId) => {
    if (newTopicId) {
      await handleGetMessage();
      scrollToBottom();
    }
  }
);
//
const joinRoom = async () => {
  emits("joinRoom");
};
// scroll
const chatBodyRef = ref(null);
const scrollToBottom = async () => {
  const chatBody = chatBodyRef.value;
  if (chatBody) {
    await nextTick(); // Đảm bảo DOM đã cập nhật xong

    chatBody.scrollTop = chatBody.scrollHeight;
  }
};
const handleBack = async () => {
  emits("back");
};
const isModalCloseTopic = ref(false);
const toogleCloseTopic = () => {
  isModalCloseTopic.value = !isModalCloseTopic.value;
};
const handleConfirmCloseOrder = () => {
  emits("closeTopicFirst");
  toogleCloseTopic();
};
</script>
