<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div
      class="bg-white rounded-lg shadow-lg py-4 px-2 max-w-5xl w-full animate-popup"
    >
      <div class="flex justify-between">
        <div></div>
        <div class="text-lg font-bold mb-2 text-center">
          <PERSON><PERSON><PERSON><PERSON> chỉnh sản phẩm
        </div>
        <div class="text-red-500 cursor-pointer" @click="cancel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <div class="overflow-auto bg-white text-sm">
        <thead class="bg-gray-100">
          <tr>
            <th class="border-dashed py-1 text-center w-5/12">Tên</th>
            <th class="border-dashed py-1 text-center w-1/12">Đơn vị</th>
            <th class="border-dashed py-1 text-center w-1/12">SL</th>
            <th class="border-dashed py-1 text-center w-2/12">Đơn giá(đ)</th>
            <th class="border-dashed py-1 text-center w-1/12">VAT(%)</th>
            <th class="border-dashed py-1 text-center w-2/12">Thành tiền(đ)</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="product in dataItemInvoice" :key="product.id">
            <ItemProductInvoice
              :product="product"
              :dataItemInvoice="dataItemInvoice"
              @updateInvoice="handleUpdateInvoice"
            ></ItemProductInvoice>
          </tr>
        </tbody>
      </div>

      <div class="flex justify-end mt-2 space-x-2">
        <button
          v-if="!isHideButton"
          @click="cancel"
          class="px-6 py-1 bg-gray-300 rounded"
        >
          Hủy
        </button>
        <button
          @click="confirm"
          class="px-6 py-1 bg-primary text-white rounded"
        >
          Lưu
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps(["order", "dataInvoiceDraft", "dataItemInvoice"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const { updateInvoiceItem } = useInvoice();
const dataChangeProduct = ref([]);
const auth = useCookie("auth");
const confirm = async () => {
  if (!props.dataInvoiceDraft?.invoiceId) {
    useNuxtApp().$toast.warning("Vui lòng tạo hóa đơn nháp");
    return;
  }

  if (dataChangeProduct.value?.length) {
    for (const item of dataChangeProduct.value) {
      try {
        const response = await updateInvoiceItem(
          props.dataInvoiceDraft?.invoiceId,
          item,
          auth.value?.user?.id
        );
        if (response?.code === 0) {
          useNuxtApp().$toast.error("Cập nhật thất bại cho một sản phẩm.");
          return;
        }
      } catch (err) {
        console.error("Lỗi gọi API:", err);
        useNuxtApp().$toast.error("Lỗi hệ thống khi cập nhật sản phẩm.");
        return;
      }
    }
  }

  emit("confirm");
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const handleUpdateInvoice = async (data) => {
  if (dataChangeProduct.value.length >= 0) {
    const index = dataChangeProduct.value.findIndex(
      (item) => item.id === data.id
    );
    if (index !== -1) {
      dataChangeProduct.value[index] = data;
    } else {
      dataChangeProduct.value.push(data);
    }
  } else {
    dataChangeProduct.value.push(data);
  }
};
</script>

<style scoped></style>
