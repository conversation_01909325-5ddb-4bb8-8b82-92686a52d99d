import { defineStore } from "pinia";
import type { PartyDetail } from "~/types/PartyDetail";
import type { Promotion } from "~/types/Promotion";

export const useSale = defineStore("sale", {
  state: () => ({
    carts: [] as Array<{
      id: number;
      orderId: string;
      products: Array<any>;
      order: any;
      selectedOption: string;
      customer: PartyDetail | null;
      productImages: { [key: string]: string };
      vouchers: Array<any>;
      appliedVouchers: Array<any>;
      campaignActions: Array<any>;
      activeCampaignId: number | null;
      orderPromotion: Promotion | null;
      notes: string;
    }>,
    currentCartIndex: null as number | null,
  }),

  actions: {
    async createNewCart() {
      const router = useRouter();

      // Use tab-isolated context instead of cookies
      const { storeId } = useTabContext();

      const body = {
        time: new Date().getTime(),
        orderType: "POS_SALE",
        customerId: "",
        platform: "WEB",
      };

      try {
        const response = await useOrder().createOrderTemp(body);
        const newCartId = this.carts.length + 1;
        this.carts.push({
          id: newCartId,
          orderId: response.data.orderId,
          products: [],
          order: [],
          selectedOption: "1",
          customer: null,
          productImages: {},
          vouchers: [],
          appliedVouchers: [],
          campaignActions: [],
          activeCampaignId: null,
          orderPromotion: null,
          notes: "",
        });
        this.currentCartIndex = newCartId - 1;
        router.push({
          path: "/sale",
          query: {
            storeId: storeId.value,
            orderId: response.data.orderId,
          },
        });
      } catch (error: any) {
        useNuxtApp().$toast.error(error.data.message);
      }
    },
    async getListSellOrder() {
      const data = {
        status: 1,
        // date_create_from: Date.now(),
        // date_create_to: Date.now(),
        // date_update_to: Date.now(),
        // date_update_from: Date.now(),
      };

      try {
        const response = await useOrder().fetchListSellOrder(data);

        this.carts = [];
        response.data.data.forEach((order: any) => {
          const newCartId = this.carts.length + 1;
          this.carts.push({
            id: newCartId,
            orderId: order.id,
            products: order.orderItemProfiles
              ? order.orderItemProfiles.map((item: any) => ({
                  ...item,
                  currentQuantity: item.quantity,
                  variantId: item.id,
                }))
              : [],
            order: order.order,
            selectedOption: order.order.totalShippingPrice.amount ? "2" : "1",
            customer: null,
            productImages: {},
            vouchers: [],
            appliedVouchers: [],
            campaignActions: [],
            activeCampaignId: null,
            orderPromotion: null,
            notes: "",
          });
        });
      } catch (error: any) {
        console.error("Error fetching list of sell orders", error);
      }
    },
    clearCarts() {
      this.carts = [];
    },

    async switchToCart(index: number) {
      if (index < 0 || index >= this.carts.length) {
        console.error("Invalid cart index:", index);
        return;
      }
      this.currentCartIndex = index;

      const currentCart = this.carts[index];
      if (
        currentCart &&
        currentCart.order &&
        currentCart.order.ownerPartyId &&
        !currentCart.customer
      ) {
        await this.getCustomerById(index, currentCart.order.ownerPartyId);
      }
      await this.fetchVouchersForCurrentCart();
      await this.fetchCampaignActionActiveNowForCurrentCart();

      // Use tab-isolated context instead of cookies
      const { storeId } = useTabContext();
      const router = useRouter();
      router.push({
        path: "/sale",
        query: {
          storeId: storeId.value,
          orderId: currentCart.orderId,
        },
      });
      console.log("currentCartIndex", this.currentCartIndex);
    },

    async cancelOrder(index: number) {
      try {
        const authStore = useAuthStore();
        await useOrder().removeDraftOrder(
          this.carts[index]?.orderId,
          authStore.user.id
        );
        this.carts.splice(index, 1);
        if (this.currentCartIndex >= index) {
          this.currentCartIndex =
            this.currentCartIndex > 0 ? this.currentCartIndex - 1 : 0;
        }
        if (this.carts.length === 0) {
          this.currentCartIndex = null;
        }
      } catch (error) {
        console.log("Error when deleting order");
        throw error;
      }
    },

    async addProductToCart(orderId: any, product: any) {
      const lineItems = [
        {
          quantity: 1,
          parent_id: "",
          product_id: product.id,
          input_price: product.price,
          discount_amount: 0,
        },
      ];
      try {
        const res = await useOrder().addOrderLineItems(orderId, lineItems);
      } catch (error) {
        console.log("Error adding product to order", error);
        throw error;
      }
    },

    async getProductImage(variantId: string) {
      const currentCartIndex = this.currentCartIndex;
      if (currentCartIndex === null) return "";
      const { getImageProducts } = useProduct();

      const currentCart = this.carts[currentCartIndex];
      if (!currentCart.productImages[variantId]) {
        try {
          const response = await getImageProducts(variantId);
          if (response && response.data && response.data[variantId]) {
            currentCart.productImages[variantId] = response.data[variantId];
          } else {
            currentCart.productImages[variantId] = "";
          }
        } catch (error) {
          console.error("Error getting product image:", error);
          currentCart.productImages[variantId] = "";
        }
      }
      return currentCart.productImages[variantId];
    },
    async updateQuantityProductInOrderAndRefresh(
      orderId: string,
      productId: string,
      quantity: number
    ) {
      try {
        await useOrder().updateQuantityProductInOrder(
          orderId,
          productId,
          quantity
        );

        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          const product = currentCart.products.find(
            (item) => item.orderLineItem.id === productId
          );
          if (product) {
            product.orderLineItem.currentQuantity = quantity;
          }
        }

        const customers = this.carts.map((cart) => cart.customer);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();

        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });
      } catch (error) {
        console.error("Error updating quantity:", error);
      }
    },

    async updatePriceInOrder(
      orderId: string,
      orderItemId: string,
      priceNew: number
    ) {
      try {
        const response = await useOrder().updatePriceInOrder(
          orderId,
          orderItemId,
          priceNew,
          ""
        );

        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          const product = currentCart.products.find(
            (item) => item.orderLineItem.id === orderItemId
          );
          if (product) {
            product.orderLineItem.price = priceNew;
          }
        }

        const customers = this.carts.map((cart) => cart.customer);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();

        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });

        return response;
      } catch (error) {
        console.error("Error updating price:", error);
        throw error;
      }
    },
    async updateDiscountProductInCart(
      orderId: string,
      productId: string,
      discount_amount: number,
      type_discount: string
    ) {
      try {
        const updated_by = "";
        const requestData = {
          discount_amount,
          type_discount,
        };
        const response = await useOrder().updateDiscountProductInCart(
          orderId,
          productId,
          updated_by,
          requestData
        );

        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        console.log("🚀 ~ currentCart:", currentCart);
        if (currentCart) {
          const product = currentCart.products.find(
            (item) => item.orderLineItem.id === productId
          );
          if (product) {
            product.orderLineItem.discountValue = discount_amount;
            product.orderLineItem.discountType = type_discount;
          }
        }
        const customers = this.carts.map((cart) => cart.customer);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();

        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });

        return response;
      } catch (error) {
        console.error("Error updating discount:", error);
        throw error;
      }
    },
    async removeProductFromOrder(orderId: string, productId: string) {
      try {
        await useOrder().removeProductInOrder(
          orderId,
          productId,
          "User Removed"
        );
        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          currentCart.products = currentCart.products.filter(
            (product) => product.id !== productId
          );
        }
      } catch (error) {
        console.error("Error removing product from order:", error);
      }
    },
    //Start Customer
    async getCustomerById(cartIndex: number, customerId: string) {
      if (cartIndex < 0 || cartIndex >= this.carts.length) {
        console.error("Invalid cart index:", cartIndex);
        return;
      }

      if (this.carts[cartIndex].customer) {
        console.log("Customer info already exists for cart index:", cartIndex);
        return this.carts[cartIndex].customer;
      }

      const { getCustomerById } = useCustomer();
      try {
        const response = await getCustomerById(customerId);
        console.log("🚀 ~ getCustomerById ~ response:", response);

        if (this.carts[cartIndex]) {
          this.carts[cartIndex].customer = response;
        } else {
          console.error("Cart at index does not exist:", cartIndex);
        }
        return response;
      } catch (error) {
        console.error("Error fetching customer info:", error);
        throw error;
      }
    },

    async updateOrderCustomer(
      orderId: string,
      customerId: string,
      shippingAddress: string
    ) {
      try {
        const response = await useOrder().updateOrderCustomer(
          orderId,
          customerId,
          shippingAddress
        );
        await this.getListSellOrder();
        return response;
      } catch (error) {
        console.error("Error updating customer and shipping address:", error);
        throw error;
      }
    },

    resetCustomerData() {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        if (currentCart) {
          currentCart.customer = null;
        }
      }
    },
    //End Customer
    async updateShippingOrder(orderId: string, shippingId: string) {
      try {
        const response = await useOrder().updateShippingOrder(
          orderId,
          shippingId
        );
        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          currentCart.order.shippingId = shippingId;
          currentCart.selectedOption = shippingId ? "2" : "1";
        }
        const customers = this.carts.map((cart) => cart.customer);
        const selectedOption = this.carts.map((cart) => cart.selectedOption);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();
        this.carts.forEach((cart, index) => {
          cart.selectedOption = selectedOption[index];
        });
        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });
        return response;
      } catch (error) {
        console.error("Error updating shipping order:", error);
        throw error;
      }
    },
    async updateShippingFee(orderId: string, shippingFee: number) {
      try {
        const response = await useOrder().updateShippingFee(
          orderId,
          shippingFee
        );
        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          currentCart.order.totalShippingPrice.amount = shippingFee;
          currentCart.selectedOption = shippingFee > 0 ? "2" : "1";
        }
        const customers = this.carts.map((cart) => cart.customer);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();

        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });
        return response;
      } catch (error) {
        console.error("Error updating shipping fee:", error);
        throw error;
      }
    },

    setSelectedOption(option: string) {
      const currentCartIndex = this.currentCartIndex;
      if (currentCartIndex !== null) {
        this.carts[currentCartIndex].selectedOption = option;
      }
    },
    async updateDiscountCart(orderId: string, amount: number, type: string) {
      try {
        const updated_by = "";
        const requestData = {
          amount,
          type,
        };
        const response = await useOrder().updateDiscountCart(
          orderId,
          updated_by,
          requestData
        );

        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          currentCart.order.discountTotalPrice = {
            amount: amount,
            type: type,
          };
        }

        const customers = this.carts.map((cart) => cart.customer);
        const campaignActions = this.carts.map((cart) => cart.campaignActions);
        await this.getListSellOrder();

        this.carts.forEach((cart, index) => {
          cart.customer = customers[index] || null;
        });
        this.carts.forEach((cart, index) => {
          cart.campaignActions = campaignActions[index] || null;
        });
        return response;
      } catch (error) {
        console.error("Error updating discount cart:", error);
        throw error;
      }
    },

    async updateSaleEmployee(
      orderId: string,
      saleId: string,
      updatedBy: string
    ) {
      try {
        const response = await useOrder().updateSaleEmployee(
          orderId,
          saleId,
          updatedBy
        );
        const currentCart = this.carts.find((cart) => cart.orderId === orderId);
        if (currentCart) {
          currentCart.order.saleId = saleId;
        }
        return response;
      } catch (error) {
        console.error("Error updating sale employee:", error);
        throw error;
      }
    },

    async updateDateCreateOrder(
      orderId: string,
      orderDate: number,
      updatedBy: string
    ) {
      try {
        const response = await useOrder().updateDateCreateOrder(
          orderId,
          orderDate,
          updatedBy
        );
        await this.getListSellOrder();
        return response;
      } catch (error) {
        console.error("Error updating order creation date:", error);
        throw error;
      }
    },
    //Vouchers
    async fetchVouchersForCurrentCart() {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        if (currentCart && currentCart.customer) {
          const { fetchVouchers } = useCampaign();
          try {
            const vouchers = await fetchVouchers(currentCart.customer.id);
            currentCart.vouchers = vouchers;
          } catch (error) {
            console.error("Error fetching vouchers for current cart:", error);
          }
        }
      }
    },

    // applyVoucherToCart(voucher: any) {
    //   if (this.currentCartIndex !== null) {
    //     const currentCart = this.carts[this.currentCartIndex];
    //     currentCart.appliedVouchers.push(voucher);
    //   }
    // },
    async addVoucherOrder(voucher: any) {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        const { checkValidVoucher } = useCampaign();
        const { addVoucher } = useOrder();

        if (currentCart.customer) {
          try {
            const response = await checkValidVoucher(
              currentCart.customer.id,
              voucher.voucherCode
            );
            if (response.isValid) {
              await addVoucher(currentCart.orderId, voucher.voucherCode);
              currentCart.appliedVouchers.push(voucher);
            } else {
              useNuxtApp().$toast.error("Voucher is not valid!");
            }
          } catch (error) {
            console.error("Error validating voucher:", error);
          }
        } else {
          useNuxtApp().$toast.error(
            "No customer associated with the current cart!"
          );
        }
      }
    },
    async applyVoucherToCart(voucher: any) {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        const { checkValidVoucher } = useCampaign();

        if (currentCart.customer) {
          try {
            const response = await checkValidVoucher(
              currentCart.customer.id,
              voucher.voucherCode
            );
            if (response.isValid) {
              currentCart.appliedVouchers.push(voucher);
            } else {
              useNuxtApp().$toast.error("Voucher is not valid!");
            }
          } catch (error) {
            console.error("Error validating voucher:", error);
          }
        } else {
          useNuxtApp().$toast.error(
            "No customer associated with the current cart!"
          );
        }
      }
    },

    //CampaignButton
    async fetchCampaignActionActiveNowForCurrentCart() {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        if (currentCart && currentCart.customer) {
          const { fetchCampaignActionActiveNow } = useCampaign();
          try {
            const campaignActions = await fetchCampaignActionActiveNow(
              currentCart.customer.id,
              ""
            );
            currentCart.campaignActions = campaignActions;
          } catch (error) {
            console.error("Error fetching vouchers for current cart:", error);
          }
        }
      }
    },

    setActiveCampaign(campaignId: number) {
      if (this.currentCartIndex !== null) {
        const currentCart = this.carts[this.currentCartIndex];
        if (currentCart.activeCampaignId === campaignId) {
          currentCart.activeCampaignId = null;
          currentCart.orderPromotion = null;
        } else {
          currentCart.activeCampaignId = campaignId;
          currentCart.orderPromotion = null;
        }
      }
    },
    //Promotion
    async getOrderPromotionByMemberLevel(memberLevel: string) {
      try {
        const { getOrderPromotion } = useOrder();
        const response = await getOrderPromotion(memberLevel);
        this.setPromotion(response.data);
      } catch (error) {
        console.error("Error fetching order promotion:", error);
      }
    },

    setPromotion(promotionData: any) {
      if (this.currentCartIndex !== null) {
        this.carts[this.currentCartIndex].orderPromotion = promotionData;
      }
    },

    updateCurrentCartNote(note: string) {
      if (this.currentCartIndex !== null) {
        this.carts[this.currentCartIndex].notes = note;
      }
    },
  },

  getters: {
    currentOrderId: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.orderId;
      }
      return null;
    },

    currentCartProducts: (state) => {
      if (
        state.currentCartIndex !== null &&
        state.carts[state.currentCartIndex]
      ) {
        return (
          state.carts[state.currentCartIndex]?.products.map((product) => ({
            id: product.orderLineItem.id,
            variantId: product.orderLineItem.variant,
            unitPrice: product.orderLineItem?.variant?.unitPrice,
            orderItemName: product.orderLineItem.orderItemName,
            currentQuantity: product.orderLineItem.currentQuantity,
            quantity: product.orderLineItem.quantity,
            price: product.orderLineItem?.variant?.price?.amount,
            originalTotalPrice:
              product.orderLineItem.originalTotalPrice?.amount || 0,
            discount: product.orderLineItem?.discount?.value || 0,
            discountedTotalPrice: product.orderLineItem?.discountedTotalPrice,
          })) || []
        );
      }
      return [];
    },

    getCurrentOrderCustomer: (state) => {
      if (state.currentCartIndex !== null) {
        const currentCart = state.carts[state.currentCartIndex];
        return {
          orderId: currentCart?.orderId || null,
          customerId: currentCart?.order?.ownerPartyId || null,
          shippingAddress: currentCart?.order?.shippingAddress || null,
        };
      }
      return {
        orderId: null,
        customerId: null,
        shippingAddress: null,
      };
    },

    currentOrderTotal: (state) => {
      if (state.currentCartIndex !== null) {
        const cart = state.carts[state.currentCartIndex];
        if (cart && cart.order) {
          const manualDiscountAmount = cart.order.discountApplications
            ? cart.order.discountApplications
                .filter((discount) => discount.title === "giảm giá thủ công")
                .reduce((sum, discount) => sum + discount.value.amount, 0)
            : 0;

          return {
            subtotal: cart.order.currentSubtotalPrice?.amount || 0,
            total: cart.order.currentTotalPrice?.amount || 0,
            totalDiscount: cart.order.discountTotalPrice?.amount || 0,
            shipping: cart.order.totalShippingPrice?.amount || 0,
            manualDiscount: manualDiscountAmount,
          };
        }
      }
      return {
        subtotal: 0,
        total: 0,
        totalDiscount: 0,
        shipping: 0,
        manualDiscount: 0,
      };
    },
    currentEmployee: (state) => {
      if (state.currentCartIndex !== null) {
        const cart = state.carts[state.currentCartIndex];
        if (cart && cart.order) {
          return {
            salePartyId: cart.order.salePartyId || null,
          };
        }
      }
      return {
        salePartyId: null,
      };
    },

    currentCarrierId: (state) => {
      // if (state.currentCartIndex !== null) {
      //   const cart = state.carts[state.currentCartIndex];
      //   if (cart && cart.order) {
      //     return {
      //       carrierId: cart.order.customAttribute.carrierId || null,
      //     };
      //   }
      // }
      // return {
      //   carrierId: null,
      // };
      return null;
    },
    getCustomerData: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.customer || null;
      }
      return null;
    },

    selectedOption: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.selectedOption || "1";
      }
      return "1";
    },

    getCurrentCartVouchers: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.vouchers || [];
      }
      return [];
    },

    getCurrentCartAppliedVouchers: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.appliedVouchers || [];
      }
      return [];
    },

    getCampaignActions: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.campaignActions || [];
      }
      return [];
    },

    getActiveCampaignId: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.activeCampaignId || null;
      }
      return null;
    },

    getOrderPromotion: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.orderPromotion || null;
      }
      return null;
    },

    currentCartNote: (state) => {
      if (state.currentCartIndex !== null) {
        return state.carts[state.currentCartIndex]?.notes || "";
      }
      return "";
    },
  },
});
