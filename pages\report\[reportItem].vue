<template>
  <div class="p-6 h-screen overflow-y-auto mx-2 bg-white">
    <DateTime
      :dataSaleEmployee="reportSaleEmployee"
      @update-dates="handleDateUpdate"
      @update-employee="handleIdUpdate"
      @update-valueDate="handleHideButtonMonth"
      @search="handleSearch"
    />
    <LoadingSpinner v-if="isLoading"></LoadingSpinner>
    <div v-if="!isLoading">
      <div v-if="route.params.reportItem === 'overview'">
        <div class="mt-5">
          <Revenue
            :isButtons="true"
            :isMonth="isMonth"
            :isYear="isYear"
            :dataRevenue="reportByDetail"
            :selectedButton="selectedButton"
            @update-typeView="handleUpdateTypeView"
          ></Revenue>
          <Chart :dataChart="reportByDetail"></Chart>
          <SaleTable :data="reportByDetail"></SaleTable>
        </div>
      </div>
      <div v-else-if="route.params.reportItem === 'employees'">
        <div class="mt-5">
          <Revenue
            :isButtons="false"
            :dataRevenue="reportSaleEmployee"
            :isMonth="isMonth"
            :isYear="isYear"
            @update-typeView="handleUpdateTypeView"
            :selectedButton="selectedButton"
          ></Revenue>
          <Chart :dataChart="reportSaleEmployee"></Chart>
          <SaleTable :data="reportSaleEmployee"></SaleTable>
          <div class="my-5 flex justify-end gap-4"></div>
        </div>
      </div>
      <div v-else>
        <div class="mt-5">
          <Revenue
            :isButtons="true"
            :dataRevenue="reportByPaymentMethod"
            :isMonth="isMonth"
            :isYear="isYear"
            @update-typeView="handleUpdateTypeView"
            :selectedButton="selectedButton"
          ></Revenue>
          <Chart :dataChart="reportByPaymentMethod"></Chart>
          <PaidRevenue :data="reportByPaymentMethod"></PaidRevenue>
        </div>
      </div>
    </div>
    <div class="pb-5"></div>
  </div>
</template>

<script setup>
const route = useRoute();
useHead({
  title: "Báo cáo",
  meta: [
    {
      name: "description",
      content: "Báo cáo",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission"],
  name: "Báo cáo",
});

const reportSaleEmployee = ref([]);
const reportByDetail = ref([]);
const reportDateMonthYear = ref([]);
const reportByPaymentMethod = ref([]);
const reportByStore = ref([]);
const dateTo = ref(Date.now());
const dateFrom = ref(Date.now());
const idEmployee = ref("ALL");
const isLoading = ref(true);
const isMonth = ref(false);
const isYear = ref(false);
const typeView = ref("DEFAULT");
const selectedButton = ref(1);
const {
  reportBySaleEmployees,
  reportByDetails,
  reportDateMonthYears,
  reportByPaymentMethods,
  reportByStores,
} = useReport();
const getData = async () => {
  isLoading.value = true;
  try {
    const [
      dataSaleEmployee,
      dataReportByDetail,
      dataDateMonthYear,
      dataPaymentMethods,
      dataReportByStore,
    ] = await Promise.all([
      reportBySaleEmployees(idEmployee.value, dateFrom.value, dateTo.value),
      reportByDetails(
        idEmployee.value,
        typeView.value,
        dateFrom.value,
        dateTo.value
      ),
      reportDateMonthYears(
        idEmployee.value,
        typeView.value,
        dateFrom.value,
        dateTo.value
      ),
      reportByPaymentMethods(idEmployee.value, dateFrom.value, dateTo.value),
      reportByStores(dateFrom.value, dateTo.value),
    ]);

    reportSaleEmployee.value = dataSaleEmployee.data;
    reportByDetail.value = dataReportByDetail.data;
    reportDateMonthYear.value = dataDateMonthYear.data;
    reportByPaymentMethod.value = dataPaymentMethods.data;
    reportByStore.value = dataReportByStore.data;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const handleDateUpdate = ({ from, to }) => {
  dateTo.value = to;
  dateFrom.value = from;
  console.log("date to", dateTo.value);
  console.log("date from", dateFrom.value);
};
const handleIdUpdate = (id) => {
  if (!id.id) {
    idEmployee.value = "ALL";
  } else {
    idEmployee.value = id.id;
  }
};
const handleHideButtonMonth = (value) => {
  if (value.value >= 3 && value.value <= 4) {
    isMonth.value = true;
  }
  if (value.value > 4) {
    isYear.value = true;
  }
};
const handleUpdateTypeView = async (TypeView) => {
  if (TypeView.id === 1) {
    console.log("xem theo ngay ", TypeView.id);
    selectedButton.value = 1;
    typeView.value = "BY_DATE";
  } else if (TypeView.id === 2) {
    console.log("xem theo thang ", TypeView.id);
    typeView.value = "BY_MONTH";
    selectedButton.value = 2;
  } else {
    console.log("xem theo nam", TypeView.id);
    typeView.value = "BY_YEAR";
    selectedButton.value = 3;
  }
};

onMounted(async () => {
  await getData();
});
watch([dateFrom, dateTo, typeView, idEmployee], async () => {
  await getData();
});
const handleSearch = async () => {
  try {
    await getData();
  } catch (error) {
    throw error;
  }
};
</script>

<style scoped></style>
