# Migration: Product Page Pagination

## Tổng quan

Page product đã được cập nhật để sử dụng component `UiPagination` chung thay cho pagination custom, cùng với `ProductTableLoading` component cho loading states.

## Các thay đổi chính

### 1. 🔄 TableManagerProduct.vue

#### **Trước (Custom Pagination)**
```vue
<!-- Desktop Pagination -->
<div class="border-t border-gray-200 bg-white flex-shrink-0 shadow-lg">
  <div class="px-4 py-4">
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        Hiển thị <span class="font-medium text-primary">{{ startItem }}</span> đến
        <span class="font-medium text-primary">{{ endItem }}</span> trong tổng số
        <span class="font-medium text-primary">{{ totalItems }}</span> sản phẩm
      </div>
      <div class="flex items-center gap-3">
        <button @click="$emit('page-change', currentPage - 1)">← Trước</button>
        <span>Trang {{ currentPage }} / {{ totalPages }}</span>
        <button @click="$emit('page-change', currentPage + 1)">Sau →</button>
      </div>
    </div>
  </div>
</div>

<!-- Mobile Pagination -->
<div class="border-t border-gray-200 bg-white flex-shrink-0 shadow-lg">
  <div class="px-4 py-4">
    <div class="flex items-center justify-between">
      <div class="text-xs text-gray-600">{{ startItem }}-{{ endItem }} / {{ totalItems }}</div>
      <div class="flex items-center gap-2">
        <button @click="$emit('page-change', currentPage - 1)">← Trước</button>
        <span>{{ currentPage }}/{{ totalPages }}</span>
        <button @click="$emit('page-change', currentPage + 1)">Sau →</button>
      </div>
    </div>
  </div>
</div>
```

#### **Sau (UiPagination Component)**
```vue
<!-- Desktop Pagination -->
<UiPagination
  v-if="ListProduct.length > 0 || isLoading"
  :current-page="currentPage"
  :total-pages="totalPages"
  :total-items="totalItems"
  :items-per-page="itemsPerPage"
  item-label="sản phẩm"
  @page-change="$emit('page-change', $event)"
/>

<!-- Mobile Pagination -->
<UiPagination
  v-if="ListProduct.length > 0 || isLoading"
  :current-page="currentPage"
  :total-pages="totalPages"
  :total-items="totalItems"
  :items-per-page="itemsPerPage"
  item-label="sản phẩm"
  :show-items-info="false"
  min-height="70px"
  @page-change="$emit('page-change', $event)"
/>
```

### 2. 📱 Loading States

#### **Trước (Custom Loading Skeleton)**
```vue
<!-- Desktop Loading -->
<div v-if="isLoading">
  <div v-for="n in 5" class="grid grid-cols-12 gap-4 px-4 py-3 animate-pulse">
    <div class="col-span-4"><div class="h-4 bg-gray-200 rounded"></div></div>
    <div class="col-span-2"><div class="h-4 bg-gray-200 rounded"></div></div>
    <!-- ... more columns -->
  </div>
</div>

<!-- Mobile Loading -->
<div v-if="isLoading" v-for="n in 3" class="bg-white rounded-lg border p-4 animate-pulse">
  <div class="flex gap-3">
    <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
    <div class="flex-1 space-y-2">
      <div class="h-4 bg-gray-200 rounded w-3/4"></div>
      <div class="h-3 bg-gray-200 rounded w-1/2"></div>
    </div>
  </div>
</div>
```

#### **Sau (ProductTableLoading Component)**
```vue
<!-- Desktop Loading -->
<ProductTableLoading 
  v-if="isLoading" 
  :item-count="itemsPerPage" 
  :show-mobile="false" 
/>

<!-- Mobile Loading -->
<ProductTableLoading 
  v-if="isLoading" 
  :item-count="itemsPerPage" 
  :show-desktop="false" 
/>
```

### 3. 🔧 Script Changes

#### **Removed (Custom Logic)**
```javascript
// Pagination computed properties
const startItem = computed(() => {
  if (props.ListProduct.length === 0) return 0;
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  if (props.ListProduct.length === 0) return 0;
  return Math.min(
    (props.currentPage - 1) * props.itemsPerPage + props.ListProduct.length,
    props.totalItems
  );
});
```

#### **Added (Component Imports)**
```javascript
// Import UI components
import UiPagination from "~/components/ui/navigation/Pagination.vue";
import ProductTableLoading from "~/components/ui/feedback/ProductTableLoading.vue";
```

### 4. 📊 ProductTableLoading Component

#### **Features**
- **Desktop Layout**: 12-column grid matching product table structure
- **Mobile Layout**: Card-based layout với product image và info
- **Shimmer Animation**: Professional loading animation
- **Staggered Effects**: Sequential loading animation
- **Responsive**: Conditional rendering cho desktop/mobile

#### **Props Interface**
```typescript
interface Props {
  itemCount?: number;    // Number of skeleton items (default: 5)
  showMobile?: boolean;  // Show mobile skeleton (default: true)
  showDesktop?: boolean; // Show desktop skeleton (default: true)
}
```

#### **Usage Examples**
```vue
<!-- Desktop only -->
<ProductTableLoading :item-count="20" :show-mobile="false" />

<!-- Mobile only -->
<ProductTableLoading :item-count="10" :show-desktop="false" />

<!-- Both desktop and mobile -->
<ProductTableLoading :item-count="15" />
```

## Benefits

### ✅ **Consistency**
1. **Unified Design**: Same pagination experience across all pages
2. **Consistent Behavior**: Standard pagination patterns
3. **Shared Components**: Reusable UI components
4. **Maintainability**: Centralized pagination logic

### ✅ **Better UX**
1. **Professional Loading**: Enhanced skeleton animations
2. **Responsive Design**: Optimized for all devices
3. **Accessibility**: Better keyboard navigation
4. **Visual Feedback**: Clear loading states

### ✅ **Performance**
1. **Optimized Rendering**: Efficient component structure
2. **Memory Management**: Better resource usage
3. **CSS Animations**: Hardware-accelerated effects
4. **Lazy Loading**: On-demand component loading

### ✅ **Developer Experience**
1. **Simplified Code**: Less custom pagination logic
2. **Type Safety**: TypeScript interfaces
3. **Easy Customization**: Flexible props system
4. **Documentation**: Clear usage examples

## Usage Examples

### 1. **Basic Product Pagination**
```vue
<template>
  <TableManagerProduct
    :ListProduct="products"
    :isLoading="loading"
    :currentPage="pagination.currentPage"
    :totalPages="pagination.totalPages"
    :totalItems="pagination.totalItems"
    :itemsPerPage="pagination.itemsPerPage"
    @page-change="handlePageChange"
  />
</template>
```

### 2. **Custom Product Loading**
```vue
<template>
  <ProductTableLoading 
    :item-count="pagination.itemsPerPage"
    :show-mobile="isMobile"
    :show-desktop="!isMobile"
  />
</template>
```

### 3. **Enhanced Pagination**
```vue
<template>
  <UiPagination
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :items-per-page="itemsPerPage"
    item-label="sản phẩm"
    show-enhanced-pagination
    :max-visible-pages="7"
    @page-change="handlePageChange"
  />
</template>
```

## Migration Checklist

### ✅ **Completed**
- [x] Replaced custom pagination với UiPagination
- [x] Created ProductTableLoading component
- [x] Updated TableManagerProduct imports
- [x] Removed custom pagination computed properties
- [x] Added loading state integration
- [x] Updated responsive behavior
- [x] Enhanced accessibility support

### 🔄 **Page Structure**
```vue
<template>
  <div class="bg-white h-full flex flex-col">
    <!-- Desktop Layout -->
    <div class="hidden md:flex flex-col h-full">
      <!-- Table Content -->
      <div class="flex-1 overflow-y-auto">
        <!-- Product Items or Loading -->
        <ProductTableLoading v-if="isLoading" />
        <ProductItems v-else />
      </div>
      
      <!-- Desktop Pagination -->
      <UiPagination v-if="products.length || isLoading" />
    </div>

    <!-- Mobile Layout -->
    <div class="md:hidden h-full flex flex-col">
      <!-- Mobile Content -->
      <div class="flex-1 overflow-y-auto">
        <!-- Mobile Cards or Loading -->
        <ProductTableLoading v-if="isLoading" :show-desktop="false" />
        <ProductCards v-else />
      </div>
      
      <!-- Mobile Pagination -->
      <UiPagination v-if="products.length || isLoading" :show-items-info="false" />
    </div>
  </div>
</template>
```

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers

## Future Enhancements

### Potential Features
1. **Enhanced Filtering**: Advanced product filters với pagination
2. **Bulk Actions**: Multi-select với pagination support
3. **Export Functions**: Export current page or all pages
4. **Sorting Integration**: Column sorting với pagination
5. **Search Highlighting**: Search term highlighting trong results

### Advanced Options
1. **Virtual Scrolling**: Cho very large product catalogs
2. **Infinite Scroll**: Toggle option cho different UX
3. **Prefetching**: Preload adjacent pages
4. **Caching**: Smart caching cho visited pages
5. **Analytics**: Track pagination usage patterns

## Testing

### Functionality Tests
- [ ] Pagination navigation works correctly
- [ ] Loading states display properly
- [ ] Search resets to page 1
- [ ] Page size changes work
- [ ] Empty states show correctly

### Responsive Tests
- [ ] Desktop table layout
- [ ] Mobile card layout
- [ ] Pagination controls responsive
- [ ] Touch interactions work
- [ ] Loading animations smooth

### Performance Tests
- [ ] Fast page transitions
- [ ] Memory usage stable
- [ ] No layout shifts
- [ ] Smooth animations

## Kết luận

Product page pagination migration mang lại:
- **Consistent UX** across all pages trong application
- **Better performance** với optimized components
- **Enhanced accessibility** với standard patterns
- **Easier maintenance** với shared components
- **Professional appearance** với polished animations

Migration này tạo foundation tốt cho product management và có thể dễ dàng extend cho future requirements.
