<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <h2 class="text-lg font-bold text-center">Tạo topic mới</h2>
      <!-- Phần nội dung -->
      <div class="mb-2" v-if="dataAppId.length">
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40 font-semibold"
            >Ch<PERSON><PERSON> kênh</label
          >
          <select
            id="template"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="appId"
          >
            <option v-for="app in dataAppId" :key="app.id" :value="app.id">
              {{ app?.name }}
            </option>
          </select>
        </div>
      </div>
      <div v-else class="text-sm text-red-500 mb-2">
        T<PERSON> chức chưa c<PERSON>u hình appId
      </div>
      <textarea
        rows="3"
        id="note"
        class="py-1 px-2 w-full text-base rounded outline-none border bg-secondary"
        placeholder="Nội dung tin nhắn..."
        v-model="message"
      ></textarea>
      <div class="flex justify-end">
        <button
          @click="confirm"
          class="text-white bg-primary px-2 py-1 rounded"
          :disabled="!dataAppId.length || !appId"
          :class="{
            'opacity-50 cursor-not-allowed':
              !dataAppId.length || !appId || !appId,
          }"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from "vue";

const props = defineProps(["title", "message", "isHideButton"]);
const emit = defineEmits(["confirm", "cancel"]);
const isVisible = ref(true);
const message = ref();

const confirm = () => {
  emit("confirm", message.value, appId.value);
  isVisible.value = false;
};

const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const dataAppId = ref([]);
const appId = ref(null);

onMounted(() => {
  const storedAppId = JSON.parse(localStorage.getItem("appId") || "[]");
  if (storedAppId.length > 0) {
    dataAppId.value = storedAppId;
    appId.value = dataAppId.value[0]?.id;
  }
});
</script>

<style scoped></style>
