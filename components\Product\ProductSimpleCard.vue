<template>
  <div class="rounded-lg py-2 px-1">
    <div class="flex items-center gap-3">
      <!-- Product Image -->
      <div class="flex-shrink-0">
        <NuxtImg
          :src="image || 'https://placehold.co/48'"
          alt="Product image"
          class="object-cover w-12 h-12 rounded-lg"
          width="48"
          height="48"
          placeholder="Product image"
          loading="lazy"
          preload
        />
      </div>

      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <!-- Product Title -->
        <h4 class="font-medium text-gray-900 text-sm leading-5 mb-1 truncate">
          {{ product?.variant?.title || "Sản phẩm" }}
        </h4>

        <!-- Product Details -->
        <div>
          <!-- ID & SKU -->
          <div class="flex items-center gap-3 text-xs text-gray-600">
            <div class="flex items-center gap-1">
              <span class="font-medium">ID:</span>
              <span class="font-mono">{{ product?.variant?.id || "N/A" }}</span>
            </div>
            <div class="flex items-center gap-1">
              <span class="font-medium">SKU:</span>
              <span class="font-mono">{{
                product?.variant?.sku || "N/A"
              }}</span>
            </div>
          </div>

          <!-- Quantity × Price | VAT & Total -->
          <div
            class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2"
          >
            <!-- Left: Quantity × Price -->
            <div class="flex items-center gap-2 text-sm">
              <span class="font-medium text-gray-700">{{
                product?.quantity || 0
              }}</span>
              <span class="text-gray-400">×</span>
              <span class="">
                {{
                  isItemProductInvoice
                    ? formatCurrency(product?.realPriceSell?.amount)
                    : formatCurrency(product?.variant?.price?.amount)
                }}
              </span>
            </div>

            <!-- Right: VAT & Total -->
            <div class="flex items-center justify-between sm:justify-end gap-2">
              <!-- VAT Info -->
              <div
                v-if="product?.vatRate?.amount"
                class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded flex-shrink-0"
              >
                VAT {{ product?.vatRate?.amount || 0 }}%
              </div>
              <!-- Total Price -->
              <span class="text-sm  flex-shrink-0">
                {{
                  formatCurrency(
                    (product?.quantity || 0) *
                      (isItemProductInvoice
                        ? product?.realPriceSell?.amount
                        : product?.variant?.price?.amount)
                  )
                }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Edit Button (if needed) -->
      <div v-if="isItemProductInvoice" class="flex-shrink-0">
        <button
          @click="toogleOpenEditPopup"
          class="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Popup -->
  <EditItemInvoicePopup
    v-if="isOpenEditItemInvoice"
    @cancel="toogleOpenEditPopup"
    @confirm="toogleOpenEditPopup"
  />
</template>

<script setup>
const props = defineProps(["product", "isItemProductInvoice"]);
const emit = defineEmits(["toogleEditItemProductInvoice"]);
const image = ref("");
const { getImageProducrUrl } = usePortal();
onMounted(async () => {
  const url = getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
  image.value = url;
});
const isOpenEditItemInvoice = ref(false);
const toogleOpenEditPopup = () => {
  isOpenEditItemInvoice.value = !isOpenEditItemInvoice.value;
};
</script>
