<template>
  <div class="relative">
    <div>
      <div class="float-right">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 705 686"
          fill="none"
          :class="svgClass"
        >
          <path
            d="M484.218 -859L1640.18 -277.939L1224.36 549.299C1160.32 676.713 1048.67 506.259 974.321 516.596C916.941 524.574 843.817 605.178 738.713 398.165C633.608 191.152 397.006 336.959 388.001 192.638C375.343 -10.2395 29.6827 128.276 68.3966 -31.7629L484.218 -859Z"
            fill="#3F51B5"
          />
          <path
            d="M343.611 192.85C348.586 276.9  84 383.863 296.51 482.889 300.572"
            :stroke="pathStrokeColor1"
          />
          <path
            d="M328.016 230.498C327.96 272.989 369.948 311.327 427.396 315.05"
            :stroke="pathStrokeColor2"
          />
        </svg>
      </div>
    </div>
    <div class="absolute top-12 lg:left-12 left-5">
      <div class="text-[#3F51B5] flex flex-col lg:gap-32 gap-20">
        <div :class="titleClass">
          pos online
          <!-- {{ Cookies.get("info-store") }} -->
        </div>
        <div class="text-[#3F51B5] flex flex-col gap-[14px]">
          <div :class="subtitleClass">
            {{ title }}
            <span
              class="inline-block w-[7px] h-[7px] bg-white rounded-full ml-2"
            ></span>
          </div>
          <div class="pr-8 mt-2 lg:text-2xl text-sm font-medium">
            {{ description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

// Define props
const props = defineProps({
  title: String,
  description: String,
});
const svgClass = computed(() => {
  return "lg:w-[605px] lg:h-[400px] w-[270px] h-[280px]";
});
const pathStrokeColor1 = "#3F51B5";
const pathStrokeColor2 = "#808080";
const titleClass = computed(() => {
  return "lg:text-4xl text-xl font-bold tracking-wide uppercase";
});
const subtitleClass = computed(() => {
  return "lg:text-4xl text-xl font-bold";
});
</script>
