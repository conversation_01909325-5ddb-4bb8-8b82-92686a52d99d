<template>
  <div
    class="bg-card relative text-card-foreground p-2 rounded-lg border flex items-center overflow-hidden"
  >
    <label for="product1" class="cursor-pointer flex items-center w-full ml-2">
      <img
        :src="image || 'https://placehold.co/80'"
        alt="Product Image"
        class="flex-shrink-0 w-16 h-16 rounded-lg object-contain"
        loading="lazy"
      />
      <div class="ml-4 flex-1">
        <div class="flex items-center justify-between">
          <h3 class="text-sm line-clamp-2 font-semibold">
            {{ product.orderItemName }}
          </h3>
        </div>
        <div class="flex items-center justify-between mt-2">
          <p class="text-primary font-semibold">
            {{ formatCurrency(product.realPriceSell.amount) }}
          </p>
          <div class="flex items-center">
            <button
              v-if="!isDecreaseQuantity"
              @click.prevent.stop="decreaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
            >
              -
            </button>
            <button
              @click.prevent.stop="handleQuantity"
              v-else
              class="w-6 h-6 flex items-center justify-center border text-primary rounded-md"
            >
              -
            </button>
            <input
              type="number"
              min="1"
              v-model="product.currentQuantity"
              @input="updateTotalPrice"
              @blur="updateQuantity"
              class="w-10 h-6 text-center border border-input rounded-md mx-2"
            />
            <button
              @click.prevent.stop="increaseQuantity"
              class="w-6 h-6 flex items-center justify-center border text-primary-foreground rounded-md"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </label>

    <div
      v-if="isUpdating"
      class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center"
    >
      <svg
        class="animate-spin h-5 w-5 text-primary"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0c4.418 0 8 3.582 8 8h-2c0-3.314-2.686-6-6-6V0c-3.314 0-6 2.686-6 6H4z"
        ></path>
      </svg>
    </div>
  </div>
</template>

<script setup>
import debounce from "lodash/debounce";
const productStore = useProductStore();
const props = defineProps({
  product: Object,
});
</script>
