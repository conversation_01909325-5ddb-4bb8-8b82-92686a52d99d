<template>
  <div>
    <div class="w-full bg-white rounded p-2 flex flex-col">
      <div class="flex items-center justify-between mb-4">
        <div class="text-primary font-bold"><PERSON><PERSON><PERSON> chỉ nh<PERSON>n hàng</div>
        <div class="flex gap-4">
          <div>
            <span
              class="text-primary font-bold"
              @click="handleCreateShippingAddress"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 4.5v15m7.5-7.5h-15"
                />
              </svg>
            </span>
          </div>
          <div>
            <span @click="handleListShippingAddress"
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6 text-primary"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
      <div
        v-if="orderStore.dataShippingAddress && orderStore.dataDefaultAddress"
      >
        <ShippingAddressDefault
          :shippingAddress="orderStore.dataDefaultAddress"
          :customer="customer"
        ></ShippingAddressDefault>
      </div>
      <div v-else class="text-sm text-red-600 flex items-center justify-center">
        Không có địa chỉ giao hàng vui vòng tạo mới!
      </div>
    </div>
    <div v-if="customer" class="bg-white rounded p-2 flex flex-col gap-2">
      <ShippingService></ShippingService>
    </div>
    <ModalCreateShippingAddress
      v-if="isShowCreateShippingAddress"
      @closeModalCreateShippingAddress="closeModalCreateShippingAddress"
      :customer="customer"
    ></ModalCreateShippingAddress>
    <ModalManageAddressShip
      v-if="isShowListShippingAddress"
      @closeModalManageAddressShip="closeModalEditShippingAddress"
      :customer="customer"
    ></ModalManageAddressShip>
    <ModalCreateOrder v-if="orderStore.isAlert"></ModalCreateOrder>
  </div>
</template>
<script setup lang="ts">
const route = useRoute();
const { valueShippingAddress } = useOrderStore();
const orderStore = useOrderStore();

const customer = computed(() => orderStore.customerInOrder);
const isDelivered = ref<string>(valueShippingAddress);
const isShowShippingAddress = ref<boolean>(false);
const isShowCreateShippingAddress = ref<boolean>(false);
const isShowListShippingAddress = ref<boolean>(false);
const handleCreateShippingAddress = () => {
  isShowCreateShippingAddress.value = true;
};
const closeModalCreateShippingAddress = (value: boolean) => {
  isShowCreateShippingAddress.value = value;
};
const handleListShippingAddress = () => {
  isShowListShippingAddress.value = true;
};
const closeModalEditShippingAddress = (value: boolean) => {
  isShowListShippingAddress.value = value;
};
const setValueShippingAddress = (value: string) => {
  isShowShippingAddress.value = false;
  isDelivered.value = value;
};
const emits = defineEmits(["isDelivery"]);
watch(isDelivered, async (newValue) => {
  orderStore.valueShippingAddress = newValue;
  if (newValue === "delivery") {
    emits("isDelivery", true);
    isShowShippingAddress.value = true;
  } else {
    emits("isDelivery", false);
    isShowShippingAddress.value = false;
  }
});
onMounted(async () => {
  orderStore.valueShippingAddress = "at-counter";
});
watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    if (oldVal) {
      if (newVal !== oldVal) {
        orderStore.valueShippingAddress = "at-counter";
      }
    }
  },
  { immediate: true }
);
const handleClick = (value: string) => {
  if (customer.value) {
    isDelivered.value = value;
    orderStore.valueShippingAddress = value;
  } else {
    useNuxtApp().$toast.warning(
      "Vui lòng thêm khách hàng trước khi chọn loại đơn vận chuyển"
    );
  }
};
</script>
