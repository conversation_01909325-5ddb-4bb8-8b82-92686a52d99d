<template>
  <div
    class="bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
  >
    <!-- Customer Header -->
    <div
      class="bg-gradient-to-r from-primary/5 to-primary/10 px-4 py-3 border-b border-gray-100"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <!-- Avatar -->
          <div
            class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>

          <!-- Customer Info -->
          <div>
            <h3 class="font-semibold text-gray-800 capitalize">
              {{ customer?.name }}
            </h3>
            <p class="text-xs text-gray-500">#{{ customer?.id }}</p>
          </div>
        </div>

        <!-- Action Button -->
        <button
          @click="handleClick"
          :disabled="isLoading"
          class="flex items-center space-x-1 text-primary hover:text-primary/80 font-medium text-sm transition-colors duration-200 disabled:opacity-50"
        >
          <span v-if="!isLoading">Chi tiết</span>
          <span v-else>Đang tải...</span>
          <svg
            v-if="!isLoading"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
          <div
            v-else
            class="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"
          ></div>
        </button>
      </div>
    </div>

    <!-- Customer Details -->
    <div class="p-4 space-y-3">
      <!-- Contact Information -->
      <div class="space-y-2">
        <!-- Phone -->
        <div class="flex items-center space-x-2">
          <div
            class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3 w-3 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
              />
            </svg>
          </div>
          <span class="text-sm text-gray-700 font-medium">{{
            customer?.phone
          }}</span>
        </div>

        <!-- Email -->
        <div v-if="customer?.email" class="flex items-center space-x-2">
          <div
            class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3 w-3 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
          </div>
          <span class="text-sm text-gray-700 truncate">{{
            customer?.email
          }}</span>
        </div>

        <!-- Address -->
        <div v-if="customer?.address" class="flex items-start space-x-2">
          <div
            class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center mt-0.5"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3 w-3 text-orange-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>
          <span class="text-sm text-gray-700 line-clamp-2">{{
            customer?.address
          }}</span>
        </div>

        <!-- Birth Date -->
        <div v-if="customer?.birthDate" class="flex items-center space-x-2">
          <div
            class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-3 w-3 text-purple-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <span class="text-sm text-gray-700">{{
            formatTimestampV2(customer?.birthDate)
          }}</span>
        </div>
      </div>

      <!-- Customer Stats and Actions -->
      <div
        class="flex items-center justify-between pt-2 border-t border-gray-100"
      >
        <div class="flex items-center space-x-2">
          <!-- Member Level Badge -->
          <span
            v-if="customer?.memberLevel"
            :class="handleMemberLevel(customer?.memberLevel)"
          >
            {{ customer.memberLevel }}
          </span>
          <span
            v-else
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
          >
            Thành viên
          </span>
        </div>

        <!-- Checkbox and Quick Actions -->
        <div class="flex items-center space-x-3">
          <!-- Quick Actions -->
          <div class="flex items-center space-x-1">
            <button
              class="p-1 text-gray-400 hover:text-green-600 transition-colors duration-200"
              title="Gọi điện"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
            </button>
            <button
              v-if="customer?.email"
              class="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
              title="Gửi email"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </button>
          </div>

          <!-- Checkbox -->
          <input
            type="checkbox"
            v-model="isChecked"
            class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2 cursor-pointer"
          />
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/80 flex items-center justify-center"
    >
      <div class="flex flex-col items-center space-y-2">
        <div
          class="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"
        ></div>
        <span class="text-sm text-gray-600">Đang tải...</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  customer: Object,
});
const { storeId, orgId } = useTabContext();

const isLoading = ref(false);
const handleClick = async () => {
  let showLoadingTimeout: ReturnType<typeof setTimeout> | null = null;
  showLoadingTimeout = setTimeout(() => {
    isLoading.value = true;
  }, 200);
  await navigateTo(
    `/customer/detail?orgId=${orgId.value}&storeId=${storeId.value}&customerId=${props.customer?.id}`
  );
  if (showLoadingTimeout) {
    clearTimeout(showLoadingTimeout);
  }
  isLoading.value = false;
};
const handleMemberLevel = (memberLevel: string) => {
  switch (memberLevel) {
    case "MEMBER":
      return "text-white bg-primary px-2 py-[3px] rounded-lg";
    case "SILVER":
      return "text-gray-500 bg-gray-300 px-2 py-[3px] rounded-lg ";
    case "GOLD":
      return "text-yellow-300 bg-yellow-100 px-2 py-[3px] rounded-lg ";
    case "PLATINUM":
      return "text-green-500 bg-green-100 px-2 py-[3px] rounded-lg";
    default:
      return "";
  }
};
const isChecked = ref(false);
const customerStore = useCustomerStore();
watch(
  () => isChecked.value,
  (newVal, oldVal) => {
    if (newVal) {
      //
      // kiểm tra nếu mảng lớn 0 và đã có phần tử
      if (customerStore.listCustomerAction?.length > 0) {
        const res = customerStore.listCustomerAction.find(
          (item: any) => item.id === props.customer?.id
        );
        if (!res) {
          customerStore.addListCustomerAction(props.customer);
        }
      } else {
        customerStore.addListCustomerAction(props.customer);
      }

      //
    } else {
      customerStore.removeCustomerListAction(props.customer);
    }
  }
);
onMounted(() => {
  if (customerStore.listCustomerAction?.length > 0) {
    const res = customerStore.listCustomerAction.find(
      (item: any) => item.id === props.customer?.id
    );
    if (res) {
      isChecked.value = true;
    }
  }
});
watch(
  () => customerStore.listCustomerAction?.length,
  (newVal, old) => {
    if (customerStore.listCustomerAction?.length > 0) {
      const res = customerStore.listCustomerAction.find(
        (item: any) => item.id === props.customer?.id
      );
      if (res) {
        isChecked.value = true;
      }
    }
  }
);
</script>

<style scoped>
/* Customer Card Styling */
.bg-white {
  position: relative;
}

/* Hover effects */
.bg-white:hover {
  transform: translateY(-2px);
}

/* Loading overlay positioning */
.absolute.inset-0 {
  border-radius: inherit;
}

/* Line clamp for long text */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions for all interactive elements */
button,
input {
  transition: all 0.2s ease-in-out;
}

/* Icon hover effects */
button:hover svg {
  transform: scale(1.1);
}

/* Member level badge animations */
.inline-flex {
  transition: all 0.2s ease-in-out;
}

/* Quick action button hover effects */
button[title]:hover {
  transform: scale(1.1);
}

/* Checkbox styling */
input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Gradient header animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .space-x-3 {
    gap: 0.5rem;
  }

  .p-4 {
    padding: 1rem;
  }
}
</style>
