<template>
  <div
    v-if="dataOrder"
    :class="
      isManagerCustomer
        ? 'relative bg-white border mb-2'
        : 'relative bg-white mb-2'
    "
  >
    <div class="space-y-2">
      <div class="bg-card p-2">
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-1">
            <div class="flex items-center">
              <span class="font-semibold text-sm">#{{ dataOrder.id }}</span>
              <span :class="statusClass">{{
                dataOrder.statusDescription
              }}</span>
            </div>
          </div>
          <div
            @click="handleNavigate"
            class="flex gap-1 items-center text-sm text-primary cursor-pointer"
          >
            Chi tiết
          </div>
        </div>
        <div class="text-sm w-full flex items-center justify-between gap-3">
          <div class="flex items-center gap-1">
            <div class="flex gap-1 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                />
              </svg>
              <span class="text-gray-500">{{
                dataOrder?.order?.ownerName
              }}</span>
            </div>
            <div class="flex gap-1 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                />
              </svg>
              <span class="text-gray-500">
                {{ dataOrder?.order?.ownerPhone }}
              </span>
            </div>
          </div>
          <div class="text-sm py-2 flex items-center gap-3">
            <div class="flex items-center gap-[2px] text-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
              <span class="text-gray-500">{{
                formatTimestampV7(dataOrder.order.createdStamp)
              }}</span>
            </div>
          </div>
        </div>

        <ul class="list-disc list-inside">
          <li
            class="text-gray-700 text-sm sm:text-base flex items-center gap-2"
            v-for="product in dataOrder.activeOrderItemProfiles"
            :key="product.id"
          >
            <ProductSimpleCard :product="product.orderLineItem" />
          </li>
        </ul>

        <div
          class="flex items-center justify-between text-sm border-t mt-2 pt-2"
        >
          Tổng tiền:
          <span class="font-semibold">
            {{ formattedTotalPrice }}
          </span>
        </div>
        <div class="flex items-center justify-between text-sm">
          Đã thanh toán:
          <span class="text-red-500 font-semibold">
            {{ formatCurrency(dataOrder?.totalAlreadyPaid) }}
          </span>
        </div>
        <div class="flex items-center justify-between text-sm">
          Trạng thái thanh toán:
          <div :class="paymentStatusClass">
            {{ dataOrder.financialStatusDescription }}
          </div>
        </div>
        <!-- action  icon -->
        <div
          v-if="!isManagerCustomer"
          class="flex justify-between items-center text-sm"
        >
          <!-- action order -->
          <div class="border-t mt-2 pt-2 w-full flex justify-between">
            <div class="flex items-center gap-[2px] text-sm"></div>
            <div class="flex items-center gap-2">
              <NuxtLink
                v-if="dataOrder.remainTotal > 0"
                :to="`/payment?orderId=${dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`"
                class="cursor-pointer rounded-md flex items-center gap-1 bg-[#6D31EDFF] text-white px-2 py-1"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                  />
                </svg>
                Thanh toán
              </NuxtLink>
              <div
                @click="handlePrintOrder"
                class="flex items-center bg-primary hover:cursor-pointer text-white px-2 py-1 rounded-md"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
                  />
                </svg>
                <span>{{
                  getPrintCount(dataOrder?.order?.customAttributes)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
const { searchEmployes } = useOrder();
const props = defineProps({
  dataOrder: Object,
  isManagerCustomer: Boolean,
});

const isOpen = ref<Boolean>(false);

const statusClass = computed(() =>
  getOrderStatusClass(props.dataOrder?.status, "semibold")
);
const paymentStatusClass = computed(() =>
  getPaymentStatusClass(props.dataOrder?.financialStatusDescription, "simple")
);
const formattedTotalPrice = computed(() =>
  formatCurrency(props.dataOrder?.order?.totalPrice?.amount)
);

// Import utilities
import {
  getOrderStatusClass,
  getPaymentStatusClass,
} from "~/utils/statusHelpers";
import { getPrintCount } from "~/utils/orderHelpers";
const handleClickPayment = () => {
  navigateTo(
    `/payment?orderId=${props.dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
  );
};
const { printOrderHTML } = useOrder();
import printJS from "print-js";

const handlePrintOrder = async () => {
  try {
    const response = await printOrderHTML(props.dataOrder?.id);
    const data = response.data;
    const printContainer = document.createElement("div");
    printContainer.id = "print-test";
    printContainer.innerHTML = `${data}`;

    printContainer.style.width = "80mm";
    printContainer.style.overflow = "visible";
    printContainer.style.height = "auto";

    document.body.appendChild(printContainer);

    printJS({
      printable: "print-test",
      type: "html",
      scanStyles: true,
      maxWidth: 80 * 3.78,
      targetStyles: ["*"],
    });

    document.body.removeChild(printContainer);
  } catch (error) {
    console.error("Error printing the order:", error);
    throw error;
  }
};

const route = useRoute();
const handleNavigate = () => {
  navigateTo(
    `/buy/detail?orderId=${props.dataOrder?.id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
  );
};
// Function moved to utilities
</script>
