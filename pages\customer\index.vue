<template>
  <div class="customer-page mx-2 text-sm">
    <!-- <PERSON>er -->
    <div class="mb-2 bg-white rounded">
      <div class="px-2 py-4 border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-800">
                Quản lý khách hàng
              </h1>
              <p class="text-sm text-gray-600">
                Tìm kiếm và quản lý thông tin khách hàng
              </p>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="hidden lg:flex items-center space-x-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary">
                {{ customerStore.listCustomer?.length || 0 }}
              </div>
              <div class="text-xs text-gray-500">Tổng KH</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">
                {{
                  customerStore.listCustomer?.filter(
                    (c) => c.status === "active"
                  )?.length ||
                  customerStore.listCustomer?.length ||
                  0
                }}
              </div>
              <div class="text-xs text-gray-500">Hiển thị</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Search & Filter Section -->
      <div class="p-2">
        <!-- Desktop Layout -->
        <div class="hidden md:flex items-center space-x-2 mb-2">
          <!-- Search Input -->
          <div class="flex-1 relative">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Tìm kiếm theo mã, tên, email, số điện thoại..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg transition-all duration-200 outline-none"
              v-model="keyword"
              @keydown.enter="search"
            />
          </div>

          <!-- Date Range Picker -->
          <div class="w-64">
            <flat-pickr
              class="w-full px-4 py-2 border border-gray-300 rounded-lg outline-none transition-all duration-200"
              placeholder="Chọn khoảng thời gian"
              :config="datePickerConfig"
              v-model="dateRange"
            />
          </div>

          <!-- Membership Level Filter -->
          <div class="w-48">
            <InputField
              type="select"
              v-model="selectedLevel"
              :options="membershipLevels"
              :defaultText="'Tất cả hạng thành viên'"
              :selectClass="'w-full px-2 py-2 border border-gray-300 rounded-lg outline-none transition-all duration-200 bg-white'"
            />
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="hidden md:flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <!-- Search Button -->
            <button
              @click="search"
              :disabled="isSearching"
              class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-all duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                v-if="!isSearching"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <div
                v-else
                class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
              ></div>
              <span>{{ isSearching ? "Đang tìm..." : "Tìm kiếm" }}</span>
            </button>

            <!-- Reset Button -->
            <button
              @click="reset"
              class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-all duration-200 flex items-center space-x-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>Đặt lại</span>
            </button>
          </div>

          <!-- Add Customer Button -->
          <button
            @click="isModalVisible = true"
            class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-all duration-200 flex items-center space-x-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            <span>Thêm khách hàng</span>
          </button>
        </div>
        <!-- Mobile Layout -->
        <div class="md:hidden space-y-4">
          <!-- Search Input -->
          <div class="relative">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Tìm kiếm khách hàng..."
              class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              v-model="keyword"
              @keydown.enter="search"
            />
          </div>

          <!-- Filters Row -->
          <div class="grid grid-cols-1 gap-3">
            <flat-pickr
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Chọn khoảng thời gian"
              :config="datePickerConfig"
              v-model="dateRange"
            />
            <InputField
              type="select"
              v-model="selectedLevel"
              :options="membershipLevels"
              :defaultText="'Tất cả hạng thành viên'"
              :selectClass="'w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white'"
            />
          </div>

          <!-- Mobile Action Buttons -->
          <div class="flex space-x-3">
            <button
              @click="search"
              :disabled="isSearching"
              class="flex-1 bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50"
            >
              <svg
                v-if="!isSearching"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <div
                v-else
                class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
              ></div>
              <span>{{ isSearching ? "Đang tìm..." : "Tìm kiếm" }}</span>
            </button>
            <button
              @click="reset"
              class="px-4 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
            <button
              @click="isModalVisible = true"
              class="px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Customer Data Table -->
    <div class="bg-white rounded">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h2 class="text-lg font-semibold text-gray-800">
              Danh sách khách hàng
            </h2>
            <div
              v-if="customerStore.listCustomer?.length"
              class="text-sm text-gray-500"
            >
              Hiển thị {{ customerStore.listCustomer.length }} khách hàng
            </div>
          </div>

          <!-- Table Actions -->
          <div class="flex items-center space-x-3">
            <!-- Export Button -->
            <button
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              title="Xuất Excel"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </button>

            <!-- Refresh Button -->
            <button
              @click="refreshData"
              :disabled="customerStore.loading"
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200 disabled:opacity-50"
              title="Làm mới dữ liệu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                :class="{ 'animate-spin': customerStore.loading }"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Table Content -->
      <div class="overflow-hidden">
        <!-- Desktop Table -->
        <div class="hidden md:block">
          <div class="h-[58vh] overflow-y-auto scroll-smooth">
            <table class="w-full">
              <TableHeader
                :headers="headers"
                :visibleColumns="visibleColumnsCustomer"
                :isCheckBox="true"
                class="sticky top-0 z-10 bg-white"
              />
              <tbody class="bg-white divide-y divide-gray-200">
                <!-- Loading State -->
                <tr v-if="customerStore.loading">
                  <td
                    :colspan="headers.length + 1"
                    class="px-6 py-12 text-center"
                  >
                    <div class="flex flex-col items-center space-y-3">
                      <div
                        class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"
                      ></div>
                      <p class="text-gray-500">
                        Đang tải dữ liệu khách hàng...
                      </p>
                    </div>
                  </td>
                </tr>

                <!-- Empty State -->
                <tr v-else-if="!customerStore.listCustomer?.length">
                  <td
                    :colspan="headers.length + 1"
                    class="px-6 py-12 text-center"
                  >
                    <div class="flex flex-col items-center space-y-3">
                      <div
                        class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-8 w-8 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">
                          Không tìm thấy khách hàng
                        </h3>
                        <p class="text-gray-500">
                          Thử thay đổi bộ lọc hoặc thêm khách hàng mới
                        </p>
                      </div>
                      <button
                        @click="isModalVisible = true"
                        class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200"
                      >
                        Thêm khách hàng đầu tiên
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Data Rows -->
                <TableRowCustomer
                  v-else
                  :headers="headers"
                  :listCustomerResponse="customerStore.listCustomer"
                  :itemsPerPage="10"
                  :loading="customerStore.loading"
                />
              </tbody>
            </table>
          </div>
        </div>

        <!-- Mobile Cards -->
        <div class="md:hidden">
          <div class="h-[600px] overflow-y-auto scroll-smooth">
            <div v-if="customerStore.loading" class="p-6 text-center">
              <div class="flex flex-col items-center space-y-3">
                <div
                  class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"
                ></div>
                <p class="text-gray-500">Đang tải dữ liệu...</p>
              </div>
            </div>

            <!-- Empty State -->
            <div
              v-else-if="!customerStore.listCustomer?.length"
              class="p-6 text-center"
            >
              <div class="flex flex-col items-center space-y-3">
                <div
                  class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-8 w-8 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-medium text-gray-900 mb-1">
                    Không có khách hàng
                  </h3>
                  <p class="text-gray-500 text-sm">
                    Thử thay đổi bộ lọc hoặc thêm mới
                  </p>
                </div>
                <button
                  @click="isModalVisible = true"
                  class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200"
                >
                  Thêm khách hàng
                </button>
              </div>
            </div>

            <div v-else class="p-4 space-y-3">
              <itemCustomer
                v-for="customer in customerStore.listCustomer"
                :key="customer.id"
                :customer="customer"
              />
              <!-- Bottom padding for better scrolling experience -->
              <div class="h-4"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ModalCreateCustomer
      v-if="isModalVisible"
      @closeModelUser="handleToogleModal"
      :isManagerCustomer="true"
    />
    <SendVoucher
      v-if="isOpenTopicPopup"
      @cancel="handleTooglePopup"
      @confirm="handleSendMessage"
    ></SendVoucher>
  </div>
</template>

<script setup lang="ts">
// Reactive Data
const isModalVisible = ref(false);
const isSearching = ref(false);

// Stores
const customerStore = useCustomerStore();
const orderStore = useOrderStore();

// Constants
const membershipLevels = [
  { code: "MEMBER", value: 1, name: "Thành viên" },
  { code: "SILVER", value: 2, name: "Bạc" },
  { code: "GOLD", value: 3, name: "Vàng" },
  { code: "PLATINUM", value: 4, name: "Kim cương" },
];
const headers = [
  "STT",
  "Mã Khách Hàng",
  "Tên",
  "Số điện thoại",
  "Hạng",
  "Ngày sinh",
  "Email",
  "Địa chỉ",
];
// follow Oa
const isFollowOa = ref();
const listFollowOa = ref<any>([]);
import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
import type { Options } from "flatpickr/dist/types/options"; // Đúng kiểu dữ liệu
const visibleColumnsCustomer = ref(headers.map(() => true));
const dateRange = ref<string[]>([]);
const datePickerConfig = ref<Partial<Options>>({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: async (selectedDates: any) => {
    if (selectedDates.length === 2) {
      let startDate = new Date(selectedDates[0]);
      let endDate = new Date(selectedDates[1]);
      if (startDate.toDateString() === endDate.toDateString()) {
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
      } else {
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);
        endDate.setDate(endDate.getDate() + 1);
      }
      handleChangeDate(endDate.getTime(), startDate.getTime());
    }
  },
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Danh Sách Khách hàng",
});
useHead({
  title: "Danh sách khách hàng",
  meta: [
    {
      name: "description",
      content: "Danh sách khách hàng",
    },
  ],
});
//
const selectedLevel = ref();
const keyword = ref();
// const currentPage = ref(1);
const route = useRoute();

const options = reactive({
  keyword: keyword.value,
  currentPage: 0,
  memberLevel: selectedLevel.value,
  followSocialAppId: "",
  pageSize: 10,
  startCreatedDate: null as string | null,
  endCreatedDate: null as string | null,
});
const zaloId = ref();
const handleGetAppId = () => {
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  zaloId.value = appId.find((app: any) => app.channelType === "ZALO");
  const data = [{ name: "Đã follow", value: "" }];
  listFollowOa.value = data;
};
onMounted(async () => {
  // gán lại orderDetail = null
  orderStore.orderDetail = null;
  handleGetAppId();
});
const router = useRouter();

// Methods
const search = async () => {
  isSearching.value = true;
  try {
    customerStore.isCheckGlobal = false;
    options.keyword = keyword.value;
    options.memberLevel = selectedLevel.value ? selectedLevel.value : null;
    options.currentPage = 1;

    if (isFollowOa.value === "Đã follow") {
      if (zaloId.value?.id) {
        options.followSocialAppId = zaloId.value?.id;
      } else {
        useNuxtApp().$toast?.warning("Tổ chức chưa cấu hình appId");
        return;
      }
    } else {
      options.followSocialAppId = "";
    }

    console.log("options", options);
    if (
      keyword.value ||
      selectedLevel.value ||
      isFollowOa.value ||
      dateRange.value
    ) {
      await customerStore.handleGetListCustomer(options);
    }
  } catch (error) {
    console.error("Search error:", error);
    useNuxtApp().$toast?.error("Có lỗi xảy ra khi tìm kiếm");
  } finally {
    isSearching.value = false;
  }
};

const refreshData = async () => {
  try {
    await customerStore.handleGetListCustomer(options);
    useNuxtApp().$toast?.success("Đã làm mới dữ liệu");
  } catch (error) {
    console.error("Refresh error:", error);
    useNuxtApp().$toast?.error("Có lỗi xảy ra khi làm mới dữ liệu");
  }
};
const reset = () => {
  selectedLevel.value = null;
  keyword.value = "";
  customerStore.isCheckGlobal = false;
  customerStore.listCustomerAction = [];
  options.currentPage = 0;
  options.memberLevel = null;
  options.followSocialAppId = "";
  customerStore.listCustomer = [];
  dateRange.value = [];
  isFollowOa.value = null;
  options.startCreatedDate = null;
  options.endCreatedDate = null;
};

const handleToogleModal = () => {
  isModalVisible.value = !isModalVisible.value;
};
////
const scrollContainer = ref(null);
let hasMoreData = ref(true);

router.beforeEach((to, from, next) => {
  if (to.path === `/customer`) {
    keyword.value = "";
    selectedLevel.value = "";
    options.followSocialAppId = "";
    customerStore.listCustomer = [];
    orderStore.orderDetail = null;
  }
  next();
});
//
const isOpenTopicPopup = ref(false);
const handleTooglePopup = () => {
  isOpenTopicPopup.value = !isOpenTopicPopup.value;
};
const { createTopic } = useCustomer();
const cusomerStore = useCustomerStore();
const listCustomerAction = computed(() => cusomerStore.listCustomerAction);
///
// send message
const { sendMessage } = useComhub();
const handleSendMessage = async (message: string) => {
  ///
  const appId = JSON.parse(localStorage.getItem("appId") || "[]");

  if (!Array.isArray(appId) || appId.length === 0) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  const zaloId = appId.find((app: any) => app.name === "ZNS");
  if (!zaloId) {
    useNuxtApp().$toast.warning(
      "Tổ chức chưa cấu hình app trao đổi khách hàng"
    );
    isOpenTopicPopup.value = false;
    return;
  }

  if (!(listCustomerAction.value?.length > 0)) {
    useNuxtApp().$toast.warning("Không có khách hàng không thể gửi");
    isOpenTopicPopup.value = false;
    return;
  }
  //
  try {
    const receivePartyIds: any = [];
    listCustomerAction.value.forEach((customer: any) => {
      receivePartyIds.push(customer.id);
    });
    await sendMessage(zaloId?.id, message, "TEXT", "SYSTEM", receivePartyIds);
  } catch (error) {
    throw error;
  }
  isOpenTopicPopup.value = false;
};
//
const handleChangeDate = (dateFrom: any, dateTo: any) => {
  options.startCreatedDate = dateTo ? new Date(dateTo).toISOString() : "";
  options.endCreatedDate = dateFrom ? new Date(dateFrom).toISOString() : "";
};
</script>

<style scoped>
/* Customer Page Styling */
.customer-page {
  min-height: 100vh;
}

/* Button hover animations */
button:hover {
  transform: translateY(-1px);
}

/* Search input focus effects */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Table row hover effects */
tbody tr:hover {
  background-color: #f8fafc;
}

/* Mobile card animations */
@media (max-width: 768px) {
  .space-y-3 > * {
    animation: slideInUp 0.3s ease-out;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stats counter animation */
.text-2xl {
  transition: all 0.3s ease;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Enhanced scrollbar styling for customer table */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
  margin: 4px 0;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scrolling performance optimization */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Hardware acceleration for better scrolling performance */
.h-\[600px\].overflow-y-auto {
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Table header shadow when scrolling */
.sticky.shadow-sm {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style>
