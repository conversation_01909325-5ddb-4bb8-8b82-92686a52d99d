<template>
  <div
    class="fixed inset-0 z-[50]"
    aria-labelledby="modal-title"
    aria-modal="true"
  >
    <div
      class="flex items-center justify-center h-screen p-0 text-center sm:block sm:p-0 overflow-auto"
    >
      <div
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        aria-hidden="true"
      ></div>
      <span
        class="hidden sm:inline-block sm:align-middle sm:h-screen"
        aria-hidden="true"
        >&#8203;</span
      >
      <div
        class="relative inline-block bg-white shadow w-full m-auto lg:w-[40%] h-full md:rounded-[0.5rem] md:h-full md:top-0"
      >
        <div
          class="flex justify-between items-center w-full p-2 rounded-t border-b bg-[#3F51B5]"
        >
          <div class="flex gap-1 items-center justify-between w-full">
            <div class="text-base text-white lg:text-lg"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bán</div>
            <button
              type="button"
              class="cursor-pointer text-gray-400 bg-transparent rounded-lg text-sm inline-flex items-center"
              @click="handleToogleModal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 16 16"
              >
                <g fill="white">
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
                  />
                  <path
                    d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
        <div class="text-left pb-[3.25rem] h-full overflow-auto md:h-full">
          <div
            class="flex flex-col w-full justify-between"
            :class="previewImages.length > 0 ? 'lg:h-full h-auto' : 'h-full'"
          >
            <div class="mx-3 flex flex-col gap-2 mb-2 h-full md:h-[80%]">
              <div class="flex flex-col">
                <div
                  v-if="previewImages.length"
                  class="mt-2 grid grid-cols-3 gap-2"
                >
                  <div
                    v-for="(image, index) in previewImages"
                    :key="index"
                    class="relative border border-primary rounded-md w-[90px]"
                  >
                    <img
                      :src="
                        image.srcPath
                          ? `https://s3-img-gw.longvan.net/img/omnichannel/${image.srcPath}`
                          : image
                      "
                      alt="Image Preview"
                      class="w-[80px] h-[80px] object-contain border rounded-sm mx-auto"
                      loading="lazy"
                    />
                    <span
                      @click="removeImage(index)"
                      class="absolute top-0 right-0 text-red-700 cursor-pointer"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
              <div
                v-if="isOpen"
                class="px-2 bottom-12 flex flex-col items-center justify-center"
              >
                <button
                  class="bg-primary px-6 py-2 rounded-md text-white my-4"
                  @click="handleTakePhoto"
                >
                  Chụp ảnh
                </button>
                <video ref="videoElement" autoplay></video>
              </div>
              <div class="md:h-full">
                <div
                  class="flex text-black gap-4 flex-col w-full justify-between"
                ></div>
                <div class="flex justify-center">
                  <div
                    @click="createCheckin"
                    class="bg-primary w-[50%] absolute bottom-3 text-center text-white py-1 rounded-md font-semibold border-2 border-primary cursor-pointer"
                  >
                    Hoàn thành
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
const isClose = ref(false);
const emit = defineEmits(["isClose"]);
const { image } = defineProps(["image"]);
const previewImages = ref([]);
const dataImage = ref([]);
const isOpen = ref(false);
const route = useRoute();
const { uploadImage, AddAttachmentForWorkEffort } = useCheckin();
const auth = useCookie("auth").value;
const checkinStore = useCheckinStore();
const handleToogleModal = () => {
  isClose.value = false;
  emit("isClose", isClose.value);
  closeCamera();
};
const handleImageUpload = (event) => {
  const files = event.target.files;
  Array.from(files).forEach((file) => {
    dataImage.value.push(file);
  });

  Array.from(files).forEach((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImages.value.push(e.target.result);
    };
    reader.readAsDataURL(file);
  });
};

const removeImage = (index) => {
  previewImages.value.splice(index, 1);
};
const createCheckin = async () => {
  handleUpdateImage(checkinStore.idImage, "create");
  closeCamera();
  emit("isClose", isClose.value);
};
const handleUpdateImage = async (id, type) => {
  dataImage.value.forEach(async (image) => {
    const res = await uploadImage(image);
    const addAttachmentRequest = [
      {
        // fileType: res?.fileType,
        // name: res?.fileName?.split("/")[0] + "-" + res?.fileName,
        // srcConfigPathId: res?.configPathID,
        srcId: res?.id,
        srcName: res?.fileName,
        srcPath: res?.filePath,
        // type: "RESULT",
      },
    ];
    await handleAddAttachmentForWorkEffort(addAttachmentRequest, id, type);
  });
  // handleToogleModal();
};
const handleAddAttachmentForWorkEffort = async (arr, id, typeFuntion) => {
  try {
    if (arr.length > 0) {
      const postAttachment = await AddAttachmentForWorkEffort(
        auth.user.id,
        id,
        arr
      );
    }
    checkinStore.getCheckinById(route.params.id);
  } catch (error) {
    throw error;
  }
};
const videoElement = ref(null);
let capturedPhoto = null;
const handleOpenCamera = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    openCamera();
  } else {
    closeCamera();
  }
};

function openCamera() {
  navigator.mediaDevices
    .getUserMedia({ video: { facingMode: "environment" } })
    .then((stream) => {
      videoElement.value.srcObject = stream;
    })
    .catch((err) => {
      console.error("Error accessing the camera:", err);
    });
}

function closeCamera() {
  const stream = videoElement?.value?.srcObject;
  if (stream) {
    const tracks = stream.getTracks();
    tracks.forEach((track) => track.stop());
  }
}

const handleTakePhoto = async () => {
  if (!videoElement.value) return;
  const canvas = document.createElement("canvas");
  canvas.width = videoElement.value.videoWidth;
  canvas.height = videoElement.value.videoHeight;

  const ctx = canvas.getContext("2d");
  ctx.drawImage(videoElement.value, 0, 0, canvas.width, canvas.height);
  canvas.toBlob((blob) => {
    dataImage.value.push(blob);
    const res = URL.createObjectURL(blob);
    previewImages.value.push(res);
  }, "image/png");
  handleOpenCamera();
};
onMounted(async () => {
  handleOpenCamera();
  previewImages.value = checkinStore.image;
});
</script>

<style scoped>
.custom-file-upload {
  @apply inline-block cursor-pointer;
}

.custom-file-upload input[type="file"] {
  @apply hidden;
}

.upload-button {
  @apply bg-[#0D47A1] text-white py-2 px-4 rounded cursor-pointer;
}
video {
  width: 100%;
  height: auto;
}
</style>
