<template>
  <div class="bg-white p-2 rounded">
    <div
      class="flex items-center justify-between cursor-pointer"
      @click="handleToogleOpen"
    >
      <div class="text-primary font-bold">Tù<PERSON> chỉnh thông tin đơn hàng</div>
      <div>
        <span v-if="!isOpen">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.0"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            />
          </svg>
        </span>
        <span v-else>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.0"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m4.5 15.75 7.5-7.5 7.5 7.5"
            />
          </svg>
        </span>
      </div>
    </div>
    <div v-if="isOpen">
      <div class="space-y-3 mt-3">
        <div class="flex items-center gap-2">
          <label for="employee-select" class="block w-40">Nhân viên</label>
          <select
            id="employee-select"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="employee"
          >
            <option :key="employee" :value="employee">
              {{ employee }}
            </option>
          </select>
        </div>
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40">Kho</label>
          <select
            id="warehouse-select"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="warehouse"
          >
            <option :key="warehouse" :value="warehouse">
              {{ warehouse }}
            </option>
          </select>
        </div>
        <div class="flex items-center gap-2">
          <label for="warehouse-select" class="block w-40">Thời gian</label>
          <input
            id="date-select"
            type="date"
            class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
            v-model="dateTime"
            :disabled="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["order"]);
const isOpen = ref(true);
const employee = ref("");
const dateTime = ref("");
const handleToogleOpen = () => {
  isOpen.value = !isOpen.value;
};
const { getInforWarehouse } = useWarehouse();
const warehouse = ref();
const handleInfoWarehouse = async (warehouse: any) => {
  try {
    const response = await getInforWarehouse(warehouse);
    return response;
  } catch (error) {
    throw error;
  }
};
const { searchEmployes } = useOrder();

const handleGetEmployee = async (employee: string) => {
  const data = {
    keyword: employee,
    positionShortName: "",
  };
  try {
    const response = await searchEmployes(data);
    return response;
  } catch (error) {
    throw error;
  }
};

onMounted(async () => {
  const resEmplyee = await handleGetEmployee(props.order?.order?.salePartyId);
  employee.value = resEmplyee[0]?.name;

  // Handle date formatting with validation
  if (props.order.order?.orderDate) {
    const formattedDate = formatTimestampV4(props.order.order.orderDate);
    dateTime.value = formattedDate !== "N/A" ? formattedDate : "";
  } else {
    dateTime.value = "";
  }

  const resWarehouse = await handleInfoWarehouse(
    props.order.order.customAttribute?.facilityId
  );
  warehouse.value = resWarehouse?.name;
});
</script>
