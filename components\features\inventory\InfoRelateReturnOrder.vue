<template>
  <div class="mx-2 pb-2">
    <div>
      <div class="font-semibold"><PERSON><PERSON> chú</div>
      <textarea
        placeholder="Ghi chú đơn hàng"
        cols="6"
        class="outline-none p-2 border w-full rounded bg-secondary-light"
        v-model="returnStore.noteOrderReturn"
      ></textarea>
    </div>
    <div>
      <div class="flex items-center gap-2">
        <label for="employee-select" class="block w-40">Nhân viên</label>
        <select
          id="employee-select"
          class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer"
          v-model="returnStore.employeeCreateOrderReturn"
        >
          <option
            v-for="employee in dataEmployee"
            :key="employee.id"
            :value="employee"
          >
            {{ employee.name }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>
<script setup>
const { fetchDataEmployees } = useOrder();
const returnStore = returnOrderStore();
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const dataEmployee = ref();
const handleDataEmployee = async () => {
  try {
    const auth = useCookie("auth").value;

    const response = await fetchDataEmployees();
    dataEmployee.value = response;
    const res = dataEmployee.value?.find(
      (employee) => employee?.id === auth?.user?.id
    );
    returnStore.employeeCreateOrderReturn = res || dataEmployee.value[0];
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  handleDataEmployee();
});
const { updateEmployee } = useOrder();
watch(
  () => returnStore.employeeCreateOrderReturn,
  (newValue, oldValue) => {
    if (orderDetail.value) {
      updateEmployee(orderDetail.value?.id, newValue?.id, newValue?.id);
    }
  }
);
</script>
