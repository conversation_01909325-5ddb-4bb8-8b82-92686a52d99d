export default function useChat() {
  const $sdk = useNuxtApp().$sdk;
  const auth = useCookie("auth").value as any;
  const getTopics = async (
    filterTopicRequest: any,
    pageSize: number,
    currentPage: number
  ) => {
    try {
      const response = await $sdk.crm.searchTopic(
        filterTopicRequest,
        pageSize,
        currentPage
      );
      return response.content;
    } catch (error) {
      console.error("Failed to get topics:", error);
      throw new Error("Failed to get topics");
    }
  };

  const closeTopic = async (topicId: string) => {
    console.log("🚀 ~ closeTopic ~ topicId:", topicId);
    try {
      await $sdk.crm.closeTopic(topicId, auth.user.id);
    } catch (error) {
      console.error("Failed to close topic:", error);
      throw new Error("Failed to close topic");
    }
  };
  const requestJoinRoom = async (topicId: string, listUser: [string]) => {
    try {
      const response = await $sdk.omnigateway.requestJoinRoom(
        topicId,
        listUser
      );
      console.log("sau khi join room", response);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getMessages = async (
    topicId: String,
    pageSize: Number,
    pageNumber: Number
  ) => {
    try {
      const response = await $sdk.crm.getMessages(
        topicId,
        pageSize,
        pageNumber
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    getTopics,
    closeTopic,
    requestJoinRoom,
    getMessages,
  };
}
