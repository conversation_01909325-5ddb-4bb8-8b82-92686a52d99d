<template>
  <div>
    <div class="h-full overflow-y-scroll md:overflow-hidden">
      <div class="w-full relative h-full">
        <div class="wrapper lg:my-0 my-0 px-2 md:[px-15] h-full">
          <div class="w-full relative h-full-custom">
            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-2 md:mb-4 md:h-full z-0"
            >
              <div
                class="w-full col-span-2 relative bg-white rounded h-full md:order-1"
              >
                <div class="font-bold text-primary p-2">
                  Danh sách sản phẩm trả
                </div>
                <div class="md:block hidden mx-2">
                  <TableProduct :isPageDetailReturnOrder="true"></TableProduct>
                </div>
                <div class="block md:hidden">
                  <ListItemMobile
                    :isPageDetailReturnOrder="true"
                  ></ListItemMobile>
                </div>
              </div>
              <div
                class="w-full col-span-2 md:col-span-1 order-1 md:order-2 mb-24"
              >
                <div
                  class="flex flex-col gap-1 md:overflow-y-scroll md:h-screen-50 bg-white"
                >
                  <div class="border-b pb-1 pt-1">
                    <div class="flex items-center mx-2 gap-1">
                      <div class="text-primary font-bold">Trạng thái:</div>
                      <div class="font-semibold">
                        {{ orderReturnDetail?.statusDescription }}
                      </div>
                    </div>
                    <CustomerOrderReturnDetail
                      :orderDetail="returnStore.orderReturnDetail"
                    ></CustomerOrderReturnDetail>
                  </div>
                  <div class="pb-1">
                    <InfoOrderReturnDetail
                      :orderDetail="returnStore.orderReturnDetail"
                      :employee="employee"
                    ></InfoOrderReturnDetail>
                    <InfoPaymentReturnDetail
                      :orderDetail="returnStore.orderReturnDetail"
                    ></InfoPaymentReturnDetail>
                    <div class="mx-2 mt-1">
                      <div class="font-bold text-primary">
                        Đơn hàng liên quan
                      </div>
                      <div v-for="order in orderRelate">
                        <div v-for="orderR in order.relations">
                          <span>
                            <span class="font-semibold">Đơn bán: </span>
                            <span
                              class="underline"
                              @click="handleNavigate(orderR)"
                            >
                              {{ orderR.order_id }} ({{
                                orderR.sub_type === "RETURN_FULL"
                                  ? "Trả toàn bộ"
                                  : "Trả một phần"
                              }})
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Chi tiết đơn trả",
});
useHead({
  title: "Chi tiết đơn trả",
  meta: [
    {
      name: "description",
      content: "Chi tiết đơn trả",
    },
  ],
});
const route = useRoute();
const returnStore = returnOrderStore();
const orderReturnDetail = computed(() => returnStore.orderReturnDetail);
const isLoading = ref(false);
const { searchEmployes, fetchListOrderRelations } = useOrder();
const employee = ref();
const orderRelate = ref();
const handleGetEmployee = async (orderId: string) => {
  try {
    const data = {
      keyword: orderReturnDetail.value?.order?.salePartyId,
      positionShortName: "",
    };
    const results = await Promise.allSettled([
      searchEmployes(data),
      fetchListOrderRelations([orderId]),
    ]);

    const response =
      results[0].status === "fulfilled" ? results[0].value : null;
    const res = results[1].status === "fulfilled" ? results[1].value : null;

    orderRelate.value = res?.data;
    employee.value = response[0];
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  returnStore.orderReturnDetail = null;
  if (route.query.orderId) {
    isLoading.value = true;
    await returnStore.getDetailOrderReturn(route.query.orderId as string);
    await handleGetEmployee(route.query.orderId as string);
    isLoading.value = false;
  }
});
watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    returnStore.orderReturnDetail = null;
    if (newVal) {
      isLoading.value = true;
      await returnStore.getDetailOrderReturn(newVal as string);
      await handleGetEmployee(newVal as string);
      isLoading.value = false;
    }
  }
);
const handleNavigate = (order: any) => {
  navigateTo(
    `/sale?orderId=${order?.order_id}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
  );
};
</script>
