<template>
  <PopupSuccessByPaymentId
    v-if="isAlert"
    :dataOrder="dataOrder"
    :dataPayment="dataPayment"
  ></PopupSuccessByPaymentId>
  <div
    v-else
    class="md:flex md:items-center md:justify-center md:h-screen-50 bg-secondary"
  >
    <!-- desktop -->
    <div class="md:block hidden">
      <div class="w-[84vw] h-[73vh] grid grid-cols-10 space-x-2">
        <div class="col-span-7 rounded-xl bg-white md:mb-[40px]">
          <div
            v-if="!dataQrCode"
            class="text-primary m-2 font-bold flex items-center justify-center"
          >
            Chọn phương thức thanh toán
          </div>
          <PaymentMethodByPaymentId
            v-if="!dataQrCode"
            :dataPaymentMethod="dataPaymentMethod"
            @choosePaymentMethod="handleChoosePaymentMethod"
          ></PaymentMethodByPaymentId>
          <div v-if="dataQrCode">
            <QrByPaymentId
              :dataQrCode="dataQrCode"
              :dataPayment="dataPayment"
              :selectedPaymentMethod="selectedPaymentMethod"
              @clickBack="handleClickBack"
              @createPaymentAgain="createPaymentAgain"
            ></QrByPaymentId>
          </div>
        </div>
        <div class="col-span-3">
          <InfoByPaymentId
            :dataOrder="dataOrder"
            :dataPayment="dataPayment"
          ></InfoByPaymentId>
        </div>
      </div>
    </div>
    <!-- mobile -->
    <div class="block md:hidden">
      <InfoByPaymentId
        :dataOrder="dataOrder"
        :dataPayment="dataPayment"
      ></InfoByPaymentId>
      <PaymentMethodByPaymentId
        v-if="!dataQrCode"
        :dataPaymentMethod="dataPaymentMethod"
        @choosePaymentMethod="handleChoosePaymentMethod"
      ></PaymentMethodByPaymentId>
      <div v-if="dataQrCode">
        <QrByPaymentId
          :dataQrCode="dataQrCode"
          :dataPayment="dataPayment"
          :selectedPaymentMethod="selectedPaymentMethod"
          @clickBack="handleClickBack"
        ></QrByPaymentId>
      </div>
    </div>
  </div>

  <div v-if="isLoadingCreateQR">
    <LoadingSpinner />
  </div>
</template>
<script setup>
const props = defineProps([
  "dataPayment",
  "dataOrder",
  "dataPaymentMethod",
  "isMobile",
]);
const { confirmToGateway, paymentStatus } = usePayment();
const dataQrCode = ref();
const route = useRoute();
const isLoadingCreateQR = ref(false);
const isHidePaymentMethod = ref(false);
const isAlert = ref(false);
let intervalId = null;
const selectedPaymentMethod = ref();
const handleChoosePaymentMethod = async (paymentMethod) => {
  selectedPaymentMethod.value = paymentMethod;
  console.log("selectedPaymentMethod", selectedPaymentMethod.value);
  intervalId = setInterval(CheckPaymentStatus, 5000);
  // await CheckLastPaymentStatus();
  // kiể  m tra nếu đã có dữ liệu
  if (dataLastPayment.value) {
    // nếu chọn pttt đã có thì mở lại -> k cần tạo
    if (selectedPaymentMethod.value === dataLastPayment.value?.methodCode) {
      console.log("mảng sau là", dataLastPayment.value);
      if (paymentMethod?.code === "casso" || paymentMethod?.code === "mpos") {
        //
        handleCreatePayment();
      } else {
        //
        window.location.href = dataLastPayment.value?.qrCode;
      }
      return;
    } else {
      // nếu pttt không trùng thì tạo pttt mới
      handleCreatePayment();
    }
  } else {
    // nếu không có thanh toán thì tạo thanh toán mới
    await handleCreatePayment();
  }
};
//
const handleCreatePayment = async () => {
  try {
    const host = window.location.origin;
    const returnUrl = `${host}/thanh-toan/ma-thanh-toan`;
    isLoadingCreateQR.value = true;
    const response = await confirmToGateway(
      route.query.paymentId,
      selectedPaymentMethod.value?.code,
      returnUrl
    );

    // chạy hàm kiểm tra mỗi 5s
    // nếu là chuyển khoản thì hiện qr
    if (response) {
      if (
        selectedPaymentMethod.value?.code === "casso" ||
        selectedPaymentMethod.value?.code === "mpos"
      ) {
        isHidePaymentMethod.value = true;
        dataQrCode.value = response;
      } else {
        if (response?.data) {
          window.location.href = response?.data;
        }
      }
    }

    isLoadingCreateQR.value = false;

    return response;
  } catch (error) {
    isLoadingCreateQR.value = false;
    throw error;
  }
};
const handleClickBack = () => {
  dataQrCode.value = null;
  dataLastPayment.value = null;
  // isHidePaymentMethod.value = false;
  clearInterval(intervalId);
};
const dataLastPayment = ref();
const CheckPaymentStatus = async () => {
  try {
    const response = await paymentStatus(route.query.paymentId);
    if (response?.statusCode === "0") {
      isAlert.value = true;
      clearInterval(intervalId);
    }
  } catch (error) {
    throw error;
  }
};
//
const CheckLastPaymentStatus = async () => {
  try {
    const response = await paymentStatus(route.query.paymentId);
    if (response?.statusCode === "0") {
      isAlert.value = true;
      clearInterval(intervalId);
    }
    if (response?.qrCode) {
      dataLastPayment.value = response;
      isHidePaymentMethod.value = true;
      dataQrCode.value = response;
    }
  } catch (error) {
    throw error;
  }
};
const createPaymentAgain = async (dataRes) => {
  try {
    const host = window.location.origin;
    const returnUrl = `${host}/thanh-toan/ma-thanh-toan`;
    isLoadingCreateQR.value = true;
    const response = await confirmToGateway(
      dataRes?.paymentId,
      dataRes?.code,
      returnUrl
    );

    // chạy hàm kiểm tra mỗi 5s
    // nếu là chuyển khoản thì hiện qr
    if (response) {
      if (
        selectedPaymentMethod.value?.code === "casso" ||
        selectedPaymentMethod.value?.code === "mpos"
      ) {
        isHidePaymentMethod.value = true;
        dataQrCode.value = response;
      } else {
        if (response?.data) {
          window.location.href = response?.data;
        }
      }
    }

    isLoadingCreateQR.value = false;

    return response;
  } catch (error) {
    isLoadingCreateQR.value = false;
    throw error;
  }
};
const isLoading = ref(false);
onMounted(async () => {
  try {
    await CheckLastPaymentStatus();
    intervalId = setInterval(CheckPaymentStatus, 5000);
  } catch (error) {
    throw error;
  }
});
onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
});
</script>
