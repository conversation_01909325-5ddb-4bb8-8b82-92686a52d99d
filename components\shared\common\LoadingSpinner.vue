<template>
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg p-6">
      <div class="flex items-center gap-3">
        <div
          class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"
        ></div>
        <span class="text-gray-700">Đang xử lý...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  text?: string;
  subText?: string;
  showCancel?: boolean;
  allowBackdropClick?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  text: "Đang tải",
  subText: "Vui lòng đợi trong giây lát...",
  showCancel: false,
  allowBackdropClick: false,
});

// Emits
const emit = defineEmits<{
  cancel: [];
  backdropClick: [];
}>();

// Reactive state
const loadingText = ref(props.text);
const subText = ref(props.subText);

// Handle backdrop click
const handleBackdropClick = () => {
  if (props.allowBackdropClick) {
    emit("backdropClick");
  }
};

// Scroll lock functionality
onMounted(() => {
  // Prevent body scroll when loading spinner is shown
  document.body.style.overflow = "hidden";
  document.body.style.paddingRight = "0px"; // Prevent layout shift
});

onUnmounted(() => {
  // Restore body scroll when loading spinner is removed
  document.body.style.overflow = "";
  document.body.style.paddingRight = "";
});
</script>

<style scoped>
/* Spinning Animations (Cái gì đó đang xoay xoay) */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

/* Running Animations (Cái gì đó đang chạy chạy) */
@keyframes progress-run {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 0%;
    transform: translateX(100%);
  }
}

@keyframes run-dot-1 {
  0%,
  20%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.3;
  }
  10% {
    transform: translateX(20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes run-dot-2 {
  0%,
  20%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.3;
  }
  10% {
    transform: translateX(20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes run-dot-3 {
  0%,
  20%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.3;
  }
  10% {
    transform: translateX(20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes run-dot-4 {
  0%,
  20%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.3;
  }
  10% {
    transform: translateX(20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes run-dot-5 {
  0%,
  20%,
  100% {
    transform: translateX(0) scale(1);
    opacity: 0.3;
  }
  10% {
    transform: translateX(20px) scale(1.2);
    opacity: 1;
  }
}

@keyframes bounce-text-1 {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

@keyframes bounce-text-2 {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

@keyframes bounce-text-3 {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Apply animations */
.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

.animate-progress-run {
  animation: progress-run 2s ease-in-out infinite;
}

.animate-run-dot-1 {
  animation: run-dot-1 1.5s infinite 0s;
}

.animate-run-dot-2 {
  animation: run-dot-2 1.5s infinite 0.1s;
}

.animate-run-dot-3 {
  animation: run-dot-3 1.5s infinite 0.2s;
}

.animate-run-dot-4 {
  animation: run-dot-4 1.5s infinite 0.3s;
}

.animate-run-dot-5 {
  animation: run-dot-5 1.5s infinite 0.4s;
}

.animate-bounce-text-1 {
  animation: bounce-text-1 1.4s infinite 0s;
}

.animate-bounce-text-2 {
  animation: bounce-text-2 1.4s infinite 0.2s;
}

.animate-bounce-text-3 {
  animation: bounce-text-3 1.4s infinite 0.4s;
}

/* Responsive */
@media (max-width: 640px) {
  .max-w-sm {
    max-width: 18rem;
  }
}
</style>
