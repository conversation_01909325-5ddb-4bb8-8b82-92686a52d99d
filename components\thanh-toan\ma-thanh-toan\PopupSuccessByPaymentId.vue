<template>
  <div class="flex items-center justify-center h-[80svh]">
    <div class="bg-white p-6 md:p-6 rounded w-full max-w-2xl text-center">
      <!-- Icon thất bại -->
      <div class="flex flex-col items-center">
        <div class="bg-green-100 p-4 rounded-full text-green-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"
            />
          </svg>
        </div>
        <h2 class="text-green-600 text-2xl font-semibold mt-2">
          Giao dịch Thành công
        </h2>
      </div>

      <!-- Thông tin giao dịch -->
      <div class="mt-2">
        <h3 class="text-sm">Số tiền</h3>
        <p class="text-red-500 text-2xl font-bold">
          {{ formatCurrency(dataPayment?.totalAmount) }}
        </p>

        <div class="border-t my-4"></div>

        <div
          class="grid grid-cols-2 gap-y-3 text-sm text-left items-center justify-center"
        >
          <div class="text-gray-500">Mã thanh toán:</div>
          <div class="font-semibold text-black text-sm">{{ dataPayment?.id }}</div>

          <div class="text-gray-500">Phương thức thanh toán:</div>
          <div class="font-semibold text-black text-sm">
            {{ dataPayment?.methodCode }}
          </div>

          <div class="text-gray-500">Tiêu đề:</div>
          <div class="font-semibold text-black text-sm">
            {{ dataPayment?.orderInfo }}
          </div>
        </div>
        <!-- 
        <p class="text-gray-500 text-sm mt-6">
          Giao dịch của bạn không được thanh toán, vui lòng chọn thanh toán lại.
        </p> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["dataOrder", "dataPayment"]);
</script>

<style scoped></style>
