import { defineStore } from "pinia";
import { ref } from "vue";

export const useTagStore = defineStore("tag", () => {
  const dataTag = ref([]);
  const dataConnector = ref();
  const { getConnectorByResource, getTags, createConnector } = usePortal();
  const handleGetConnectorByResource = async (
    resourceId: string,
    resourceType: string,
    type: string
  ) => {
    try {
      dataConnector.value = null;
      const response = await getConnectorByResource(
        resourceId,
        resourceType,
        type
      );
      if (response.length > 0) {
        dataConnector.value = response;
      }
    } catch (error) {
      throw error;
    }
  };
  return {
    dataTag,
    dataConnector,
    handleGetConnectorByResource,
  };
});
// else {
//   await createConnector(resourceId, "ORDER", "", "TAG", "");
//   await handleGetConnectorByResource(resourceId, resourceType, type);
// }
