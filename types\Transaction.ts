/**
 * Transaction Types and Enums
 * Comprehensive type definitions for transaction management
 */

// Enums
export enum PaymentGatewayType {
  BANK_TRANSACTION = "BANK_TRANSACTION",
  OTHER_PAYMENT_GATEWAY = "OTHER_PAYMENT_GATEWAY"
}

export enum TransactionStatus {
  PENDING = "PENDING",
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
  PROCESSING = "PROCESSING"
}

export enum PaymentConfirmStatus {
  PENDING = "PENDING",
  CONFIRMED = "CONFIRMED",
  REJECTED = "REJECTED",
  MANUAL_REVIEW = "MANUAL_REVIEW"
}

export enum TransactionPurpose {
  SALE_ORDER = "sale_order",
  COLLECT_FROM_GATEWAY = "collect_from_gateway",
  OTHER = "other"
}

// Main Transaction Interface
export interface Transaction {
  // Core identifiers
  partnerId: string;
  extId: string;
  transactionId: string;
  
  // Payment gateway information
  paymentGatewayType: PaymentGatewayType;
  bankCode: string;
  bankAccountNumber: string;
  extBankAccountNumber: string;
  extBank: string;
  
  // Transaction details
  amount: number;
  currencyCode: string;
  description: string;
  timeTransaction: Date;
  status: TransactionStatus;
  gateway: string;
  
  // Payment information
  paymentId: string;
  paymentAmount: number;
  paymentConfirmStatus: PaymentConfirmStatus;
  paymentConfirmNote: string;
  
  // Related entities
  accountTransactionId: string;
  orderId: string;
  invoiceId: string;
  cashAccountId: string;
  
  // Purpose and metadata
  purpose: TransactionPurpose;
  customAttributes: Record<string, any>;
  
  // UI/Display fields (for compatibility with existing components)
  id?: number;
  type?: "income" | "expense";
  category?: { id: number; name: string };
  paymentMethod?: string;
  date?: string;
}

// Legacy Transaction Interface (for backward compatibility)
export interface LegacyTransaction {
  id: number;
  type: "income" | "expense";
  amount: number;
  category?: { id: number; name: string };
  description?: string;
  paymentMethod: string;
  date: string;
  status: string;
}

// Transaction Filter Interface
export interface TransactionFilters {
  status?: TransactionStatus;
  paymentGatewayType?: PaymentGatewayType;
  paymentConfirmStatus?: PaymentConfirmStatus;
  purpose?: TransactionPurpose;
  bankCode?: string;
  gateway?: string;
  dateFrom?: Date;
  dateTo?: Date;
  amountFrom?: number;
  amountTo?: number;
  search?: string;
}

// Transaction Statistics Interface
export interface TransactionStats {
  totalIncome: number;
  totalExpense: number;
  netProfit: number;
  transactionCount: number;
  pendingCount: number;
  completedCount: number;
  failedCount: number;
  incomeChange: number;
  expenseChange: number;
  profitChange: number;
  countChange: number;
}
