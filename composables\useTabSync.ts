/**
 * Tab Synchronization Composable
 * 
 * This composable provides cross-tab communication for synchronizing
 * authentication state, particularly for logout events.
 * 
 * Features:
 * - BroadcastChannel API for modern browsers
 * - localStorage events as fallback for older browsers
 * - Automatic cleanup on component unmount
 * - Type-safe event handling
 */

import { ref, onMounted, onUnmounted } from 'vue';

// Event types for tab synchronization
export interface TabSyncEvent {
  type: 'LOGOUT' | 'LOGIN' | 'TOKEN_REFRESH' | 'CONTEXT_CHANGE';
  timestamp: number;
  data?: any;
}

// Storage key for localStorage fallback
const TAB_SYNC_STORAGE_KEY = 'tab_sync_event';
const BROADCAST_CHANNEL_NAME = 'dms_tab_sync';

export const useTabSync = () => {
  // BroadcastChannel for modern browsers
  let broadcastChannel: BroadcastChannel | null = null;
  
  // Event listeners registry
  const eventListeners = new Map<string, Set<(event: TabSyncEvent) => void>>();
  
  // Check if BroadcastChannel is supported
  const isBroadcastChannelSupported = typeof BroadcastChannel !== 'undefined';
  
  /**
   * Initialize tab synchronization
   */
  const initializeTabSync = (): void => {
    if (!process.client) return;
    
    try {
      // Initialize BroadcastChannel if supported
      if (isBroadcastChannelSupported) {
        broadcastChannel = new BroadcastChannel(BROADCAST_CHANNEL_NAME);
        broadcastChannel.addEventListener('message', handleBroadcastMessage);
      }
      
      // Always set up localStorage fallback
      window.addEventListener('storage', handleStorageEvent);
      
      console.log('[TabSync] Initialized with', {
        broadcastChannel: !!broadcastChannel,
        localStorage: true
      });
    } catch (error) {
      console.error('[TabSync] Initialization failed:', error);
    }
  };
  
  /**
   * Handle BroadcastChannel messages
   */
  const handleBroadcastMessage = (event: MessageEvent<TabSyncEvent>): void => {
    try {
      const syncEvent = event.data;
      if (isValidTabSyncEvent(syncEvent)) {
        notifyListeners(syncEvent);
      }
    } catch (error) {
      console.error('[TabSync] Error handling broadcast message:', error);
    }
  };
  
  /**
   * Handle localStorage events (fallback)
   */
  const handleStorageEvent = (event: StorageEvent): void => {
    try {
      if (event.key === TAB_SYNC_STORAGE_KEY && event.newValue) {
        const syncEvent = JSON.parse(event.newValue) as TabSyncEvent;
        if (isValidTabSyncEvent(syncEvent)) {
          notifyListeners(syncEvent);
        }
      }
    } catch (error) {
      console.error('[TabSync] Error handling storage event:', error);
    }
  };
  
  /**
   * Validate TabSyncEvent structure
   */
  const isValidTabSyncEvent = (event: any): event is TabSyncEvent => {
    return (
      event &&
      typeof event === 'object' &&
      typeof event.type === 'string' &&
      typeof event.timestamp === 'number' &&
      ['LOGOUT', 'LOGIN', 'TOKEN_REFRESH', 'CONTEXT_CHANGE'].includes(event.type)
    );
  };
  
  /**
   * Notify all registered listeners for an event type
   */
  const notifyListeners = (event: TabSyncEvent): void => {
    const listeners = eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`[TabSync] Error in listener for ${event.type}:`, error);
        }
      });
    }
  };
  
  /**
   * Broadcast an event to all other tabs
   */
  const broadcastEvent = (type: TabSyncEvent['type'], data?: any): void => {
    if (!process.client) return;
    
    const event: TabSyncEvent = {
      type,
      timestamp: Date.now(),
      data
    };
    
    try {
      // Use BroadcastChannel if available
      if (broadcastChannel) {
        broadcastChannel.postMessage(event);
      }
      
      // Always use localStorage as fallback/backup
      localStorage.setItem(TAB_SYNC_STORAGE_KEY, JSON.stringify(event));
      
      // Clean up localStorage after a short delay to prevent accumulation
      setTimeout(() => {
        try {
          localStorage.removeItem(TAB_SYNC_STORAGE_KEY);
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 100);
      
    } catch (error) {
      console.error('[TabSync] Error broadcasting event:', error);
    }
  };
  
  /**
   * Subscribe to a specific event type
   */
  const subscribe = (eventType: TabSyncEvent['type'], callback: (event: TabSyncEvent) => void): (() => void) => {
    if (!eventListeners.has(eventType)) {
      eventListeners.set(eventType, new Set());
    }
    
    const listeners = eventListeners.get(eventType)!;
    listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      listeners.delete(callback);
      if (listeners.size === 0) {
        eventListeners.delete(eventType);
      }
    };
  };
  
  /**
   * Cleanup tab synchronization
   */
  const cleanup = (): void => {
    try {
      if (broadcastChannel) {
        broadcastChannel.removeEventListener('message', handleBroadcastMessage);
        broadcastChannel.close();
        broadcastChannel = null;
      }
      
      if (process.client) {
        window.removeEventListener('storage', handleStorageEvent);
      }
      
      eventListeners.clear();
      
      console.log('[TabSync] Cleaned up');
    } catch (error) {
      console.error('[TabSync] Error during cleanup:', error);
    }
  };
  
  // Auto-initialize on mount
  onMounted(() => {
    initializeTabSync();
  });
  
  // Auto-cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });
  
  return {
    // Core functions
    broadcastEvent,
    subscribe,
    
    // Utility functions
    initializeTabSync,
    cleanup,
    
    // State
    isBroadcastChannelSupported: ref(isBroadcastChannelSupported)
  };
};
