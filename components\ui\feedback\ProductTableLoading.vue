<template>
  <div class="animate-pulse">
    <!-- Desktop Table Rows Loading -->
    <div v-if="showDesktop" class="hidden md:block">
      <div class="bg-white">
        <div
          v-for="index in itemCount"
          :key="index"
          class="grid grid-cols-12 gap-4 p-4 border-b border-gray-200 hover:bg-gray-50"
        >
          <!-- Product Image -->
          <div class="col-span-1 flex items-center">
            <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
          </div>

          <!-- Product Info -->
          <div class="col-span-3 flex items-center">
            <div class="space-y-2 w-full">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>

          <!-- Category -->
          <div class="col-span-2 flex items-center">
            <div class="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>

          <!-- Price -->
          <div class="col-span-2 flex items-center">
            <div class="space-y-2 w-full">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>

          <!-- Stock -->
          <div class="col-span-2 flex items-center">
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>

          <!-- Status -->
          <div class="col-span-1 flex items-center">
            <div class="h-6 w-16 bg-gray-200 rounded-full"></div>
          </div>

          <!-- Actions -->
          <div class="col-span-1 flex items-center justify-center">
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Cards Loading -->
    <div v-if="showMobile" class="md:hidden space-y-3 p-4">
      <div
        v-for="index in itemCount"
        :key="index"
        class="bg-white rounded-lg border border-gray-200 shadow-sm p-4"
      >
        <div class="flex gap-3">
          <!-- Mobile Product Image -->
          <div class="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
          
          <!-- Mobile Product Info -->
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
            
            <!-- Mobile Price and Stock -->
            <div class="flex justify-between items-center pt-2">
              <div class="h-4 bg-gray-200 rounded w-1/3"></div>
              <div class="h-6 w-16 bg-gray-200 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  itemCount?: number;
  showMobile?: boolean;
  showDesktop?: boolean;
}

withDefaults(defineProps<Props>(), {
  itemCount: 5,
  showMobile: true,
  showDesktop: true,
});
</script>

<style scoped>
/* Enhanced pulse animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer effect for skeleton elements */
.bg-gray-200 {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation for better visual effect */
.grid:nth-child(1) { animation-delay: 0ms; }
.grid:nth-child(2) { animation-delay: 100ms; }
.grid:nth-child(3) { animation-delay: 200ms; }
.grid:nth-child(4) { animation-delay: 300ms; }
.grid:nth-child(5) { animation-delay: 400ms; }

.space-y-3 > *:nth-child(1) { animation-delay: 0ms; }
.space-y-3 > *:nth-child(2) { animation-delay: 100ms; }
.space-y-3 > *:nth-child(3) { animation-delay: 200ms; }
.space-y-3 > *:nth-child(4) { animation-delay: 300ms; }
.space-y-3 > *:nth-child(5) { animation-delay: 400ms; }

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>
