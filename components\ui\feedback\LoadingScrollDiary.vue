<template>
  <div
    class="h-full animate-pulse bg-white space-y-2 border max-h-[225px] p-2 rounded-lg my-2"
  >
    <div class="flex items-center justify-between">
      <div class="bg-gray-200 w-1/3 text-gray-200 border rounded">a</div>
      <div class="bg-gray-200 w-[50px] text-gray-200 border rounded">b</div>
    </div>
    <div class="bg-gray-200 w-1/2 text-gray-200 border rounded">a</div>
    <div class="border-b mx-6 my-4"></div>
    <div class="flex items-center gap-4">
      <div class="w-[40px] h-[40px] rounded bg-gray-200 text-gray-200">1</div>
      <div class="bg-gray-200 text-gray-200 w-full">1</div>
    </div>
    <div class="flex items-center gap-4">
      <div class="w-[40px] h-[40px] rounded bg-gray-200 text-gray-200">1</div>
      <div class="bg-gray-200 text-gray-200 w-full">1</div>
    </div>
    <div class="border-b mx-6 mb-4"></div>
    <div class="bg-gray-200 text-gray-200 my-2 w-1/4">1</div>
  </div>
</template>
<script setup lang="ts"></script>
