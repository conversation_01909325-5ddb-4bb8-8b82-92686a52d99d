<template>
  <div class="bg-white border-b border-gray-200 px-4 py-3">
    <!-- Mobile Layout -->
    <div class="md:hidden space-y-3">
      <!-- Search -->
      <div class="relative">
        <input
          type="text"
          v-model="filters.search"
          @input="$emit('filter-change', filters)"
          placeholder="Tìm kiếm giao dịch..."
          class="pl-10 pr-4 py-3 border border-gray-300 rounded-lg text-sm outline-none w-full"
        />
        <svg
          class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>

      <!-- Filters Grid -->
      <div class="grid grid-cols-2 gap-3">
        <!-- <select
          v-model="filters.type"
          @change="$emit('filter-change', filters)"
          class="px-3 py-3 border border-gray-300 rounded-lg text-sm outline-none"
        >
          <option value="">Tất cả loại</option>
          <option value="income">Thu</option>
          <option value="expense">Chi</option>
        </select> -->
        <!-- 
        <select
          v-model="filters.category"
          @change="$emit('filter-change', filters)"
          class="px-3 py-3 border border-gray-300 rounded-lg text-sm outline-none"
        >
          <option value="">Tất cả danh mục</option>
          <option
            v-for="category in categories"
            :key="category.id"
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select> -->
      </div>
    </div>

    <!-- Desktop Layout -->
    <div class="hidden md:flex items-center gap-4 flex-wrap">
      <flat-pickr
        class="col-span-1 min-w-[250px] py-[6px] px-2 text-base border border-gray-300 rounded-lg outline-none text-center"
        placeholder="Chọn khoảng thời gian"
        :config="datePickerConfig"
        v-model="dateRange"
      />
      <!-- Search -->
      <div class="flex items-center gap-2 ml-auto">
        <div class="relative">
          <input
            type="text"
            v-model="filters.search"
            @input="$emit('filter-change', filters)"
            placeholder="Tìm kiếm giao dịch..."
            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm outline-none w-64"
          />
          <svg
            class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import FlatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
});
const dateRange = ref([]);
const emit = defineEmits(["filter-change"]);

const datePickerConfig = ref({
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: (selectedDates) => {
    if (selectedDates.length === 2) {
      filters.dateRange = selectedDates;
      emit("filter-change", filters);
    }
  },
});

const filters = reactive({
  dateRange: "",
  search: "",
});
</script>
