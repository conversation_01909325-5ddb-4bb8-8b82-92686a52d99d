<template>
  <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
    <!-- Desktop Table Skeleton -->
    <div class="hidden md:block">
      <div class="overflow-auto">
        <table class="table-auto w-full text-sm">
          <thead class="bg-blue-100">
            <tr class="text-left font-semibold">
              <th class="p-3 w-1/12 text-center border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
              </th>
              <th class="p-3 w-2/12 border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
              </th>
              <th class="p-3 w-2/12 border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
              </th>
              <th class="p-3 w-2/12 border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
              </th>
              <th class="p-3 w-5/12 border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse"></div>
              </th>
              <th class="p-3 w-auto border-b border-gray-200">
                <div class="h-4 bg-gray-300 rounded animate-pulse mx-auto w-6"></div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="i in skeletonRows"
              :key="i"
              class="border-b border-gray-100 last:border-b-0"
            >
              <td class="p-3 text-center">
                <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
              </td>
              <td class="p-3">
                <div class="space-y-2">
                  <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </td>
              <td class="p-3">
                <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
              </td>
              <td class="p-3">
                <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
              </td>
              <td class="p-3">
                <div class="space-y-2">
                  <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse w-5/6"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse w-4/6"></div>
                </div>
              </td>
              <td class="p-3 text-center">
                <div class="h-6 w-6 bg-gray-200 rounded animate-pulse mx-auto"></div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile Cards Skeleton -->
    <div class="block md:hidden p-4">
      <div class="space-y-4">
        <div
          v-for="i in skeletonRows"
          :key="i"
          class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
        >
          <div class="flex justify-between items-start mb-3">
            <div class="h-5 bg-gray-200 rounded animate-pulse w-20"></div>
            <div class="h-6 w-6 bg-gray-200 rounded animate-pulse"></div>
          </div>
          
          <div class="space-y-3">
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
            </div>
            
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-28"></div>
            </div>
            
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
              <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
              <div class="h-3 bg-gray-200 rounded animate-pulse w-4/5"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  rows?: number;
}

const props = withDefaults(defineProps<Props>(), {
  rows: 5,
});

const skeletonRows = computed(() => props.rows);
</script>

<style scoped>
/* Optimized animation for better performance */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* CSS containment for performance */
.bg-white {
  contain: layout style;
}

/* Prevent layout shift */
table {
  table-layout: fixed;
}

th, td {
  contain: layout;
}
</style>
