export default function useCampaign() {
  const $sdk = useNuxtApp().$sdk;

  const fetchCampaignActionActiveNow = async (
    customerId: string,
    campaignActionType: string
  ) => {
    try {
      const response = await $sdk.campaign.getCampaignActionActiveNow(
        customerId,
        campaignActionType
      );
      return response;
    } catch (error) {
      console.error("Error fetching campaign action active now:", error);
    }
  };

  const searchVoucher = async (
    campaignId: String,
    campaignActionId: String,
    campaignActionType: String,
    customerId: String,
    excludeExpired: Boolean,
    pageNumber: number,
    pageSize: number
  ) => {
    try {
      const response = await $sdk.campaign.searchVouchers(
        campaignId,
        campaignActionId,
        campaignActionType,
        customerId,
        excludeExpired,
        pageNumber,
        pageSize
      );
      return response;
    } catch (error) {
      console.error("Error fetching vouchers:", error);
    }
  };

  const checkValidVoucher = async (customerId: string, voucherCode: string) => {
    try {
      const response = await $sdk.campaign.checkValidVoucher(
        customerId,
        voucherCode
      );
      return response;
    } catch (error) {
      useNuxtApp().$toast.error(error + "!");
    }
  };
  const getCampaignActiveNow = async (
    campaignActionType?: string,
    customerId?: string
  ) => {
    try {
      const response = await $sdk.campaign.getCampaignActionActiveNow(
        customerId,
        campaignActionType
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getPromotionProductPrice = async (
    productId: String,
    productPrice: number
  ) => {
    try {
      const response = await $sdk.campaign.getPromotionProductPrice(
        productId,
        productPrice
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getVoucherAvailableForCustomer = async (
    campaignId: string,
    campaignActionId: string,
    customerId: string,
    excludeExpired: Boolean
  ) => {
    try {
      const response = await $sdk.campaign.getVoucherAvailableForCustomer(
        campaignId,
        campaignActionId,
        customerId,
        excludeExpired,
        false
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const suggestVoucher = async (customerId: string) => {
    try {
      const response = await $sdk.campaign.suggestVoucher(customerId);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const addCustomerToVoucher = async (voucherCode: string, userId: string, affiliateId: string)=>{
    try {
      const response = await $sdk.campaign.addCustomerToVoucher(voucherCode, userId, affiliateId)
      return response
    } catch (error) {
      throw error
    }
  }
  return {
    fetchCampaignActionActiveNow,
    searchVoucher,
    checkValidVoucher,
    getCampaignActiveNow,
    getPromotionProductPrice,
    getVoucherAvailableForCustomer,
    suggestVoucher,
    addCustomerToVoucher
  };
}
