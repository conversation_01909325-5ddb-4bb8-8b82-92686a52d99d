<template>
  <div
    class="bg-white rounded-lg border border-gray-200 p-3 mb-3 w-full overflow-hidden"
  >
    <!-- Header: Transaction ID and Amount -->
    <div class="flex items-start justify-between mb-3">
      <div class="min-w-0 flex-1 mr-2">
        <div class="text-sm font-medium text-gray-900 truncate">
          {{ transaction.transactionId }}
        </div>
        <div class="text-xs text-gray-500 truncate">
          {{ transaction.extId }}
        </div>
      </div>
      <div
        class="text-base font-bold whitespace-nowrap"
        :class="
          transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
        "
      >
        {{ transaction.type === "income" ? "+" : "-"
        }}{{
          formatTransactionAmount(transaction.amount, transaction.currencyCode)
        }}
      </div>
    </div>

    <!-- Bank Info -->
    <div class="mb-3">
      <div class="text-sm font-medium text-gray-900 mb-1">
        {{ getBankName(transaction.bankCode) }}
      </div>
      <div class="text-xs text-gray-500 mb-1">
        {{ maskAccountNumber(transaction.bankAccountNumber) }}
      </div>
      <div class="text-xs text-gray-500">
        {{ transaction.gateway }}
      </div>
    </div>

    <!-- Description -->
    <div class="mb-3" v-if="transaction.description">
      <div class="text-sm text-gray-600 truncate">
        {{ transaction.description }}
      </div>
    </div>

    <!-- Status Row -->
    <div class="mb-3">
      <div class="flex flex-wrap gap-2">
        <span :class="getTransactionStatusClass(transaction.status)">
          {{ getTransactionStatusText(transaction.status) }}
        </span>
        <span
          :class="
            getPaymentConfirmStatusClass(transaction.paymentConfirmStatus)
          "
        >
          {{ getPaymentConfirmStatusText(transaction.paymentConfirmStatus) }}
        </span>
      </div>
    </div>

    <!-- Footer: Date and Actions -->
    <div class="flex items-center justify-between">
      <div class="text-xs text-gray-500">
        {{ formatTransactionDate(transaction.timeTransaction) }}
        {{ formatTransactionTime(transaction.timeTransaction) }}
      </div>
      <div class="flex items-center gap-1">
        <button
          @click="$emit('view-detail', transaction)"
          class="p-1.5 text-gray-400 hover:text-primary rounded transition-colors"
        >
          <svg
            class="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Transaction } from "~/types/Transaction";
import {
  getTransactionStatusClass,
  getTransactionStatusText,
  getPaymentConfirmStatusClass,
  getPaymentConfirmStatusText,
  formatTransactionAmount,
  formatTransactionDate,
  formatTransactionTime,
  getBankName,
  maskAccountNumber,
} from "~/utils/transactionHelpers";

interface Props {
  transaction: Transaction;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "view-detail": [transaction: Transaction];
}>();
</script>
