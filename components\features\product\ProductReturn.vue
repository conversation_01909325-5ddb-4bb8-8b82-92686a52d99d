<template>
  <li
    class="text-gray-700 text-sm sm:text-base flex items-center gap-2 w-auto my-1 cursor-pointer"
    @click="handleChooseItem"
  >
    <img
      :src="image || 'https://via.placeholder.com/300'"
      alt=""
      class="object-contain w-8 h-8 mt-1 rounded-lg"
      loading="lazy"
    />
    <div class="flex justify-between w-full">
      <h3 class="text-sm">
        {{ product.orderItemName }}
      </h3>
    </div>
  </li>
</template>

<script setup>
const props = defineProps(["product"]);
const productStore = useProductStore();
const image = ref("");
const {} = usePortal();
onMounted(async () => {
  if (props.product.variant.id) {
    const url = getImageProducrUrl(props.product?.variant?.id, "PRODUCT");
    image.value = url;
  }
});
const handleChooseItem = async () => {
  console.log("sản phẩm mình chọn là", props.product);
};
</script>
