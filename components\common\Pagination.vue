<template>
  <div class="flex items-center justify-center space-x-4 py-2 border-t">
    <button
      @click="handlePrePage"
      :disabled="currentPage == 1"
      class="rounded-full p-2 text-gray-400 hover:bg-gray-200 transition duration-200 disabled:opacity-50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
          clip-rule="evenodd"
        />
      </svg>
    </button>
    <span class="text-gray-600 font-medium">
      {{ currentPage }} / {{ totalPages }}
    </span>
    <button
      @click="handleNextPage"
      :disabled="props.currentPage === props.totalPages"
      class="rounded-full p-2 text-gray-400 hover:bg-gray-200 transition duration-200 disabled:opacity-50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
          clip-rule="evenodd"
        />
      </svg>
    </button>
  </div>
</template>

<script setup>
const props = defineProps(["currentPage", "totalPages"]);
const emits = defineEmits(["nextPage", "prePage"]);
const handleNextPage = () => {
  emits("nextPage");
};
const handlePrePage = () => {
  emits("prePage");
};
</script>
