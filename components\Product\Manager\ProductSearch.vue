<template>
  <div class="w-full">
    <!-- Desktop Search Layout -->
    <div class="hidden md:flex items-center gap-3">
      <!-- Search Input -->
      <div class="relative flex-1">
        <input
          type="text"
          placeholder="T<PERSON><PERSON> kiếm sản phẩm theo tên, mã SP, SKU..."
          class="w-full pl-10 pr-10 py-3 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
          v-model="keyword"
          @keydown.enter="handleSearch"
          @input="handleInputChange"
        />

        <!-- Search Icon -->
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <!-- Loading/Clear Icon -->
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <div
            v-if="isLoading"
            class="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"
          ></div>
          <button
            v-else-if="keyword"
            @click="clearInput"
            class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-2">
        <button
          @click="handleSearch"
          class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-all duration-200 flex items-center gap-2 font-medium"
          :disabled="isLoading"
          :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
        >
          <svg
            v-if="!isLoading"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <div
            v-else
            class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
          ></div>
          Tìm kiếm
        </button>

        <button
          @click="clearQuery"
          class="bg-gray-100 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-200 transition-all duration-200 flex items-center gap-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Làm mới
        </button>
      </div>
    </div>

    <!-- Mobile Search Layout -->
    <div class="md:hidden space-y-3">
      <!-- Search Input -->
      <div class="relative">
        <input
          type="text"
          placeholder="Tìm kiếm sản phẩm..."
          class="w-full pl-10 pr-10 py-3 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          v-model="keyword"
          @keydown.enter="handleSearch"
          @input="handleInputChange"
        />

        <!-- Search Icon -->
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        <!-- Loading/Clear Icon -->
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <div
            v-if="isLoading"
            class="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"
          ></div>
          <button
            v-else-if="keyword"
            @click="clearInput"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Action Buttons -->
      <div class="flex gap-2">
        <button
          @click="handleSearch"
          class="flex-1 bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center gap-2 font-medium"
          :disabled="isLoading"
          :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
        >
          <svg
            v-if="!isLoading"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <div
            v-else
            class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"
          ></div>
          Tìm kiếm
        </button>

        <button
          @click="clearQuery"
          class="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const keyword = ref<string>("");
const emits = defineEmits(["search", "clearQuery"]);
const props = defineProps(["isLoading"]);

const handleSearch = () => {
  const data = {
    keyword: keyword.value,
    category: selectedCategory.value,
  };
  emits("search", data);
};

const clearQuery = () => {
  keyword.value = "";
  selectedCategory.value = "";
  const data = {
    keyword: keyword.value,
    category: selectedCategory.value,
  };
  emits("clearQuery", data);
};

const clearInput = () => {
  keyword.value = "";
  const data = {
    keyword: keyword.value,
    category: selectedCategory.value,
  };
  emits("clearQuery", data);
};

// Debounced input handler for better performance
let searchTimeout: NodeJS.Timeout | null = null;
const handleInputChange = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  // Auto search after 500ms of no typing
  searchTimeout = setTimeout(() => {
    if (keyword.value && keyword.value.length >= 2) {
      handleSearch();
    }
  }, 500);
};

const { getCategory } = useProduct();
const selectedCategory = ref<string>("");
const listCategories = ref<any>([]);

const handleGetCategories = async () => {
  try {
    const response = await getCategory("", 1);
    listCategories.value = response;
  } catch (error) {
    throw error;
  }
};

const handleChangeCategory = () => {
  handleSearch();
};

onMounted(async () => {
  await handleGetCategories();
});

onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});
</script>
