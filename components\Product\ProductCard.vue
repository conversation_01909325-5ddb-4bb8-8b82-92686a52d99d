<template>
  <div
    class="border cursor-pointer border-neutral-200 rounded-md hover:shadow-lg flex flex-col w-[148px] md:w-[192px] overflow-hidden"
    data-testid="product-card-vertical"
    @click="addProduct(product)"
  >
    <div class="relative overflow-hidden">
      <NuxtImg
        data-testid="image-slot"
        :alt="product.name"
        :src="handleGetImageProductUrl()"
        class="object-contain w-full h-[148px] md:h-[156px]"
        loading="lazy"
        preload
      />
    </div>
    <div
      class="p-2 md:p-2 border-t border-neutral-200 h-full flex flex-col text-neutral-900"
    >
      <div
        class="focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm hover:text-primary-800 active:text-primary-900 text-sm text-inherit no-underline hover:underline hover:!text-primary-800 active:text-primary-900 visited:!text-inherit line-clamp-2 h-10"
        data-testid="link"
      >
        {{ product.title }}
      </div>
      <span
        class="block font-bold text-sm text-neutral-900 mt-2 md:mt-0"
        data-testid="product-card-vertical-price"
      >
        {{
          product.price !== null && product.price !== undefined
            ? formatCurrency(product.price)
            : "Chưa có giá"
        }}
      </span>
    </div>

    <div
      v-if="isLoading"
      class="fixed z-[999999999] inset-0 flex items-center justify-center bg-black bg-opacity-50 pointer-events-none"
    >
      <LoadingSpinner />
    </div>
  </div>
  <ModalProductDetail
    v-if="isModalOpen"
    :isOpen="isModalOpen"
    :productId="product.id"
    @close="closeModal"
  />
</template>

<script setup>
const { addProductToOrder, handleCheckInventory } = useOrderStore();
const isModalOpen = ref(false);
const { product } = defineProps({
  product: {
    type: Object,
    required: true,
  },
});
const isLoading = ref(false);
const addProduct = async () => {
  if (product.subType !== "VARIABLE") {
    isLoading.value = true;
    await addProductToOrder(product);
    isLoading.value = false;
    return;
  }
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
};
const { getImageProducrUrl } = usePortal();
const handleGetImageProductUrl = () => {
  const url = getImageProducrUrl(product.id, "PRODUCT");
  return url;
};
watchEffect(() => {
  if (isLoading.value) {
    document.body.classList.add("loading");
  } else {
    document.body.classList.remove("loading");
  }
});
</script>
<style>
body.loading {
  pointer-events: none;
}
</style>
