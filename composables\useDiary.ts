import type { Auth } from "~/types/Auth";
import { useDiariesStore } from "../stores/diaries";
export default function useOrder() {
  const { $sdk } = useNuxtApp();

  const route = useRoute();
  const router = useRouter();
  const diariesChange = ref(0);
  const diaryIdActive = ref("");
  const isLoadingRemoveDiary = ref(false);
  const diariesStore = useDiariesStore();
  const saleStore = useSale();
  const handleDiariesChange = () => {
    diariesChange.value += 1;
  };
  const getDiaries = async (query: any) => {
    try {
      const response = await $sdk.order.getListSellOrder({
        status: [1],
        source: "diary",
        currentPage: 1,
        maxResult: 5,
      });
      diariesStore.setDiaries(response.data.data);
      return response;
    } catch (error) {
      console.error("Error fetching diaries:", error);
      throw error;
    }
  };

  const handleActiveDiary = async (diaryId: string) => {
    router.push({
      path: "/diaries",
      query: {
        ...router.currentRoute.value.query,
        orderId: diaryId,
      },
    });
  };
  const checkIndexDiaryActive = (diaryId: string) => {
    return diariesStore.diaries.findIndex((diary) => diary.id === diaryId);
  };
  const removeDraftDiary = async (orderId: string, updatedBy: string) => {
    isLoadingRemoveDiary.value = true;
    const indexActive = checkIndexDiaryActive(orderId);
    try {
      const response = await $sdk.order.removeDraftOrder(orderId, updatedBy);
      // await getDiaries();
      if (route.query.orderId === orderId) {
        const diariesLength = diariesStore.diaries.length;
        if (diariesLength === indexActive) {
          handleActiveDiary(diariesStore.diaries[indexActive - 1].id);
        }
      }
      isLoadingRemoveDiary.value = false;
      return response;
    } catch (error) {
      isLoadingRemoveDiary.value = false;
      throw error;
    }
  };
  const createSellingDiary = async () => {
    const warehouseId = useCookie("warehouseId").value;

    try {
      const response = await $sdk.order.createOrderTemp(
        {
          status: "DRAFT",
          orderType: "POS_SALE",
          platform: "WEB",
          source: "diary",
          time: new Date().getTime(),
          warehouseId: warehouseId,
        },
        "WEB"
      );
      const auth = useCookie("auth").value as unknown as Auth;

      updateSaleEmployee(
        response?.data?.orderId,
        auth?.user?.id,
        auth?.user?.id
      );
      router.push({
        path: "/diary",
        query: {
          ...router.currentRoute.value.query,
          orderId: response.data.orderId,
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error creating order:", error);
    }
  };
  const updateSaleEmployee = async (
    orderId: string,
    saleId: string,
    updatedBy: string
  ) => {
    try {
      const response = await $sdk.order.updateSaleEmployee(
        orderId,
        saleId,
        updatedBy
      );
      return response;
    } catch (error) {
      throw error;
    }
  };
  const handleInitialDiary = async () => {
    const response = await createSellingDiary();
    if (response) {
      handleActiveDiary(response.data.orderId);
    }
  };
  const updateNoteDiary = async (note: string) => {
    const orderId = route.query.orderId as string;
    const createdBy = "admin";
    try {
      const response = await $sdk.order.createNoteWithoutLogin(
        orderId,
        createdBy,
        note
      );
      return response;
    } catch (error) {
      console.error("Error update note diary:", error);
      throw error;
    }
  };
  const updateStatsDiary = async (orderId: string, status: string) => {
    try {
      const response = await $sdk.order.updateStatusSellOrder(orderId, status);
      // await getDiaries();

      const indexActive = checkIndexDiaryActive(orderId);
      if (route.query.orderId === orderId) {
        const diariesLength = diariesStore.diaries.length;
        if (diariesLength === indexActive) {
          handleActiveDiary(diariesStore.diaries[indexActive - 1].id);
        }
      }
      return response;
    } catch (error) {
      console.error("Error update stats diary:", error);
      throw error;
    }
  };
  const updateOrderDescription = async (
    orderId: string,
    description: string
  ) => {
    try {
      const response = await $sdk.order.updateOrderDescription(
        orderId,
        description
      );
      return response;
    } catch (error) {
      console.error("Error update order description:", error);
      throw error;
    }
  };
  const getOrderDetail = async (orderId: string) => {
    try {
      const response = await $sdk.order.getInfoSellOrder(orderId);
      return response.data;
    } catch (error) {
      console.error("Error get order detail:", error);
      throw error;
    }
  };
  const updateQuantityInDiary = async (
    orderId: string,
    productId: string,
    quantity: number
  ) => {
    try {
      const response = await $sdk.order.updateQuantityProductInOrder(
        orderId,
        productId,
        quantity
      );
      return response;
    } catch (error) {
      console.error("Error get update quantity:", error);
      throw error;
    }
  };
  return {
    diariesChange,
    diaryIdActive,
    isLoadingRemoveDiary,
    handleDiariesChange,
    handleActiveDiary,
    createSellingDiary,
    getDiaries,
    handleInitialDiary,
    removeDraftDiary,
    updateNoteDiary,
    updateStatsDiary,
    updateOrderDescription,
    getOrderDetail,
    updateQuantityInDiary,
  };
}
