<template>
  <div :style="myStyles">
    <Doughnut :data="chartData" :options="chartOptions" />
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { Doughnut } from "vue-chartjs";
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  DoughnutController,
  CategoryScale,
  LinearScale,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import DoughnutLabel from "chartjs-plugin-doughnutlabel-rebourne";

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  DoughnutController,
  CategoryScale,
  LinearScale,
  ChartDataLabels,
  DoughnutLabel
);

const myStyles = computed(() => ({
  height: "400px",
  width: "100%",
  position: "relative",
}));

const chartData = ref({
  labels: [],
  datasets: [
    {
      label: "Total Revenue",
      backgroundColor: [
        "#FF6384",
        "#36A2EB",
        "#FFCE56",
        "#4BC0C0",
        "#9966FF",
        "#FF9F40",
      ],
      data: [],
    },
  ],
});

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  cutout: "60%",
  plugins: {
    doughnutlabel: {
      labels: [
        {
          text: "Tổng",
          font: {
            size: "20",
            weight: "bold",
          },
          color: "#36A2EB",
        },
        {
          text: "0",
          font: {
            size: "30",
            weight: "bold",
          },
          color: "#36A2EB",
        },
        {
          text: "VNĐ",
          font: {
            size: "20",
            weight: "bold",
          },
          color: "#36A2EB",
        },
      ],
    },
    datalabels: false,
    title: {
      display: true,
    },
  },
  interaction: {
    mode: "index",
    intersect: false,
  },
  stacked: false,
});

const props = defineProps(["dataPayment"]);

const updateChartData = () => {
  const labels = props.dataPayment?.reportDTOS?.map((item) => item.name) || [];
  const data =
    props.dataPayment?.reportDTOS?.map((item) => item.totalAmountRevenue) || [];

  chartData.value = {
    labels: labels,
    datasets: [
      {
        label: "Total Revenue",
        backgroundColor: [
          "#FF6384",
          "#36A2EB",
          "#FFCE56",
          "#4BC0C0",
          "#9966FF",
          "#FF9F40",
        ],
        data: data,
      },
    ],
  };

  const totalAmountRevenue = data.reduce((acc, curr) => acc + curr, 0);
  chartOptions.value.plugins.doughnutlabel.labels[1].text =
    totalAmountRevenue.toLocaleString();
};

watch(
  () => props.dataPayment,
  () => {
    updateChartData();
  },
  { immediate: true }
);
</script>

<style scoped></style>
