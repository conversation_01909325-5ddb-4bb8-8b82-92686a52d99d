import { defineStore } from "pinia";
import { ref } from "vue";

export const returnOrderStore = defineStore("return", () => {
  const { getOrderDetail, fetchOrderReturnDetails } = useOrder();
  const orderReturn = ref<any>();
  const orderChooseReturn = ref<any>([]);
  const orderSwap = ref<any>();
  const totalProductReturn = ref(0);
  const totalPriceProductReturn = ref(0);
  const feeReturnOrder = ref(0);
  const noteOrderReturn = ref();
  const employeeCreateOrderReturn = ref();
  const isSwapOrder = ref(false);
  const orderStore = useOrderStore();

  const getOrderReturn = async (orderId: string) => {
    orderStore.orderDetail = null;
    orderStore.dataListProductDiary = null;
    orderChooseReturn.value = [];
    orderSwap.value = [];
    orderReturn.value = null;
    isSwapOrder.value = false;
    totalProductReturn.value = 0;
    totalPriceProductReturn.value = 0;
    noteOrderReturn.value = "";
    feeReturnOrder.value = 0;
    try {
      const response = await getOrderDetail(orderId);
      orderReturn.value = response;
    } catch (error) {
      throw error;
    }
  };
  const addProductToOrderChooseReturn = (product: any) => {
    orderChooseReturn.value.push(product);
    calculatorPrice();
  };
  const removeProductFromOrderChooseReturn = (product: any) => {
    orderChooseReturn.value = orderChooseReturn.value.filter(
      (productItem: any) => productItem.id !== product.id
    );
    calculatorPrice();
  };
  const handleIncreaseQuantity = (product: any) => {
    const item = orderReturn.value.activeOrderItemProfiles.find(
      (item: any) => item.id === product.id
    );

    if (item) {
      if (item.orderLineItem.currentQuantity < item.orderLineItem.quantity) {
        item.orderLineItem = {
          ...item.orderLineItem,
          currentQuantity: item.orderLineItem.currentQuantity + 1,
        };
        handleDecreaseQuantityOrderChoose(
          product,
          item.orderLineItem.currentQuantity
        );
        orderReturn.value.activeOrderItemProfiles = [
          ...orderReturn.value.activeOrderItemProfiles,
        ];
        calculatorPrice();
      }
    }
  };

  const handleDecreaseQuantityOrderChoose = (
    product: any,
    quantity: number
  ) => {
    const index = orderChooseReturn.value.findIndex(
      (item: any) => item.id === product?.id
    );
    if (index !== -1) {
      orderChooseReturn.value[index] = {
        ...orderChooseReturn.value[index],
        orderLineItem: {
          ...orderChooseReturn.value[index].orderLineItem,
          currentQuantity: (orderChooseReturn.value[
            index
          ].orderLineItem.currentQuantity = quantity),
        },
      };
      orderChooseReturn.value = [...orderChooseReturn.value];
      // calculatorPrice();
    }
  };

  const handleDecreaseQuantity = (product: any) => {
    const itemIndex = orderReturn.value.activeOrderItemProfiles.findIndex(
      (item: any) => item.id === product.id
    );
    if (itemIndex !== -1) {
      const item = orderReturn.value.activeOrderItemProfiles[itemIndex];
      if (item.orderLineItem.currentQuantity > 1) {
        orderReturn.value.activeOrderItemProfiles[itemIndex] = {
          ...item,
          orderLineItem: {
            ...item.orderLineItem,
            currentQuantity: item.orderLineItem.currentQuantity - 1,
          },
        };
        handleIncreaseQuantityOrderChoose(
          product,
          orderReturn.value.activeOrderItemProfiles[itemIndex]?.orderLineItem
            ?.currentQuantity
        );
        orderReturn.value.activeOrderItemProfiles = [
          ...orderReturn.value.activeOrderItemProfiles,
        ];
        calculatorPrice();
      }
    }
  };

  const handleIncreaseQuantityOrderChoose = (
    product: any,
    quantity: number
  ) => {
    const index = orderChooseReturn.value.findIndex(
      (item: any) => item.id === product?.id
    );

    if (index !== -1) {
      const item = orderChooseReturn.value[index];
      orderChooseReturn.value[index] = {
        ...item,
        orderLineItem: {
          ...item.orderLineItem,
          currentQuantity: (item.orderLineItem.currentQuantity = quantity),
        },
      };
      orderChooseReturn.value = [...orderChooseReturn.value];
      // calculatorPrice();
    }
  };
  //

  const calculatorPrice = () => {
    const { totalQuantity, totalPrice } = orderChooseReturn.value.reduce(
      (acc: any, order: any) => {
        const quantity = order?.orderLineItem?.currentQuantity || 0;
        const price =
          quantity * (order?.orderLineItem?.realPriceSell?.amount || 0);

        return {
          totalQuantity: acc.totalQuantity + quantity,
          totalPrice: acc.totalPrice + price,
        };
      },
      { totalQuantity: 0, totalPrice: 0 }
    );

    totalProductReturn.value = totalQuantity;
    totalPriceProductReturn.value = totalPrice;
  };

  const handleSwitchType = (type: string) => {
    console.log("type", type);
    if (type === "Đổi hàng") {
      isSwapOrder.value = true;
    } else {
      isSwapOrder.value = false;
    }
  };
  const paymentMethodReturnOrder = ref({
    id: "1",
    name: "Hoàn tiền",
    paymentMethod: "refund",
    type: "REFUND_INVOICE",
  });
  const orderReturnDetail = ref();
  const getDetailOrderReturn = async (orderId: string) => {
    try {
      const response = await fetchOrderReturnDetails(orderId);
      orderReturnDetail.value = response?.data;
      console.log("orderReturnDetail", orderReturnDetail.value);
    } catch (error) {
      throw error;
    }
  };
  return {
    orderReturn,
    getOrderReturn,
    addProductToOrderChooseReturn,
    removeProductFromOrderChooseReturn,
    orderChooseReturn,
    orderSwap,
    handleIncreaseQuantity,
    handleDecreaseQuantity,
    totalProductReturn,
    totalPriceProductReturn,
    feeReturnOrder,
    noteOrderReturn,
    employeeCreateOrderReturn,
    isSwapOrder,
    handleSwitchType,
    paymentMethodReturnOrder,
    getDetailOrderReturn,
    orderReturnDetail,
  };
});
