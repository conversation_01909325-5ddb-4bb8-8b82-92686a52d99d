<template>
  <div>
    <div
      class="flex flex-col flex-auto border-l bg-white h-[100vh] mt-5 hidden md:block"
    >
      <div class="h-full overflow-y-scroll">
        <div class="w-full relative h-full">
          <div class="wrapper my-0 px-2 h-full">
            <div class="w-full relative h-full-custom">
              <div class="hidden">
                <TabOrder></TabOrder>
              </div>
              <div
                class="w-full col-span-2 bg-white rounded h-full"
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  color: #0d47a1;
                "
              >
                <div></div>
                <!-- <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="size-5 hidden md:block"
                  style="cursor: pointer"
                  @click="closeChatSale"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.793 2.232a.75.75 0 0 1-.025 1.06L3.622 7.25h10.003a5.375 5.375 0 0 1 0 10.75H10.75a.75.75 0 0 1 0-1.5h2.875a3.875 3.875 0 0 0 0-7.75H3.622l4.146 3.957a.75.75 0 0 1-1.036 1.085l-5.5-5.25a.75.75 0 0 1 0-1.085l5.5-5.25a.75.75 0 0 1 1.06.025Z"
                    clip-rule="evenodd"
                  />
                </svg> -->
                <h6 class="text-center font-bold">TẠO ĐƠN HÀNG</h6>
                <div></div>
              </div>
              <div class="w-full col-span-2 relative bg-white rounded h-full">
                <div class="md:block hidden border-b">
                  <TypeOrderComponent
                    @isDelivery="handleDelivery"
                  ></TypeOrderComponent>
                </div>
                <SearchProduct></SearchProduct>
                <OrderItems :isTrue="true"></OrderItems>
              </div>
              <div class="w-full col-span-2 mb-24">
                <div class="flex flex-col gap-2">
                  <div
                    class="w-full col-span-2 md:col-span-1 order-1 md:order-2 mb-24"
                  >
                    <div class="flex flex-col gap-2">
                      <UserDetail
                        v-if="customer"
                        @setValueShippingAddress="setValueShippingAddress"
                      ></UserDetail>
                      <SearchUser v-else></SearchUser>
                      <Campaign v-if="campaign"></Campaign>
                      <InfoPayment></InfoPayment>
                      <EditEmployee></EditEmployee>
                      <FooterOrder></FooterOrder>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ModalCreateShippingAddress
          v-if="isShowCreateShippingAddress"
          @closeModalCreateShippingAddress="closeModalCreateShippingAddress"
          :customer="customer"
        ></ModalCreateShippingAddress>
        <ModalManageAddressShip
          v-if="isShowListShippingAddress"
          @closeModalManageAddressShip="closeModalEditShippingAddress"
          :customer="customer"
        ></ModalManageAddressShip>
        <ModalCreateOrder v-if="orderStore.isAlert"></ModalCreateOrder>
      </div>
    </div>
    <div class="block md:hidden">
      <div
        v-if="showModal"
        class="fixed inset-0 flex items-center justify-center z-50"
      >
        <div
          class="absolute inset-0 bg-black opacity-50"
          @click="closeChatSale"
        ></div>
        <div
          class="relative bg-white rounded-lg shadow-lg max-w-3xl w-full h-[100vh]"
        >
          <div class="flex flex-col p-4 h-full overflow-y-scroll">
            <div class="flex justify-between items-center border-b pb-2 mb-4">
              <div></div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="size-5 hidden md:block"
                style="cursor: pointer"
                @click="closeChatSale"
              >
                <path
                  fill-rule="evenodd"
                  d="M7.793 2.232a.75.75 0 0 1-.025 1.06L3.622 7.25h10.003a5.375 5.375 0 0 1 0 10.75H10.75a.75.75 0 0 1 0-1.5h2.875a3.875 3.875 0 0 0 0-7.75H3.622l4.146 3.957a.75.75 0 0 1-1.036 1.085l-5.5-5.25a.75.75 0 0 1 0-1.085l5.5-5.25a.75.75 0 0 1 1.06.025Z"
                  clip-rule="evenodd"
                />
              </svg>
              <h6 class="text-center font-bold">TẠO ĐƠN HÀNG</h6>
              <!-- Minimize button for mobile view -->
              <button @click="minimizeChat" class="text-gray-500 text-lg">
                &minus;
              </button>
            </div>

            <div class="w-full relative bg-white rounded h-full-custom">
              <div class="md:block hidden border-b">
                <TypeOrderComponent
                  @isDelivery="handleDelivery"
                ></TypeOrderComponent>
              </div>
              <SearchProduct></SearchProduct>
              <OrderItems :isTrue="true"></OrderItems>

              <div class="flex flex-col gap-2 mt-4">
                <UserDetail
                  v-if="customer"
                  @setValueShippingAddress="setValueShippingAddress"
                ></UserDetail>
                <SearchUser v-else></SearchUser>
                <Campaign v-if="campaign"></Campaign>
                <InfoPayment></InfoPayment>
                <EditEmployee></EditEmployee>
                <FooterOrder></FooterOrder>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else
        @click="restoreChat"
        class="fixed top-[350px] right-2 bg-blue-500 text-white p-3 rounded-full shadow-lg z-50 cursor-pointer"
      >
        <div class="flex">
          <span>
            <svg
              class="w-6 h-6 stroke-current"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
              ></path>
            </svg>
          </span>
          <p>Đơn Hàng</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const route = useRoute();
const router = useRouter();
const emit = defineEmits(["closeChatSale", "closeSidebar", "openSideBar"]);
const showModal = ref(true);

// Stores and methods
const { getFeaturedProducts } = useProductStore();
const orderStore = useOrderStore();
const {
  getOrderById,
  addCustomerToOrder,
  getDataShippingAddress,
  valueShippingAddress,
  getShippingCarrier,
  getShippingServices,
  dataShippingCarrier,
} = orderStore;

const listOrderTerm = ref<any>([]);
const selectedOrderTerm = ref<any>(null);
const numberOrder = ref(1);
const customer = computed(() => orderStore.customerInOrder);
const isDelivered = ref<string>(valueShippingAddress);
const isShowShippingAddress = ref<boolean>(false);
const isShowCreateShippingAddress = ref<boolean>(false);
const isShowListShippingAddress = ref<boolean>(false);
const campaign = computed(() => orderStore.campaign);
const isShippingOpen = ref(false);

// Event handlers
const closeChatSale = () => {
  emit("closeChatSale");
  showModal.value = false;
};
const minimizeChat = () => {
  showModal.value = false;
};

const restoreChat = () => {
  showModal.value = true;
};
const handleClickOrderTerm = (orderTerm: any) => {
  selectedOrderTerm.value = orderTerm.id;
  orderStore.orderDetail = null;
  orderStore.customerInOrder = null;
};

const handleCreateShippingAddress = () => {
  isShowCreateShippingAddress.value = true;
};

const closeModalCreateShippingAddress = (value: boolean) => {
  isShowCreateShippingAddress.value = value;
};

const handleListShippingAddress = () => {
  isShowListShippingAddress.value = true;
};

const closeModalEditShippingAddress = (value: boolean) => {
  isShowListShippingAddress.value = value;
};

const setValueShippingAddress = (value: string) => {
  isShowShippingAddress.value = false;
  isDelivered.value = value;
};

const handleDelivery = (value: any) => {
  isShippingOpen.value = value;
};

// Watchers
watch(
  () => isDelivered.value,
  async (newValue) => {
    orderStore.valueShippingAddress = newValue;
    if (newValue === "delivery") {
      if (customer.value?.id) {
        orderStore.getDataShippingAddress(customer.value?.id);
      }
      isShowShippingAddress.value = true;
    } else {
      isShowShippingAddress.value = false;
    }
  }
);

watch(
  () => route.query.roomId,
  async (newRoomId, oldRoomId) => {
    if (newRoomId && newRoomId !== oldRoomId) {
      // Room orders functionality removed - create new order instead
      await handleCreateOrderTemp();
    }
  }
);

// Room orders functionality has been removed with SQLite
// Orders are no longer persisted per room

const handleCreateOrderTemp = async () => {
  const newOrderId = `order-${numberOrder.value}`;

  listOrderTerm.value.push({ id: newOrderId });
  selectedOrderTerm.value = newOrderId;
  orderStore.orderDetail = null;
  orderStore.customerInOrder = null;
  numberOrder.value++;

  // Room orders functionality removed - no longer saving order by room ID

  await router.push({
    path: `/chat`,
    query: {
      ...route.query,
      orderId: newOrderId,
    },
  });
};

const fetchData = async () => {
  const { orderId } = route.query;

  if (orderId) {
    try {
      await Promise.all([
        getOrderById(orderId as string),
        getFeaturedProducts(),
      ]);
      selectedOrderTerm.value = orderId;

      // Room orders functionality removed - no longer saving order by room ID
    } catch (error) {
      console.error("Error fetching data or saving order ID:", error);
    }
  } else {
    await handleCreateOrderTemp();
  }
};
watch(
  () => route.query.orderId,
  async (newOrderId, oldOrderId) => {
    if (newOrderId && newOrderId !== oldOrderId) {
      await fetchData();
    }
  },
  { immediate: true }
);
onMounted(async () => {
  const { orderId } = route.query;

  if (!orderId) {
    await handleCreateOrderTemp();
  }
});
</script>

<style scoped></style>
