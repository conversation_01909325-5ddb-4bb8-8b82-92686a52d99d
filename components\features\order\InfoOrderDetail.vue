<template>
  <div class="col-span-6 bg-white px-2 py-1">
    <h2 class="text-lg font-semibold text-primary mb-2">Thông tin đơn hàng</h2>
    <div class="space-y-2 text-sm">
      <div>
        <span class="text-gray-600 font-medium">Mã đơn: </span>
        <span class="text-gray-900">#{{ orderDetail?.id }}</span>
      </div>
      <!-- <div>
        <span class="text-gray-600 font-medium">Nhân viên tạo đơn: </span>
        <span class="text-gray-900">{{ employeeCreate }}</span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">Nhân viên tư vấn: </span>
        <span class="text-gray-900">{{ employeeSale }}</span>
      </div> -->
      <div>
        <span class="text-gray-600 font-medium">Ng<PERSON><PERSON> tạo: </span>
        <span class="text-gray-900">{{
          formatTimestampV2(orderDetail?.order?.createdStamp)
        }}</span>
      </div>

      <div>
        <span class="text-gray-600 font-medium">Trạng thái: </span>
        <span :class="getOrderStatusClass(orderDetail?.status, 'background')">
          {{ orderDetail?.statusDescription }}
        </span>
      </div>

      <div>
        <span class="text-gray-600 font-medium">Loại đơn: </span>
        <span class="ml-2 mr-3 whitespace-nowrap rounded px-2 py-0.5">
          {{ handleOrderType(orderDetail?.order?.orderType) }}
        </span>
        <span class="text-gray-900">{{}}</span>
      </div>
      <!-- <div>
        <span class="text-gray-600 font-medium">Fulfillment: </span>
        <span class="ml-2 mr-3 whitespace-nowrap rounded px-2 py-0.5 text-sm">{{
          orderDetail?.order.fulfillmentStatus
        }}</span>
      </div> -->
      <div>
        <span class="text-gray-600 font-medium">Trạng thái thanh toán: </span>
        <span
          :class="
            getPaymentStatusClass(
              orderDetail?.financialStatusDescription,
              'background'
            )
          "
          >{{ orderDetail?.financialStatusDescription }}</span
        >
      </div>
      <!-- <div>
        <span class="text-gray-600 font-medium">Hình thức vận chuyển: </span>
        <span class="text-red-500 font-semibold">
          <span> </span>
        </span>
      </div> -->
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const { searchEmployes } = useOrder();
// const employeeCreate = computed(() =>
//   handleSearchEmployee(orderStore.orderDetail)
// );
const handleSearchEmployee = async (id: string) => {
  try {
    const data = {
      keyword: id,
      positionShortName: "",
    };
    const response = await searchEmployes(data);
    return response[0]?.name;
  } catch (error) {
    throw error;
  }
};
const handleOrderType = (orderType: string) => {
  switch (orderType) {
    case "POS_SALE":
      return "Tại quầy";
    case "SHIPMENT":
      return "Vận chuyển";

    default:
      break;
  }
};
// Import utilities
import {
  getPaymentStatusClass,
  getOrderStatusClass,
} from "~/utils/statusHelpers";
const employeeCreate = ref();
const employeeSale = ref();
watch(orderDetail, async () => {
  employeeCreate.value = await handleSearchEmployee(
    orderDetail.value?.order?.createdBy
  );
  employeeSale.value = await handleSearchEmployee(
    orderDetail.value?.order?.salePartyId
  );
});
</script>
