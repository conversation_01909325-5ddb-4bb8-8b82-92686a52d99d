<template>
  <div>
    <div v-if="loading || loadingCreateOrder || isLoad">
      <LoadingSpinner />
    </div>
    <div class="h-full overflow-y-scroll md:overflow-hidden">
      <div class="items-center gap-[10px] mt-2 px-2"></div>
      <div class="w-full relative h-full">
        <div class="wrapper lg:my-0 my-0 px-2 md:[px-15] h-full">
          <div class="w-full relative h-full-custom">
            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-2 md:mb-4 md:h-full"
            >
              <div
                class="w-full col-span-2 relative bg-white rounded h-full order-2 md:order-1 max-h-screen overflow-y-auto"
              >
                <SearchProduct></SearchProduct>
                <DiaryItems></DiaryItems>

                <ProductOutStanding></ProductOutStanding>
              </div>

              <div
                class="w-full col-span-2 md:col-span-1 order-1 md:order-2 bg-white"
              >
                <div
                  class="flex flex-col gap-1 md:overflow-y-scroll md:h-screen-150 md:relative"
                >
                  <div class="border-b pb-1">
                    <UserDetail v-if="customer"></UserDetail>
                    <SearchUser v-else></SearchUser>
                  </div>
                  <div class="border-b pb-1">
                    <TagOrder></TagOrder>
                  </div>
                  <OrderNoteExtend></OrderNoteExtend>
                </div>
                <div class="p-2">
                  <FooterDiary></FooterDiary>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Cấu hình trang
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Nhật ký",
});

useHead({
  title: "Nhật ký bán hàng",
  meta: [{ name: "description", content: "Bán hàng" }],
});

// Store và reactive state
const orderStore = useOrderStore();
const productStore = useProductStore();
const tagStore = useTagStore();
const { createConnector } = usePortal();
const route = useRoute();

const loading = computed(() => orderStore.loading);
const loadingCreateOrder = computed(() => orderStore.loadingCreateOrder);
const customer = computed(() => orderStore.customerInOrder);
const dataConnector = computed(() => tagStore.dataConnector);
const isLoad = ref(false);

// Hàm tải dữ liệu
const fetchOrderData = async (orderId: string) => {
  if (!orderId) return;
  isLoad.value = true;
  try {
    await Promise.all([
      orderStore.getOrderById(orderId),
      productStore.getFeaturedProducts(),
    ]);
    await tagStore.handleGetConnectorByResource(orderId, "ORDER", "TAG");
    await handleConnector(orderId);
  } catch (error) {
    console.error("Error fetching order data:", error);
  } finally {
    isLoad.value = false;
  }
};

const handleConnector = async (resourceId: string) => {
  if (!resourceId || dataConnector.value) return;
  try {
    await createConnector(resourceId, "ORDER", "", "TAG", "");
    await tagStore.handleGetConnectorByResource(resourceId, "ORDER", "TAG");
  } catch (error) {
    console.error("Error handling connector:", error);
  }
};

onMounted(() => fetchOrderData(route.query.orderId as string));

watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    if (newVal) {
      fetchOrderData(newVal as string);
    }
  }
);
</script>

<style scoped>
.h-full-custom {
  height: calc(100vh - 4rem);
}
</style>
