<template>
  <div class="col-span-3 md:col-span-2 bg-white border rounded-lg p-2 text-sm">
    <div class="flex flex-row items-center gap-1">
      <h2 class="text-lg font-semibold text-primary">Thông tin khách hàng</h2>
      <svg
        @click="handleOpenEditCustomer"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 16 16"
        fill="#3F51B5"
        class="size-4 mt-1 cursor-pointer"
      >
        <path
          d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z"
        />
        <path
          d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z"
        />
      </svg>
    </div>
    <div class="grid grid-cols-2 gap-4">
      <!-- cột 1  -->
      <div class="space-y-2 text-sm p-2">
        <div>
          <span class="text-gray-600 font-medium">Mã khách hàng: </span>
          <span class="text-gray-900">#{{ customer?.id }}</span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Tên khách hàng: </span>
          <span class="text-gray-900 ml-2">{{ customer?.name }} </span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Địa chỉ: </span>
          <span class="text-gray-900">{{ customer?.address }}</span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Giới tính: </span>
          <span class="text-gray-900">{{ customer?.gender }}</span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Ngày sinh: </span>
          <span v-if="customer?.birthDate" class="text-gray-900">{{
            formatTimestampV2(customer?.birthDate)
          }}</span>
        </div>
      </div>
      <!-- Cột 2 -->
      <div class="space-y-2 text-sm p-2">
        <div>
          <span class="text-gray-600 font-medium">Email: </span>
          <span class="text-gray-900">{{ customer?.email }}</span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Số điện thoại: </span>
          <span class="text-gray-900">{{ customer?.phone }}</span>
        </div>
        <div>
          <span class="text-gray-600 font-medium">Hạng: </span>
          <span :class="handleMemberLevel(customer?.memberLevel)">{{
            customer?.memberLevel || "Chưa có hạng"
          }}</span>
        </div>
        <div>
          <span class="font-semibold">Mã ví: </span>
          <span class="">{{ wallet?.details?.data?.accountNumber }}</span>
        </div>
        <div>
          <span class="font-semibold">Ngày tạo: </span>
          <span>{{ formatTimestampV2(wallet?.details?.data?.created) }}</span>
        </div>
        <div>
          <span class="font-semibold">Số dư: </span>
          <span class="text-primary font-bold">{{
            formatCurrency(wallet?.details?.data?.balance || 0)
          }}</span>
        </div>
      </div>
    </div>
    <div v-if="isModalEditCustomer" class="">
      <ModalEditCustomer
        @closeModalEditCustomer="closeModalEditCustomer"
        :customer="customer"
        :isManagerCustomer="true"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  customer: Object,
  wallet: Object,
});
const isModalEditCustomer = ref(false);
const handleOpenEditCustomer = () => {
  isModalEditCustomer.value = !isModalEditCustomer.value;
};
const closeModalEditCustomer = (value: boolean) => {
  isModalEditCustomer.value = value;
};
const handleMemberLevel = (memberLevel: string) => {
  switch (memberLevel) {
    case "MEMBER":
      return "text-white bg-primary px-2 py-[3px] rounded-lg";
    case "SILVER":
      return "text-gray-500 bg-gray-300 px-2 py-[3px] rounded-lg ";
    case "GOLD":
      return "text-yellow-300 bg-yellow-100 px-2 py-[3px] rounded-lg ";
    case "PLATINUM":
      return "text-green-500 bg-green-100 px-2 py-[3px] rounded-lg";
    default:
      return "";
  }
};
</script>
