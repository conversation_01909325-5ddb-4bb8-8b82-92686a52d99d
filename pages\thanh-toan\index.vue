<template>
  <LoadingSpinner v-if="isLoading || loading" />

  <div class="h-screen bg-gray-50 flex flex-col">
    <!-- Header -->
    <div class="bg-primary shadow-sm border-b border-primary/20 flex-shrink-0">
      <div class="px-3">
        <div class="flex items-center h-10">
          <div class="text-white font-semibold flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
              />
            </svg>
            <span class="text-xs">
              {{
                route.query.orgId ? route.query.orgId : route.query.partnerCode
              }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 min-h-0">
      <div v-if="!isPayment" class="h-full">
        <!-- Desktop Layout -->
        <div class="hidden md:flex justify-center items-center min-h-full p-4">
          <div class="flex gap-2 max-w-7xl mx-auto w-full">
            <!-- Payment Methods Area -->
            <div
              class="flex-1 bg-white rounded-lg shadow-md border border-gray-200 flex flex-col"
            >
              <div class="p-3 border-b border-gray-200 flex-shrink-0">
                <h2 class="text-base font-bold text-gray-900">
                  Phương thức thanh toán
                </h2>
              </div>
              <div class="flex-1 overflow-y-auto p-3">
                <PaymentNoLogin
                  :orderDetails="orderDetails"
                  :dataPaymentMethod="dataPaymentMethod"
                  @changeLoading="changeLoading"
                />
              </div>
            </div>

            <!-- Order Info Sidebar -->
            <div
              class="w-80 bg-white rounded-lg shadow-md border border-gray-200 flex-shrink-0"
            >
              <div class="p-2 py-3 border-b border-gray-200">
                <h3 class="text-base font-bold text-primary">
                  Thông tin đơn hàng
                </h3>
              </div>
              <div class="p-2">
                <InfoOrder :dataOrder="orderDetails?.data" />
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Layout -->
        <div class="md:hidden overflow-y-auto">
          <div class="flex flex-col items-center justify-center min-h-full p-2">
            <div class="w-full max-w-lg space-y-5 mx-auto">
              <!-- Order Info Header - Mobile -->
              <div
                class="bg-white rounded-xl shadow-md border border-gray-200 flex-shrink-0"
              >
                <div class="p-2 border-b border-gray-200">
                  <h3 class="text-lg font-bold text-primary">
                    Thông tin đơn hàng
                  </h3>
                </div>
                <div class="p-2">
                  <InfoOrder :dataOrder="orderDetails?.data" />
                </div>
              </div>

              <!-- Payment Methods - Mobile -->
              <div
                class="bg-white rounded-xl shadow-md border border-gray-200 flex flex-col"
              >
                <div class="p-5 border-b border-gray-200 flex-shrink-0">
                  <h2 class="text-lg font-bold text-gray-900">
                    Phương thức thanh toán
                  </h2>
                </div>
                <div class="p-5">
                  <PaymentNoLogin
                    :orderDetails="orderDetails"
                    :dataPaymentMethod="dataPaymentMethod"
                    @changeLoading="changeLoading"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Success State -->
      <div v-else class="flex items-center justify-center h-full">
        <div class="text-center max-w-md mx-auto p-6">
          <div
            class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <svg
              class="w-8 h-8 text-green-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <h2 class="text-xl font-bold text-green-700 mb-2">
            Cảm ơn bạn đã mua hàng!
          </h2>
          <p class="text-gray-600">
            Đơn hàng của bạn đã được ghi nhận. Chúng tôi sẽ liên hệ với bạn
            trong thời gian sớm nhất.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// definePageMeta({
//   layout: "dashboard",
//   name: "Thanh toán đơn hàng",
// });
// definePageMeta({
//   middleware: ["store"],
// });
useHead({
  title: "Thanh toán đơn hàng",
  meta: [
    {
      name: "description",
      content: "Thanh toán đơn hàng",
    },
  ],
});
const route = useRoute();
const orderId = route.query.orderId as string;
const orderDetails = ref<any>(null);
const isPayment = ref<Boolean>(false);
const ListItem = ref<any>();
const isLoading = ref<Boolean>(false);
const loading = ref<Boolean>(false);
const { getOrderByIdNoLogin, updateStatusApproved } = useOrder();
const {
  getPaymentMethods,
  dataPaymentMethod,
  getOrderById,
  paymentMethods,
  getPaymentMethodTypes,
  paymentsByOrders,
} = usePayment();
const fetchOrderData = async (
  partnerId: string,
  storeId: string,
  orderId: string
) => {
  try {
    orderDetails.value = await getOrderByIdNoLogin(partnerId, storeId, orderId);
    if (orderDetails.value.data.remainTotal === 0) {
      isPayment.value = true;
    }
  } catch (error) {
    throw error;
  }
};
const { hasPermission, setOrgId, setStore } = usePermission();
let intervalId: any;
const handleCheckPaymentStatus = async () => {
  const response = await paymentsByOrders([orderId]);
  const lastPaymentId = response[response.length - 1];
  if (lastPaymentId.statusCode === "0") {
    await updateStatusApproved(orderId);
    clearInterval(intervalId);
    navigateTo(
      `/thanh-toan/thanh-cong?id=${route.query.id}&orderId=${route.query.orderId}&orgId=${route.query.orgId}&storeId=${route.query.storeId}`
    );
  }
};
const paymentStore = usePaymentStore();
onMounted(async () => {
  isLoading.value = true;
  isPayment.value = false;

  const partnerCode =
    (route.query.partnerCode as string) || (route.query.orgId as string);
  const storeID =
    (route.query.storeID as string) || (route.query.storeId as string);

  if (partnerCode && storeID) {
    handleCheckPaymentStatus();

    await setOrgId(partnerCode);
    await setStore(storeID);

    // Keep cookie for SSR compatibility
    useCookie("storeId").value = storeID;
    await getPaymentMethodTypes();
    await fetchOrderData(partnerCode, storeID, orderId);
    await paymentStore.getDataPaymentOrder(route.query.orderId as string);
  } else {
    // Handle the case when neither partnerCode nor orgId is present
  }
  isLoading.value = false;
});
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/thanh-toan`) {
    isLoading.value = true;
    isPayment.value = false;

    const partnerCode =
      (route.query.partnerCode as string) || (route.query.orgId as string);
    const storeID =
      (route.query.storeID as string) || (route.query.storeId as string);

    if (partnerCode && storeID) {
      await handleCheckPaymentStatus();
      await setOrgId(partnerCode);
      await setStore(storeID);

      // Keep cookie for SSR compatibility
      useCookie("storeId").value = storeID;
      await getPaymentMethodTypes();
      await fetchOrderData(partnerCode, storeID, orderId);
      await paymentStore.getDataPaymentOrder(route.query.orderId as string);
    } else {
      // Handle the case when neither partnerCode nor orgId is present
    }
    isLoading.value = false;
  }
  next();
});
const changeLoading = () => {
  loading.value = !loading.value;
};
// Import utilities
import { getPaymentStatusClass } from "~/utils/statusHelpers";
</script>

<style scoped>
nav button {
  border-bottom-width: 4px;
}
nav button.active {
  border-color: #1d4ed8;
  color: #1d4ed8;
}
</style>
