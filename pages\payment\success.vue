<template>
  <div>
    <div
      v-if="ListItem?.activeOrderItemProfiles"
      class="w-full h-full min-h-[100vh] bg-white"
    >
      <div
        class="bg-[#eaecf0] p-4 m-5 rounded-lg flex gap-2 items-center justify-center"
      >
        <span class="text-primary"
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"
            />
          </svg>
        </span>
        <div class="text-primary font-bold text-base">
          Thanh toán thành công
        </div>
      </div>
      <div class="m-5">
        <p class="font-bold text-primary mb-2">Danh sách sản phẩm</p>
        <div v-for="item in ListItem?.activeOrderItemProfiles">
          <ItemProduct :product="item"></ItemProduct>
        </div>
      </div>
      <div class="m-5 flex gap-1">
        <div class="text-primary font-bold">Tổng thanh toán:</div>
        <div class="font-semibold text-red-500">
          {{ ` ${formatCurrency(ListItem?.order?.totalPrice?.amount)}` }}
        </div>
      </div>
    </div>
    <div v-else>
      <LoadingSpinner />
    </div>
  </div>
</template>
<script setup lang="ts">
useHead({
  title: "Thanh toán thành công",
  meta: [
    {
      name: "description",
      content: "Thanh toán thành công",
    },
  ],
});
// definePageMeta({
//   layout: "customPayment",
// });
const route = useRoute();
const { getOrderByIdNoLogin } = useOrder();
const ListItem = ref<any>();
const handleGetProduct = async (
  partnerId: string,
  storeId: string,
  orderId: string
) => {
  try {
    const response = await getOrderByIdNoLogin(partnerId, storeId, orderId);
    ListItem.value = response.data;
    // ListItem.value?.orderItemProfiles.forEach((item: any) => {});
  } catch (error) {
    throw error;
  }
};
onMounted(() => {
  handleGetProduct(
    route.query.partnerCode as string,
    route.query.storeID as string,
    route.query.orderId as string
  );
});
</script>
