<template>
  <div class="chat-room">
    <div class="message-list">
      <div
        v-for="(message, index) in messages"
        :key="index"
        class="message-item"
      >
        <p>
          <strong>{{ message.sender }}</strong
          >: {{ message.content }}
        </p>
        <span class="timestamp">{{
          new Date(message.timestamp).toLocaleString()
        }}</span>
      </div>
    </div>

    <div class="message-input">
      <input
        v-model="newMessage"
        type="text"
        placeholder="Nhập tin nhắn..."
        @keyup.enter="handleSendMessage"
      />
      <button @click="handleSendMessage">G<PERSON>i</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useNuxtApp } from "#app";

const { $matrixClient } = useNuxtApp();
const roomId = "!eXlFWCaevVatYyYWmS:matrix-synapse.longvan.vn"; // ID của phòng cần chat
const messages = ref([]);
const newMessage = ref("");
// Khởi tạo kết nối và tải tin nhắn
onMounted(async () => {
  const { token } = $matrixClient.loadCredentials();
  if (token) {
    try {
      await $matrixClient.connectMatrix(token);
      const client = $matrixClient.getClient();

      client.once("sync", async (state) => {
        console.log("Sync state:", state);
        if (state === "PREPARED") {
          await $matrixClient.loadMessages(roomId);
        }
      });
    } catch (error) {
      console.error("Matrix connection error:", error);
    }
  } else {
    console.error("No Matrix token found. Please log in.");
  }
});

// Dọn dẹp sự kiện khi component bị hủy
onUnmounted(() => {
  const client = $matrixClient.getClient();
  if (client) {
    client.removeAllListeners("Room.timeline");
  }
});

const loadMessages = async () => {
  messages.value = await $matrixClient.loadMessages(roomId);
};

const handleSendMessage = async () => {
  if (newMessage.value.trim() !== "") {
    await $matrixClient.sendMessage(roomId, newMessage.value);
    newMessage.value = "";
    await loadMessages();
  }
};
</script>

<style scoped>
.chat-room {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex-grow: 1;
  overflow-y: auto;
  margin-bottom: 10px;
}

.message-item {
  margin-bottom: 10px;
}

.message-input {
  display: flex;
}

input {
  flex-grow: 1;
  padding: 10px;
  margin-right: 10px;
}

button {
  padding: 10px;
}
</style>
