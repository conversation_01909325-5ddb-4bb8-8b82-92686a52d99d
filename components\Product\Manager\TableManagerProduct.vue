<template>
  <div class="bg-white h-full flex flex-col">
    <!-- Desktop Table Layout -->
    <div class="hidden md:flex flex-col h-full">
      <!-- Table Header - Fixed -->
      <div class="border-b border-gray-200 flex-shrink-0 bg-white">
        <div
          class="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-gray-700"
        >
          <div class="col-span-4">Sản phẩm</div>
          <div class="col-span-2"><PERSON>h mục</div>
          <div class="col-span-2 text-center">G<PERSON><PERSON> b<PERSON></div>
          <div class="col-span-2 text-center">Giá <PERSON>M</div>
          <div class="col-span-1">Đơn vị</div>
          <div class="col-span-1">Thao tác</div>
        </div>
      </div>

      <!-- Table Body - Scrollable Content -->
      <div class="flex-1 overflow-y-auto min-h-0">
        <div class="divide-y divide-gray-200">
          <div
            v-for="product in ListProduct"
            :key="product?.id + '-' + product?.updatedAt"
            class="grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-50 transition-colors"
          >
            <ItemManagerTableProduct
              :product="product"
              :dataUnit="dataUnit"
              :dataCategories="dataCategories"
              @updateProduct="handleUpdateProduct"
              @viewDetail="handleViewDetail"
            />
          </div>

          <!-- Loading Skeleton -->
          <div
            v-if="isLoading"
            v-for="n in 5"
            :key="'loading-' + n"
            class="grid grid-cols-12 gap-4 px-4 py-3 animate-pulse"
          >
            <div class="col-span-4">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="col-span-2">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="col-span-2">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="col-span-2">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="col-span-1">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
            <div class="col-span-1">
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-if="!isLoading && ListProduct.length === 0"
          class="flex flex-col items-center justify-center py-16 text-center"
        >
          <svg
            class="w-16 h-16 text-gray-300 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Chưa có sản phẩm nào
          </h3>
          <p class="text-gray-500">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
        </div>
      </div>

      <!-- Fixed Pagination at Bottom -->
      <div class="border-t border-gray-200 bg-white flex-shrink-0 shadow-lg">
        <div class="px-4 py-4">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Hiển thị
              <span class="font-medium text-primary">{{ startItem }}</span> đến
              <span class="font-medium text-primary">{{ endItem }}</span> trong
              tổng số
              <span class="font-medium text-primary">{{ totalItems }}</span> sản
              phẩm
            </div>
            <div class="flex items-center gap-3">
              <button
                @click="$emit('page-change', currentPage - 1)"
                :disabled="currentPage <= 1"
                class="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
              >
                ← Trước
              </button>
              <span
                class="px-4 py-2 text-sm font-semibold bg-gray-100 rounded-lg"
              >
                Trang {{ currentPage }} / {{ totalPages }}
              </span>
              <button
                @click="$emit('page-change', currentPage + 1)"
                :disabled="currentPage >= totalPages"
                class="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
              >
                Sau →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Card Layout -->
    <div class="md:hidden h-full flex flex-col">
      <!-- Mobile Content - Scrollable -->
      <div class="flex-1 overflow-y-auto min-h-0">
        <div class="p-4 space-y-3">
          <!-- Mobile Product Cards -->
          <div
            v-for="product in ListProduct"
            :key="product?.id"
            class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <ItemProductMobile
              :product="product"
              :dataUnit="dataUnit"
              @viewDetail="handleViewDetail"
            />
          </div>

          <!-- Mobile Loading Skeleton -->
          <div
            v-if="isLoading"
            v-for="n in 3"
            :key="'mobile-loading-' + n"
            class="bg-white rounded-lg border border-gray-200 p-4 animate-pulse"
          >
            <div class="flex gap-3">
              <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                <div class="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>

          <!-- Mobile Empty State -->
          <div
            v-if="!isLoading && ListProduct.length === 0"
            class="flex flex-col items-center justify-center py-16 text-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-16 w-16 text-gray-300 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
              />
            </svg>
            <p class="text-lg font-medium text-gray-900 mb-2">
              Không tìm thấy sản phẩm
            </p>
            <p class="text-sm text-gray-500">
              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
            </p>
          </div>
        </div>
      </div>

      <!-- Fixed Mobile Pagination at Bottom -->
      <div class="border-t border-gray-200 bg-white flex-shrink-0 shadow-lg">
        <div class="px-4 py-4">
          <div class="flex items-center justify-between">
            <!-- Mobile pagination info -->
            <div class="text-xs text-gray-600">
              {{ startItem }}-{{ endItem }} / {{ totalItems }}
            </div>

            <!-- Mobile pagination controls -->
            <div class="flex items-center gap-2">
              <button
                @click="$emit('page-change', currentPage - 1)"
                :disabled="currentPage <= 1"
                class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white transition-all duration-200 font-medium"
              >
                ← Trước
              </button>
              <span
                class="px-3 py-2 text-sm font-semibold bg-gray-100 rounded-lg"
              >
                {{ currentPage }}/{{ totalPages }}
              </span>
              <button
                @click="$emit('page-change', currentPage + 1)"
                :disabled="currentPage >= totalPages"
                class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed bg-white transition-all duration-200 font-medium"
              >
                Sau →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  ListProduct: any[];
  isLoading: boolean;
  dataUnit: any[];
  dataCategories: any[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

const props = defineProps<Props>();
const emit = defineEmits(["updateProduct", "viewDetail", "page-change"]);

const handleUpdateProduct = (productId: string) => {
  emit("updateProduct", productId);
};

const handleViewDetail = (productId: string) => {
  emit("viewDetail", productId);
};

// Pagination computed properties
const startItem = computed(() => {
  if (props.ListProduct.length === 0) return 0;
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  if (props.ListProduct.length === 0) return 0;
  return Math.min(
    (props.currentPage - 1) * props.itemsPerPage + props.ListProduct.length,
    props.totalItems
  );
});
</script>
