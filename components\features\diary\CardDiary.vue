<template>
  <div class=" bg-white w-full pb-1 mb-2">
    <div class="w-full bg-white rounded-lg ">
      <!--  -->
      <!-- <div :class="getStatusClass(diary?.status)">{{ diary?.statusDescription }}</div> -->
      <!--  -->
      <div class="px-2 py-1">
        <div class="flex justify-between">
          <div class="flex items-center">
            <h2 class="font-semibold text-base sm:text-sm">{{ diary?.id }}</h2>
            <div :class="getOrderStatusClass(diary?.status, 'semibold')">{{ diary?.status ==='DRAFT' ?'Nhật ký' : diary?.statusDescription }}</div>
          </div> 
          <div
          @click="handleNavigate"
            class="text-primary hover:text-primary-dark cursor-pointer"
          >
            Chi tiết
          </div>
        </div>
        <p class="text-gray-700 text-sm sm:text-base">
          <div>
            <!-- 1. Mô tả ngoại hình và thông tin chung -->
            <section class="text-sm">
              {{ diary.order?.note }}
            </section>
          </div>
        </p>
      </div>
      <!-- Khách hàng -->
       <div class="flex items-center justify-between px-2">
         <div class="flex items-center gap-1 text-sm">
           <div class="flex items-center gap-1">
             <span>
               <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                       <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
               </svg>
             </span>
               <span class="text-gray-500">{{ diary?.order?.ownerName }}</span>
           </div>
           <div class="flex items-center gap-1">
             <span>
               <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                 </svg>
             </span>
             <span class="text-gray-500">{{ diary?.order?.ownerPhone }}</span>
           </div>
         </div>
         <div>
        <div class=" flex gap-1 items-center">
          <span class="font-semibold text-sm">
            Tạo:
          </span>
          <span class="text-sm text-gray-500">
            {{ employeeSale }}
          </span>
      </div>
         </div>
       </div>
       <!-- thời gian +  -->
        <div class="flex items-center justify-between pb-1 px-2">
          <div class="flex items-center gap-1 text-sm ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
              <span class="text-gray-500">{{ formatTimestampV7(diary?.order?.createdStamp) }}</span>
            </div>
          <div>
            <span class="font-semibold text-sm ">
            Tư vấn:
          </span>
          <span class="text-sm text-gray-500">
            {{ employee }}
          </span>
          </div>
        </div>
       <!-- Nhật ký  -->
       <div class=" border-t border-b mx-2 border-gray-200 pt-1">
        <h3 class="font-semibold text-base sm:text-sm">Nhật ký</h3>
        <div v-for="connector in dataConnector">
         <TagDiary :connector="connector"></TagDiary>
        </div>
      </div>
      <!--  -->
      <div class=" mx-2 border-gray-200 py-1 border-b">
        <h3 class="font-semibold text-base sm:text-sm">Sản phẩm quan tâm</h3>
        <ul class="list-disc list-inside">
          <li
            class="text-gray-700 text-sm sm:text-base flex items-center gap-2"
            v-for="product in diary.orderItemProfiles"
            :key="product.id"
          >
          <ProductSimpleCard :product="product.orderLineItem"></ProductSimpleCard>
          </li>
        </ul>
      </div>
      <!-- danh sách hàng nút action -->
       <div class="mx-2">
         <ZaloZnsMobile :diary="diary" :isButtonSendMessage="true" :data="data"></ZaloZnsMobile>
       </div>
    </div>

  </div>

</template>

<script setup lang="ts">
// const { diary } = defineProps({
//   diary: {
//     type: Object,
//     default: {},
//   },
// });
const props = defineProps(["diary","data"])
const isLoading = ref(false)

// Use tab-isolated context instead of cookies
const { storeId, orgId } = useTabContext();
const { searchEmployes } = useOrder();
const employee  =ref()
const diaryStore = useDiariesStore();

const handleSearchEmployees = async (idEmployee: string) =>{
  if (!idEmployee) return;

  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );
  if (res) {
    employeeSale.value = res?.name;
  } else {
    employeeSale.value = "";
  }
}
const employeeSale  =ref()
const handleSearchEmployeeSale = async (idEmployee: string) =>{
  if (!idEmployee) return;

  const res = diaryStore.dataEmployee?.find(
    (item: any) => item.id === idEmployee
  );
  if (res) {
    employee.value = res?.name;
  } else {
    employee.value = "";
  }
}
//
const emits = defineEmits(["handleLoading"]);

const handleNavigate = async ()=>{
  let timeout;
  let isEmitted = false;

  // Đặt timeout để emit nếu quá 200ms
  timeout = setTimeout(() => {
    isEmitted = true;
    emits("handleLoading", true); // Emit trạng thái loading
  }, 200);

  try {
    if (props.diary.status === "DRAFT") {
      await navigateTo(`/diary?orderId=${props.diary?.id}&orgId=${orgId}&storeId=${storeId}`)
    } else {
      await  navigateTo(`/sale?orderId=${props.diary?.id}&orgId=${orgId}&storeId=${storeId}`)
    }
  } catch (error) {
    console.error("Navigation error:", error);
  } finally {
    // Xóa timeout nếu navigate hoàn thành trước 200ms
    clearTimeout(timeout);

    // Nếu đã emit, thông báo kết thúc loading
    if (isEmitted) {
      emits("handleLoading", false);
    }
  }
}
////// lấy ra thông tin tag 
const { getConnectorByResource, getTags, createConnector } = usePortal();
const dataConnector = ref()
const handleGetConnector =async ()=>{
  try {
    const response = await getConnectorByResource(props.diary.id,"ORDER","TAG")
    dataConnector.value = response
  } catch (error) {
    throw error
  }
}

onMounted(async ()=>{
  await Promise.all([handleGetConnector(), handleSearchEmployees(props.diary?.order?.salePartyId),handleSearchEmployeeSale(props.diary?.order?.createdBy)])
})
// Import utilities
import { getOrderStatusClass } from '~/utils/statusHelpers';

</script>
