<template>
  <div class="md:col-span-3 col-span-6 bg-white rounded-lg p-2 border">
    <h2 class="text-lg font-semibold text-primary mb-2">
      Thông tin khách hàng
    </h2>
    <div class="space-y-2 text-sm">
      <div>
        <span class="text-gray-600 font-medium">Tên khách hàng: </span>
        <span class="text-gray-900">{{ customer?.name }}</span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">SĐT: </span>
        <span class="text-gray-900">{{ customer?.phone }}</span>
      </div>
      <!-- <div>
        <span class="text-gray-600 font-medium">Ngày sinh: </span>
        <span class="text-gray-900">{{ customer?.birthDate || "" }}</span>
      </div>
      <div>
        <span class="text-gray-600 font-medium">Hạng: </span>
        <span class="font-normal bg-gray-500 p-1 rounded text-xs text-white">
          {{ customer?.memberLevel || "chưa có hạng" }}
        </span>
      </div> -->

      <div v-if="customer?.address">
        <span class="text-gray-600 font-medium">Địa chỉ:</span>
        <span class="text-gray-900">{{ customer?.address }} </span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const customer = computed(() => orderStore.customerInOrder);
</script>
