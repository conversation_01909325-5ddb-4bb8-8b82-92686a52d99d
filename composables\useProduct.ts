import type {
  Product,
  ProductFilterOptions,
} from "@longvansoftware/storefront-js-client/dist/src/types/product";
export default function useProduct() {
  const $sdk = useNuxtApp().$sdk;
  const products = ref<Product[]>([]);
  const productImages = ref<any>({});
  const productInventory = ref<any>({});
  const searchLoading = ref(false);

  const options = ref<ProductFilterOptions>({
    keyword: "",
    maxResult: 10,
    currentPage: 1,
  });

  // Use tab-isolated context instead of cookies
  const { orgId } = useTabContext();

  const isModalOpen = ref(false);
  const openModal = () => {
    isModalOpen.value = true;
  };

  const closeModal = () => {
    isModalOpen.value = false;
  };
  //Lấy danh sách id sản phẩm từ danh sách sản phẩm
  const getProductIds = (products: Product[]) => {
    return {
      productIds: products.map((product) => product.id),

      inventory: products.map((product) => {
        return {
          productId: product.id,
          variantId: "",
          sku: product.sku,
        };
      }),
    };
  };
  const getProductById = async (productId: string) => {
    const product = await $sdk.product.getProductById(productId);
    return product;
  };

  const handleProductInventory = async (data: any) => {
    const inventory = await getInventory(data);
    const inventoryMap = await convertInventory(inventory);
    productInventory.value = inventoryMap;
  };
  //Lấy danh sách sản phẩm từ danh sách id sản phẩm
  const getProducts = async ({
    keyword = "",
    maxResult = 10,
    currentPage = 1,
  } = {}) => {
    const options = { keyword, maxResult, currentPage };

    try {
      const response = (await $sdk.product.getSimpleProducts(options)) as any;
      products.value = response.data;
      return response.data;
    } catch (error) {
      console.error("Error getting products:", error);
      searchLoading.value = false;
      throw error;
    }
  };

  const convertInventory = (inventory: any) => {
    return inventory?.reduce((acc: any, item: any) => {
      acc[item.productId] = item;
      return acc;
    }, {});
  };
  const getInventory = async (payload: any) => {
    try {
      // const response = await fetch(
      //   "https://facility-api-v2.dev.longvan.vn/facility-api/public-facility/1.0.0/product-inventory/1699410559510051",
      //   {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/json",
      //       Partnerid: "FOX",
      //     },
      //     body: JSON.stringify(payload),
      //   }
      // );
      // if (!response.ok) {
      //   throw new Error("Failed to get inventory");
      // }
      // return response.json();
    } catch (error) {
      console.error("Error getting inventory:", error);
    }
  };
  const getImageProduct = async (productId: string) => {
    try {
      const response = await $sdk.product.getProductImage(productId);
      return response;
    } catch (error) {
      console.error("Error getting image products:", error);
    }
  };
  // categorry
  const getCategory = async (typeBuild: string, level: number) => {
    try {
      const response = await $sdk.product.getCategory(typeBuild, level);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getProductCategory = async (category: string) => {
    try {
      const response = await $sdk.product.getProduct(category);
      return response;
    } catch (error) {
      throw error;
    }
  };
  const getUnitInvoice = async () => {
    try {
      const response = await $sdk.product.getUnits();
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    options,
    products,
    productImages,
    productInventory,
    searchLoading,
    isModalOpen,
    getProducts,
    openModal,
    closeModal,
    getProductById,
    getImageProduct,
    getCategory,
    getProductCategory,
    getUnitInvoice,
  };
}
