<template>
  <div
    v-if="!isDetailOrder"
    class="col-span-3 md:col-span-2 bg-white border rounded-lg p-2 text-sm overflow-y-auto max-h-[250px]"
  >
    <div class="flex justify-between items-center">
      <h2 class="text-lg font-semibold text-primary">Danh sách topic</h2>
      <div @click="toogleTopicPopup" class="cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 4.5v15m7.5-7.5h-15"
          />
        </svg>
      </div>
    </div>
    <div
      v-if="listTopic?.length === 0"
      class="flex items-center justify-center mx-6 py-1 mt-4"
    >
      Ch<PERSON>a có Topic nào được trao đổi
    </div>
    <div v-else>
      <div v-for="topic in listTopic" :key="topic?.id">
        <div
          @click="handleNavigate(topic)"
          class="border-b pb-2 class flex items-center justify-between gap-2 p-1 py-2"
        >
          <div class="font-semibold text-sm">{{ topic?.name }}</div>
          <span :class="handleAction(topic?.status)">{{
            topic?.status === "OPENED" ? "Đang mở" : "Đã đóng"
          }}</span>
        </div>
      </div>
    </div>
  </div>
  <!-- trang detail order -->
  <div v-else>
    <div class="bg-white px-2 pb-2 rounded-lg">
      <div class="flex items-center justify-between">
        <span class="text-md text-primary font-bold">Topic trao đổi</span>
        <span class="cursor-pointer">
          <svg
            v-if="isOpenTopic"
            @click="toogleOpenTopic"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m4.5 15.75 7.5-7.5 7.5 7.5"
            />
          </svg>
          <svg
            v-else
            @click="toogleOpenTopic"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            />
          </svg>
        </span>
      </div>
      <div v-if="isOpenTopic">
        <div
          v-if="listTopic?.length === 0"
          class="flex items-center justify-center mx-6 py-1 mt-4"
        >
          Chưa có Topic nào được trao đổi
        </div>
        <div v-else class="max-h-[200px] overflow-y-auto">
          <div v-for="topic in listTopic" :key="topic?.id">
            <div
              @click="handleNavigate(topic)"
              class="border-b pb-2 class flex items-center justify-between gap-2 p-1 py-2 cursor-pointer"
            >
              <div class="font-semibold text-sm">{{ topic?.name }}</div>
              <span :class="handleAction(topic?.status)">{{
                topic?.status === "OPENED" ? "Đang mở" : "Đã đóng"
              }}</span>
            </div>
          </div>
        </div>
        <button
          class="flex items-center cursor-pointer border border-primary px-[6px] py-[2px] rounded text-primary mt-3"
          @click="toogleTopicPopup"
        >
          <span class="text-xs">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
          </span>
          <span>Tạo mới</span>
        </button>
      </div>
    </div>
  </div>
  <CreateTopicPopUp
    v-if="isOpenTopicPopup"
    @cancel="toogleTopicPopup"
    @confirm="handleCreateTopic"
  >
  </CreateTopicPopUp>
  <TopicAlert
    v-if="isPopupNavigate"
    @navigate="continueExchange"
    @closeTopic="handleCloseTopic"
    @cancel="tooglePopupNavigate"
  >
  </TopicAlert>
</template>
<script setup lang="ts">
import type { Auth } from "~/types/Auth";

const auth = useCookie("auth").value as unknown as Auth;

const props = defineProps(["listTopic", "isDetailOrder", "customer"]);
const handleAction = (value: string) => {
  switch (value) {
    case "OPENED":
      return "text-green-500 text-sm ";
    case "CLOSED":
      return "text-red-500 text-sm";
    default:
      return "";
  }
};
const route = useRoute();
const isPopupNavigate = ref(false);
const handleNavigate = (data: any) => {
  topic.value = data;

  if (data?.status === "OPENED") {
    isPopupNavigate.value = true;
    // navigateTo(
    //   `/chat?orgId=${route.query.orgId}&storeId=${route.query.storeId}&roomId=${topic?.roomId}&threadId=${topic?.threadId}&topicId=${topic?.id}&customerId=${route?.query?.customerId}`
    // );
  } else {
    useNuxtApp().$toast.warning("Topic đã đóng vui lòng chọn topic khác");
  }
};

const isOpenTopic = ref(false);
const toogleOpenTopic = () => {
  isOpenTopic.value = !isOpenTopic.value;
};
//
const isAlert = ref(false);
const isOpenTopicPopup = ref(false);
const toogleTopicPopup = () => {
  if (props.listTopic.length > 0) {
    const res = props.listTopic.find((topic: any) => topic.status === "OPENED");
    if (res) {
      isAlert.value = true;
      return;
    }
  }
  isOpenTopicPopup.value = !isOpenTopicPopup.value;
};
//
const { createTopic } = useCustomer();
const { closeTopic } = useComhub();
const customerStore = useCustomerStore();
const handleCreateTopic = async (message: string, appId: string) => {
  try {
    const res = await createTopic(appId, props.customer?.id, message);
  } catch (error) {
    console.error("Error creating topic:", error);
  } finally {
    await customerStore.handleGetTopic(props.customer?.id);
  }
  isOpenTopicPopup.value = !isOpenTopicPopup.value;
};
//
const topic = ref();
const handleCloseTopic = async () => {
  await closeTopic(topic.value?.id, auth?.user?.id);
  await customerStore.handleGetTopic(props.customer?.id);
};
const cancel = async () => {
  isAlert.value = false;
};
const confirm = async () => {
  const res = props.listTopic.find((topic: any) => topic.status === "OPENED");

  await handleCloseTopic();
  toogleTopicPopup();
  isAlert.value = false;
};
/////
const tooglePopupNavigate = () => {
  isPopupNavigate.value = !isPopupNavigate.value;
};
const continueExchange = async () => {
  navigateTo(
    `/chat?orgId=${route.query.orgId}&storeId=${route.query.storeId}&roomId=${topic.value?.roomId}&threadId=${topic.value?.threadId}&topicId=${topic.value?.id}&customerId=${props.customer?.id}`
  );
};
</script>
