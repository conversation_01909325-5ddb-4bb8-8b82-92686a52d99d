<template>
  <!-- <div :class="['sidebar', showChatSale ? 'sidebar-chat-sale' : '']">
    <button v-if="!showChatSale" @click="$emit('closeSidebar')" class="close-btn left-0">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
        class="size-5"
      >
        <path
          d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
        />
      </svg>
    </button>

    <div v-if="!showChatSale">
      <div class="profile-card flex flex-col items-center p-4 rounded-lg mb-4 text-center">
        <img :src="userAvatar" alt="User Avatar" class="w-28 h-28 rounded-full object-cover" />
        <div class="ml-3">
          <h3 class="text-sm font-semibold">{{ userName }}</h3>
          <p class="text-xs text-gray-500">{{ userEmail }}</p>
        </div>
      </div>

      <div class="menu-options">
        <h4 class="text-xs text-gray-400 px-4">Tính năng</h4>
        <button @click="handleCreateOrder" class="menu-item">
          <span>Tạo đơn hàng</span>
        </button>
    
      </div>

      <Modal :isOpen="showModal" @update:isOpen="showModal = $event" @confirm="handleConfirm">
        <h2 class="text-lg font-semibold text-gray-800">Bạn có chắc chắn muốn đóng chủ đề này?</h2>
        <p class="text-gray-600 mt-2">
          Khi bạn đóng chủ đề, bạn sẽ không thể nhận được thông báo từ chủ đề này nữa.
        </p>
      </Modal>
    </div>
  </div> -->
  <div>
    <ChatSale />
  </div>
</template>

<script setup>
import { ref, defineEmits } from "vue";

const emit = defineEmits(["confirmCloseTopic", "closeSidebar", "openChatSale"]);
const showModal = ref(false);
const showChatSale = ref(false);
const userAvatar = ref("https://via.placeholder.com/150");
const userName = ref("ZALO");

const handleCreateOrder = () => {
  emit("openChatSale"); // Emit event to open ChatSale
};

const handleConfirm = () => {
  emit("confirmCloseTopic");
  showModal.value = false;
};
</script>

<style scoped>
.sidebar {
  background-color: #ffffff;
  padding: 16px;
  border-right: 1px solid #e5e7eb;
}
.sidebar-chat-sale {
  padding: 0px;
  border: none;
  background-color: #ffffff;
}
.profile-card img {
  object-fit: cover;
}
.menu-options {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  border-radius: 4px;
  background-color: #ffffff;
  transition: background-color 0.2s;
  cursor: pointer;
}
.menu-item:hover {
  background-color: #f1f5f9;
}
.menu-options h4 {
  font-weight: 600;
  color: #888;
  margin-bottom: 4px;
}
</style>
