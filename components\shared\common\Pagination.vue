<template>
  <div
    class="flex flex-col sm:flex-row items-center justify-between gap-4 py-4 px-4 bg-white border-t border-gray-200"
  >
    <!-- Thông tin tổng quan -->
    <div
      class="flex items-center gap-2 text-sm text-gray-600 order-2 sm:order-1"
    >
      <span v-if="totalItems">
        Hi<PERSON><PERSON> thị {{ startItem }} - {{ endItem }} trong tổng số
        {{ totalItems }} mục
      </span>
      <span v-else> Không có dữ liệu </span>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> khiển phân trang -->
    <div class="flex items-center gap-2 order-1 sm:order-2">
      <!-- Nút Previous -->
      <button
        @click="handlePrePage"
        :disabled="currentPage <= 1"
        class="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        :class="
          currentPage <= 1
            ? 'bg-gray-100 text-gray-400'
            : 'bg-white text-gray-700 hover:text-gray-900'
        "
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="hidden sm:inline">Trước</span>
      </button>

      <!-- Hiển thị số trang -->
      <div class="flex items-center gap-1">
        <!-- Trang đầu -->
        <button
          v-if="showFirstPage"
          @click="handlePageClick(1)"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          :class="
            currentPage === 1
              ? 'bg-primary text-white border-primary'
              : 'bg-white text-gray-700'
          "
        >
          1
        </button>

        <!-- Dấu ... đầu -->
        <span v-if="showStartEllipsis" class="px-2 text-gray-400">...</span>

        <!-- Các trang xung quanh trang hiện tại -->
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="handlePageClick(page)"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          :class="
            currentPage === page
              ? 'bg-primary text-white border-primary'
              : 'bg-white text-gray-700'
          "
        >
          {{ page }}
        </button>

        <!-- Dấu ... cuối -->
        <span v-if="showEndEllipsis" class="px-2 text-gray-400">...</span>

        <!-- Trang cuối -->
        <button
          v-if="showLastPage"
          @click="handlePageClick(totalPages)"
          class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          :class="
            currentPage === totalPages
              ? 'bg-primary text-white border-primary'
              : 'bg-white text-gray-700'
          "
        >
          {{ totalPages }}
        </button>
      </div>

      <!-- Nút Next -->
      <button
        @click="handleNextPage"
        :disabled="currentPage >= totalPages"
        class="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        :class="
          currentPage >= totalPages
            ? 'bg-gray-100 text-gray-400'
            : 'bg-white text-gray-700 hover:text-gray-900'
        "
      >
        <span class="hidden sm:inline">Sau</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  itemsPerPage?: number;
  maxVisiblePages?: number;
}

const props = withDefaults(defineProps<Props>(), {
  totalItems: 0,
  itemsPerPage: 10,
  maxVisiblePages: 5,
});

const emits = defineEmits<{
  nextPage: [];
  prePage: [];
  pageChange: [page: number];
}>();

// Computed properties
const startItem = computed(() => {
  if (!props.totalItems || props.totalItems === 0) return 0;
  return (props.currentPage - 1) * props.itemsPerPage + 1;
});

const endItem = computed(() => {
  if (!props.totalItems || props.totalItems === 0) return 0;
  const end = props.currentPage * props.itemsPerPage;
  return Math.min(end, props.totalItems);
});

// Logic hiển thị các trang
const visiblePages = computed(() => {
  const pages: number[] = [];
  const maxVisible = props.maxVisiblePages;
  const current = props.currentPage;
  const total = props.totalPages;

  if (total <= maxVisible) {
    // Nếu tổng số trang ít hơn maxVisible, hiển thị tất cả
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Tính toán range xung quanh trang hiện tại
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(1, current - half);
    let end = Math.min(total, current + half);

    // Điều chỉnh nếu cần
    if (end - start + 1 < maxVisible) {
      if (start === 1) {
        end = Math.min(total, start + maxVisible - 1);
      } else {
        start = Math.max(1, end - maxVisible + 1);
      }
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
  }

  return pages;
});

const showFirstPage = computed(() => {
  return (
    props.totalPages > props.maxVisiblePages && !visiblePages.value.includes(1)
  );
});

const showLastPage = computed(() => {
  return (
    props.totalPages > props.maxVisiblePages &&
    !visiblePages.value.includes(props.totalPages)
  );
});

const showStartEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2;
});

const showEndEllipsis = computed(() => {
  return (
    showLastPage.value &&
    visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1
  );
});

// Event handlers
const handleNextPage = () => {
  if (props.currentPage < props.totalPages) {
    emits("nextPage");
    emits("pageChange", props.currentPage + 1);
  }
};

const handlePrePage = () => {
  if (props.currentPage > 1) {
    emits("prePage");
    emits("pageChange", props.currentPage - 1);
  }
};

const handlePageClick = (page: number) => {
  if (page !== props.currentPage && page >= 1 && page <= props.totalPages) {
    emits("pageChange", page);
  }
};
</script>
