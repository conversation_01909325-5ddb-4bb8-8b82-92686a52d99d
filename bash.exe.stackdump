Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA19770000 ntdll.dll
7FFA18850000 KERNEL32.DLL
7FFA17030000 KERNELBASE.dll
7FFA19270000 USER32.dll
7FFA16A40000 win32u.dll
7FFA194A0000 GDI32.dll
7FFA16A70000 gdi32full.dll
7FFA169A0000 msvcp_win.dll
7FFA16880000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA17F30000 advapi32.dll
7FFA191C0000 msvcrt.dll
7FFA17630000 sechost.dll
7FFA16B90000 bcrypt.dll
7FFA17D40000 RPCRT4.dll
7FFA16090000 CRYPTBASE.DLL
7FFA16E40000 bcryptPrimitives.dll
7FFA194D0000 IMM32.DLL
