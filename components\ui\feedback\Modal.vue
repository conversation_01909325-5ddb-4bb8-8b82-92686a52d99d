<template>
    <div v-if="isOpen" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center z-50" @click="onOverlayClick">
      <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto" @click.stop>
        <!-- Modal Content -->
        <slot />
        
        <!-- Modal Footer -->
        <div class="flex justify-end space-x-4 mt-4">
          <button @click="confirmClose" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">
            Xác nhận
          </button>
          <button @click="closeModal" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition">
            Hủy
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, defineEmits } from 'vue'
  
  const props = defineProps({
    isOpen: Boolean
  })
  const emit = defineEmits(['update:isOpen', 'confirm'])
  
  const closeModal = () => {
    emit('update:isOpen', false)
  }
  
  const confirmClose = () => {
    emit('confirm')
    closeModal()
  }
  
  const onOverlayClick = () => {
    closeModal()
  }
  </script>
  