<template>
  <header
    class="bg-[#1565C0] flex justify-between items-center h-14 fixed top-0 z-30 shadow-lg transition-all duration-300 ease-in-out"
    :class="[
      isSlimSidebar
        ? 'w-full md:w-[100%] transition-all duration-300 ease-in-out'
        : 'w-full md:w-[100%] transition-all duration-300 ease-in-out',
    ]"
  >
    <!-- Left Section -->
    <div class="flex items-center space-x-4">
      <!-- Mobile Menu Button -->
      <button
        @click="openModal"
        class="md:hidden p-2 rounded-lg hover:bg-white/20 transition-colors duration-200 group"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 256 256"
          class="text-white group-hover:text-white/80 transition-colors duration-200"
        >
          <path
            d="M224,160H32a16,16,0,0,0,0,32H224a16,16,0,0,0,0-32Zm0-64H32a16,16,0,0,0,0,32H224a16,16,0,0,0,0-32Zm0-64H32a16,16,0,0,0,0,32H224a16,16,0,0,0,0-32Z"
          ></path>
        </svg>
      </button>

      <!-- Back Button -->

      <!-- Store Info -->
      <div
        v-if="dataDetailStore"
        class="hidden md:flex items-center space-x-3 bg-white/20 backdrop-blur-sm rounded-xl px-4 py-1 shadow-sm border border-white/30"
      >
        <div
          class="w-8 h-8 bg-white/30 rounded-lg flex items-center justify-center shadow-sm"
        >
          <svg
            class="w-4 h-4 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
            <path
              fill-rule="evenodd"
              d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </div>
        <div class="min-w-0">
          <p class="text-sm font-medium text-white truncate">
            {{ dataDetailStore.name }}
          </p>
          <p class="text-xs text-white/80">Cửa hàng</p>
        </div>
      </div>
      <div class="flex-1 flex justify-center">
        <h1 class="text-lg font-bold text-white">{{ namePage }}</h1>
      </div>
    </div>

    <!-- Right Section -->
    <div class="flex items-center space-x-3">
      <button
        v-if="levelPage >= 2"
        @click="goBack"
        class="hidden md:flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors duration-200 group"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-5 h-5 text-white group-hover:text-white/80 transition-colors duration-200"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M6.75 15.75 3 12m0 0 3.75-3.75M3 12h18"
          />
        </svg>
        <span
          class="text-sm font-medium text-white group-hover:text-white/80 transition-colors duration-200"
        >
          Quay lại
        </span>
      </button>
      <!-- Create Order Button -->

      <button
        v-if="isButton"
        @click="createOrder"
        class="hidden sm:flex items-center space-x-2 px-4 py-2 bg-white text-primary text-sm font-medium rounded-lg hover:bg-white/90 transition-colors duration-200 shadow-sm"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-4 h-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        <span>Nhật ký mới</span>
      </button>

      <!-- Mobile Create Order Button -->
      <button
        v-if="isButton"
        @click="createOrder"
        class="sm:hidden p-2 bg-white text-primary rounded-lg hover:bg-white/90 transition-colors duration-200 shadow-sm"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-5 h-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
      </button>

      <!-- User Menu -->
      <div class="hidden md:flex relative">
        <button
          v-if="user"
          @click="toggleShowSubMenu"
          class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/20 transition-colors duration-200 group"
        >
          <div
            class="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              fill="currentColor"
              viewBox="0 0 256 256"
              class="text-white"
            >
              <path
                d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
              ></path>
            </svg>
          </div>
          <div class="hidden lg:block text-left">
            <p class="text-sm font-medium text-white">{{ userName }}</p>
            <p class="text-xs text-white/80">{{ userRole }}</p>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4 text-white/80 group-hover:text-white transition-colors duration-200"
            :class="{ 'rotate-180': subMenu }"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M19.5 8.25l-7.5 7.5-7.5-7.5"
            />
          </svg>
        </button>

        <!-- User Dropdown -->
        <Transition name="dropdown">
          <div
            v-show="subMenu"
            class="absolute right-0 top-full mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200/50 py-2 z-50"
          >
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
              <p class="text-xs text-gray-500">{{ user?.email }}</p>
            </div>
            <button
              @click="logout()"
              class="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                viewBox="0 0 256 256"
                class="text-gray-400"
              >
                <path
                  d="M141.66,133.66l-40,40a8,8,0,0,1-11.32-11.32L116.69,136H24a8,8,0,0,1,0-16h92.69L90.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,141.66,133.66ZM192,32H136a8,8,0,0,0,0,16h56V208H136a8,8,0,0,0,0,16h56a16,16,0,0,0,16-16V48A16,16,0,0,0,192,32Z"
                ></path>
              </svg>
              <span>Đăng xuất</span>
            </button>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Mobile Menu Modal -->
    <ModalMenuMobile :isOpen="isModalOpen" @close="closeModal" />
  </header>
</template>

<script setup>
// Nuxt Composables
const route = useRoute();
const router = useRouter();

// ✅ Cookie định nghĩa trong setup
const warehouseCookie = useCookie("warehouse");
const warehouseIdCookie = useCookie("warehouseId");

// Tab context is available if needed
// const { orgId, storeId } = useTabContext();

// Props
const props = defineProps({
  isButton: Boolean,
  storeId: String,
  isSlimSidebar: Boolean,
});

// State
const levelPage = ref(0);
const subMenu = ref(false);
const isButton = ref(false);
const isModalOpen = ref(false);
const dataDetailStore = ref(null);

// Stores
const authStore = useAuthStore();
const { logout } = useAuth();
const { createOrder } = useOrderStore();
const { getDetailStore } = useStore();

// Computed
const namePage = computed(() => route.meta?.name || "");
const user = computed(() => authStore?.user || null);
const userName = computed(() => user.value?.name || "");
const userRole = computed(() => {
  const roles = user.value?.roles || [];
  if (roles.includes("ORG_ADMIN")) return "Quản trị viên";
  if (roles.includes("SALE_ADMIN")) return "Quản lý bán hàng";
  if (roles.includes("SALE")) return "Nhân viên bán hàng";
  return "Nhân viên";
});

// Functions
const openModal = () => {
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
};

const toggleShowSubMenu = () => {
  subMenu.value = !subMenu.value;
};

const goBack = () => {
  router.back();
};

const handleGetStore = async () => {
  try {
    const response = await getDetailStore();

    // ✅ Gán giá trị cookie từ setup context
    warehouseCookie.value = response?.warehouses;
    warehouseIdCookie.value = response?.warehouseIdDefault;

    dataDetailStore.value = response;
  } catch (error) {
    console.error("Error fetching store:", error);
  }
};

// Watchers
watch(
  () => route.path,
  (newValue) => {
    isButton.value = newValue.startsWith("/diary");
    const filtered = newValue.split("/").filter((item) => item);
    levelPage.value = filtered.length;
  },
  { immediate: true }
);

watch(
  () => route.query.storeId,
  async () => {
    await handleGetStore();
  },
  { immediate: true }
);

onMounted(async () => {
  await handleGetStore();
});
</script>

<style scoped>
/* Dropdown animation */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(255, 255, 255, 0.95);
  }
}

/* Smooth transitions for all interactive elements */
button,
.transition-colors {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
button:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .truncate {
    max-width: 150px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/95 {
    background-color: rgba(255, 255, 255, 0.98);
  }

  .border-gray-200\/50 {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .dropdown-enter-active,
  .dropdown-leave-active {
    transition: none;
  }
}
</style>
