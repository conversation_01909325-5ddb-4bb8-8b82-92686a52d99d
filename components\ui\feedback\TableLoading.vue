<template>
  <div class="animate-pulse">
    <!-- Desktop Table Loading -->
    <div class="hidden md:block">
      <div class="divide-y divide-gray-200 bg-white">
        <div
          v-for="index in itemCount"
          :key="index"
          class="px-4 py-3"
          :class="tableRowClass"
        >
          <div :class="gridClass">
            <!-- Dynamic skeleton columns based on columns prop -->
            <div
              v-for="(column, colIndex) in columns"
              :key="colIndex"
              :class="column.class || 'col-span-1'"
            >
              <div class="space-y-2">
                <!-- Main content skeleton -->
                <div
                  class="h-4 bg-gray-200 rounded"
                  :class="column.width || 'w-3/4'"
                ></div>
                <!-- Secondary content skeleton (if specified) -->
                <div
                  v-if="column.hasSecondary"
                  class="h-3 bg-gray-200 rounded"
                  :class="column.secondaryWidth || 'w-1/2'"
                ></div>
                <!-- Tertiary content skeleton (if specified) -->
                <div
                  v-if="column.hasTertiary"
                  class="h-3 bg-gray-200 rounded"
                  :class="column.tertiaryWidth || 'w-2/3'"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Cards Loading -->
    <div class="md:hidden space-y-3">
      <div
        v-for="index in itemCount"
        :key="index"
        class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm"
      >
        <div class="space-y-3">
          <!-- Card header -->
          <div class="flex justify-between items-start">
            <div class="space-y-2 flex-1">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div class="h-6 w-16 bg-gray-200 rounded-full ml-4"></div>
          </div>

          <!-- Card content -->
          <div class="space-y-2">
            <div class="h-3 bg-gray-200 rounded w-full"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>

          <!-- Card footer -->
          <div class="flex justify-between items-center pt-2">
            <div class="h-3 bg-gray-200 rounded w-1/3"></div>
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  class?: string; // Grid column class (e.g., 'col-span-2')
  width?: string; // Main content width (e.g., 'w-3/4')
  hasSecondary?: boolean; // Has secondary line
  secondaryWidth?: string; // Secondary content width
  hasTertiary?: boolean; // Has tertiary line
  tertiaryWidth?: string; // Tertiary content width
}

interface Props {
  itemCount?: number; // Number of skeleton items to show
  columns?: Column[]; // Column configuration for desktop table
  gridClass?: string; // Grid class for table layout
  tableRowClass?: string; // Additional classes for table rows
  showMobile?: boolean; // Show mobile skeleton
  showDesktop?: boolean; // Show desktop skeleton
}

const props = withDefaults(defineProps<Props>(), {
  itemCount: 5,
  columns: () => [
    {
      class: "col-span-2",
      width: "w-3/4",
      hasSecondary: true,
      secondaryWidth: "w-1/2",
    },
    {
      class: "col-span-2",
      width: "w-2/3",
      hasSecondary: true,
      secondaryWidth: "w-1/3",
    },
    { class: "col-span-2", width: "w-1/2" },
    { class: "col-span-2", width: "w-3/5" },
    { class: "col-span-2", width: "w-4/5" },
    { class: "col-span-1", width: "w-8", hasSecondary: false },
  ],
  gridClass: "grid grid-cols-11 gap-2",
  tableRowClass: "hover:bg-gray-50 transition-colors",
  showMobile: true,
  showDesktop: true,
});

// Preset configurations for different table types
export const TRANSACTION_LOADING_CONFIG = {
  itemCount: 5,
  columns: [
    {
      class: "col-span-2",
      width: "w-3/4",
      hasSecondary: true,
      secondaryWidth: "w-1/2",
      hasTertiary: true,
      tertiaryWidth: "w-2/3",
    },
    {
      class: "col-span-2",
      width: "w-2/3",
      hasSecondary: true,
      secondaryWidth: "w-1/3",
      hasTertiary: true,
      tertiaryWidth: "w-1/2",
    },
    { class: "col-span-2", width: "w-1/2" },
    { class: "col-span-2", width: "w-3/5" },
    { class: "col-span-2", width: "w-4/5" },
    { class: "col-span-1", width: "w-8" },
  ],
  gridClass: "grid grid-cols-11 gap-2",
  tableRowClass: "hover:bg-gray-50 transition-colors",
};

export const DIARIES_LOADING_CONFIG = {
  itemCount: 5,
  columns: [
    { class: "w-1/12 text-center", width: "w-3/4" },
    {
      class: "w-2/12",
      width: "w-2/3",
      hasSecondary: true,
      secondaryWidth: "w-1/2",
    },
    {
      class: "w-2/12",
      width: "w-3/4",
      hasSecondary: true,
      secondaryWidth: "w-1/3",
    },
    { class: "w-2/12", width: "w-1/2" },
    {
      class: "w-5/12 max-w-0",
      width: "w-4/5",
      hasSecondary: true,
      secondaryWidth: "w-2/3",
    },
    { class: "w-auto text-center", width: "w-6" },
  ],
  gridClass: "table-auto w-full text-sm",
  tableRowClass:
    "hover:bg-blue-50 even:bg-gray-50 odd:bg-white border-b border-gray-100",
};
</script>

<style scoped>
/* Enhanced pulse animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer effect for skeleton elements */
.bg-gray-200 {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation for better visual effect */
.space-y-3 > *:nth-child(1) {
  animation-delay: 0ms;
}
.space-y-3 > *:nth-child(2) {
  animation-delay: 100ms;
}
.space-y-3 > *:nth-child(3) {
  animation-delay: 200ms;
}
.space-y-3 > *:nth-child(4) {
  animation-delay: 300ms;
}
.space-y-3 > *:nth-child(5) {
  animation-delay: 400ms;
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>
