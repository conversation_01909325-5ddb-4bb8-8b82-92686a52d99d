<template>
  <aside
    class="hidden md:flex flex-col h-full bg-white shadow-xl overflow-x-hidden"
    :class="[
      'sidebar',
      isSlimSidebar ? 'w-16' : 'w-60',
      'transition-all duration-300 ease-in-out',
    ]"
  >
    <!-- Header Section -->
    <div class="flex-shrink-0 bg-[#1565C0]">
      <div
        class="flex items-center h-14 px-4 text-white"
        :class="[isSlimSidebar ? 'justify-center' : 'justify-start gap-3']"
      >
        <!-- Logo -->
        <div
          class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm"
        >
          <svg
            class="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
            ></path>
          </svg>
        </div>

        <!-- Organization Name -->
        <div
          v-if="!isSlimSidebar"
          class="flex flex-col min-w-0 transition-all duration-300 ease-in-out"
        >
          <span class="text-sm font-bold text-white truncate">
            {{ orgName }}
          </span>
          <span class="text-xs text-white/80 truncate"> POS System </span>
        </div>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav
      class="flex-1 overflow-y-auto py-6 space-y-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
      :class="[isSlimSidebar ? 'px-2' : 'px-4']"
    >
      <template v-for="(item, index) in menuItems" :key="index">
        <div v-if="isFeatureAccessible(item.roles, userRoles)">
          <!-- Simple Menu Item -->
          <div
            v-if="!item.childItems || item.childItems.length === 0"
            @click="handleNavigate(item)"
            class="group relative flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
            :class="[
              isSlimSidebar ? 'justify-center' : 'justify-start',
              checkActive(item)
                ? 'bg-[#1565C0] text-white shadow-md'
                : 'text-gray-700 hover:bg-blue-50 hover:text-[#1565C0]',
            ]"
          >
            <!-- Icon -->
            <div
              class="flex-shrink-0 w-5 h-5 flex items-center justify-center"
              :class="[
                checkActive(item)
                  ? 'text-white'
                  : 'text-gray-500 group-hover:text-[#1565C0]',
              ]"
              v-html="item.icon"
            />

            <!-- Label -->
            <span
              v-if="!isSlimSidebar"
              class="ml-3 truncate transition-all duration-300 ease-in-out"
            >
              {{ item.name }}
            </span>

            <!-- Active Indicator -->
            <div
              v-if="checkActive(item) && !isSlimSidebar"
              class="ml-auto w-2 h-2 bg-white rounded-full shadow-sm"
            />

            <!-- Tooltip for slim sidebar -->
            <Transition name="tooltip">
              <div
                v-if="isSlimSidebar"
                class="invisible group-hover:visible absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg z-50 whitespace-nowrap"
              >
                {{ item.name }}
                <div
                  class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"
                ></div>
              </div>
            </Transition>
          </div>

          <!-- Menu Item with Children -->
          <div v-else>
            <!-- Parent Item -->
            <div
              @click="handleParentClick(item, index)"
              class="group relative flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer"
              :class="[
                isSlimSidebar ? 'justify-center' : 'justify-start',
                checkActive(item)
                  ? 'bg-[#1565C0] text-white shadow-md'
                  : 'text-gray-700 hover:bg-blue-50 hover:text-[#1565C0]',
              ]"
            >
              <!-- Icon -->
              <div
                class="flex-shrink-0 w-5 h-5 flex items-center justify-center"
                :class="[
                  checkActive(item)
                    ? 'text-white'
                    : 'text-gray-500 group-hover:text-[#1565C0]',
                ]"
                v-html="item.icon"
              />

              <!-- Label -->
              <span
                v-if="!isSlimSidebar"
                class="ml-3 truncate transition-all duration-300 ease-in-out"
              >
                {{ item.name }}
              </span>

              <!-- Expand Arrow - Clickable to toggle submenu -->
              <button
                v-if="!isSlimSidebar"
                @click.stop="toggleReportList(index)"
                class="ml-auto p-1 rounded hover:bg-white/20 transition-colors duration-200"
              >
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  :class="[
                    showReportList[index] ? 'rotate-90' : '',
                    checkActive(item) ? 'text-white' : 'text-gray-400',
                  ]"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </button>

              <!-- Enhanced Tooltip with submenu for slim sidebar -->
              <Transition name="tooltip">
                <div
                  v-if="isSlimSidebar"
                  class="invisible group-hover:visible absolute left-full ml-3 py-2 bg-white border border-gray-200 rounded-lg shadow-lg z-[9999] min-w-48"
                  @click.stop
                >
                  <div
                    class="px-3 py-2 text-sm font-medium text-gray-900 border-b border-gray-100"
                  >
                    {{ item.name }}
                  </div>
                  <div class="py-1">
                    <button
                      v-for="(childItem, childIndex) in item.childItems"
                      :key="childIndex"
                      @click.stop="handleNavigate(childItem)"
                      class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-[#1565C0] transition-colors duration-150"
                    >
                      {{ childItem.name }}
                    </button>
                  </div>
                  <div
                    class="absolute top-4 left-0 transform -translate-x-1 w-2 h-2 bg-white border-l border-t border-gray-200 rotate-45"
                  ></div>
                </div>
              </Transition>
            </div>

            <!-- Child Items (expanded) -->
            <Transition name="expand">
              <div
                v-if="!isSlimSidebar && showReportList[index]"
                class="ml-6 mt-2 space-y-1"
              >
                <button
                  v-for="(childItem, childIndex) in item.childItems"
                  :key="childIndex"
                  @click="handleNavigate(childItem)"
                  class="w-full flex items-center px-3 py-2 text-sm text-gray-600 rounded-lg hover:bg-blue-50 hover:text-[#1565C0] transition-all duration-150"
                  :class="[
                    checkActive(childItem)
                      ? 'bg-blue-50 text-[#1565C0] font-medium'
                      : '',
                  ]"
                >
                  <div
                    class="w-1.5 h-1.5 rounded-full mr-3 flex-shrink-0"
                    :class="[
                      checkActive(childItem) ? 'bg-[#1565C0]' : 'bg-gray-300',
                    ]"
                  ></div>
                  <span class="truncate">{{ childItem.name }}</span>
                </button>
              </div>
            </Transition>
          </div>
        </div>
      </template>
    </nav>

    <!-- Footer Section -->
    <div
      class="flex-shrink-0 border-t border-gray-200 space-y-2 bg-gray-50"
      :class="[isSlimSidebar ? 'p-2' : 'p-4']"
    >
      <!-- Organization Link -->
      <NuxtLink
        to="/org"
        class="group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-gray-600 hover:bg-white hover:text-[#1565C0] hover:shadow-sm"
        :class="isSlimSidebar ? 'justify-center' : 'justify-start'"
      >
        <div
          class="flex-shrink-0 w-5 h-5 flex items-center justify-center text-gray-500 group-hover:text-[#1565C0]"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m6.115 5.19.319 1.913A6 6 0 0 0 8.11 10.36L9.75 12l-.387.775c-.217.433-.132.956.21 1.298l1.348 1.348c.21.21.329.497.329.795v1.089c0 .426.24.815.622 1.006l.153.076c.433.217.956.132 1.298-.21l.723-.723a8.7 8.7 0 0 0 2.288-4.042 1.087 1.087 0 0 0-.358-1.099l-1.33-1.108c-.251-.21-.582-.299-.905-.245l-1.17.195a1.125 1.125 0 0 1-.98-.314l-.295-.295a1.125 1.125 0 0 1 0-1.591l.13-.132a1.125 1.125 0 0 1 1.3-.21l.603.302a.809.809 0 0 0 1.086-1.086L14.25 7.5l1.256-.837a4.5 4.5 0 0 0 1.528-1.732l.146-.292M6.115 5.19A9 9 0 1 0 17.18 4.64M6.115 5.19A8.965 8.965 0 0 1 12 3c1.929 0 3.716.607 5.18 1.64"
            />
          </svg>
        </div>
        <span
          v-if="!isSlimSidebar"
          class="ml-3 truncate transition-all duration-300 ease-in-out"
        >
          Tổ chức
        </span>

        <!-- Tooltip for slim sidebar -->
        <Transition name="tooltip">
          <div
            v-if="isSlimSidebar"
            class="invisible group-hover:visible absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg z-50 whitespace-nowrap"
          >
            Danh sách tổ chức
            <div
              class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"
            ></div>
          </div>
        </Transition>
      </NuxtLink>

      <!-- Dashboard Link -->
      <NuxtLink
        :to="`/dashboard?orgId=${orgId}`"
        class="group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-gray-600 hover:bg-white hover:text-[#1565C0] hover:shadow-sm"
        :class="isSlimSidebar ? 'justify-center' : 'justify-start'"
      >
        <div
          class="flex-shrink-0 w-5 h-5 flex items-center justify-center text-gray-500 group-hover:text-[#1565C0]"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72"
            />
          </svg>
        </div>
        <span
          v-if="!isSlimSidebar"
          class="ml-3 truncate transition-all duration-300 ease-in-out"
        >
          Cửa hàng
        </span>

        <!-- Tooltip for slim sidebar -->
        <Transition name="tooltip">
          <div
            v-if="isSlimSidebar"
            class="invisible group-hover:visible absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg z-50 whitespace-nowrap"
          >
            Danh sách cửa hàng
            <div
              class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"
            ></div>
          </div>
        </Transition>
      </NuxtLink>

      <!-- Toggle Sidebar Button -->
      <button
        @click="toggleSidebar"
        class="group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-gray-600 hover:bg-white hover:text-[#1565C0] hover:shadow-sm w-full"
        :class="isSlimSidebar ? 'justify-center' : 'justify-start'"
      >
        <div
          class="flex-shrink-0 w-5 h-5 flex items-center justify-center text-gray-500 group-hover:text-[#1565C0]"
        >
          <svg
            class="w-4 h-4 transition-transform duration-200"
            :class="isSlimSidebar ? '' : 'rotate-180'"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
        <span
          v-if="!isSlimSidebar"
          class="ml-3 truncate transition-all duration-300 ease-in-out"
        >
          Thu gọn
        </span>

        <!-- Tooltip for slim sidebar -->
        <Transition name="tooltip">
          <div
            v-if="isSlimSidebar"
            class="invisible group-hover:visible absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg z-50 whitespace-nowrap"
          >
            Mở rộng
            <div
              class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"
            ></div>
          </div>
        </Transition>
      </button>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { menuItems } from "@/utils/dataMenuLeft";

// Composables and stores
const { storeId, orgId } = useTabContext();
const { isFeatureAccessible } = usePermission();
const route = useRoute();
const authStore = useAuthStore();

// Props
const { isSlimSidebar } = defineProps({
  isSlimSidebar: Boolean,
});

// Emits
const emit = defineEmits(["toggleSidebar"]);

// Computed
const user = computed(() => authStore?.user);
const userRoles = computed(() => user.value?.roles || ([] as string[]));
const orgName = computed(() => orgId.value || "Tổ chức");

// State
const items = Array.isArray(menuItems) ? menuItems : menuItems.value;
const showReportList = ref(Array(items.length).fill(false));

// Functions
const toggleReportList = (index: number) => {
  showReportList.value[index] = !showReportList.value[index];
};

const handleParentClick = (item: any, index: number) => {
  // Always navigate to first child item if available, regardless of sidebar mode
  if (item.childItems && item.childItems.length > 0) {
    handleNavigate(item.childItems[0]);
  } else if (item.to) {
    // If parent has direct route, navigate to it
    handleNavigate(item);
  }

  // In full mode, also toggle submenu for traditional UX
  if (!isSlimSidebar) {
    toggleReportList(index);
  }
};

const toggleSidebar = () => {
  emit("toggleSidebar");
};

const checkActive = (item: any): boolean => {
  if (item.childItems) {
    return item.childItems.some((child: any) => child.to === route.path);
  } else {
    return item.to === route.path;
  }
};

const handleNavigate = async (item: any) => {
  try {
    await navigateTo(
      `${item.to}?orgId=${orgId.value}&storeId=${storeId.value}`
    );
  } catch (error) {
    console.error("Navigation error:", error);
  }
};
</script>

<style scoped>
/* Tooltip animations */
.tooltip-enter-active,
.tooltip-leave-active {
  transition: all 0.2s ease;
}

.tooltip-enter-from,
.tooltip-leave-to {
  opacity: 0;
  transform: translateX(-10px) scale(0.95);
}

/* Expand animations for child items */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

/* Custom scrollbar - only vertical */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.375rem;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background-color: transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 0px; /* Hide horizontal scrollbar */
}

.scrollbar-thin::-webkit-scrollbar-horizontal {
  display: none; /* Completely hide horizontal scrollbar */
}

/* Force hide all horizontal scrollbars */
.sidebar::-webkit-scrollbar-horizontal {
  display: none !important;
}

.sidebar * {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.sidebar *::-webkit-scrollbar-horizontal {
  display: none !important;
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(255, 255, 255, 0.95);
  }
}

/* Focus styles for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* No horizontal scroll for sidebar */
.sidebar {
  overflow-x: hidden !important;
  max-width: 100%;
}

.sidebar * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Slim sidebar optimizations */
.sidebar.w-16 {
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}

.sidebar.w-16 * {
  white-space: nowrap;
  overflow: hidden;
  max-width: 64px;
}

.sidebar.w-16 .truncate {
  text-overflow: clip;
}

/* Full sidebar optimizations */
.sidebar.w-60 {
  overflow-x: hidden !important;
  overflow-y: auto;
}

.sidebar.w-60 * {
  max-width: 240px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/95 {
    background-color: rgba(255, 255, 255, 0.98);
  }

  .border-gray-200\/50 {
    border-color: rgba(0, 0, 0, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .transition-transform,
  .tooltip-enter-active,
  .tooltip-leave-active,
  .expand-enter-active,
  .expand-leave-active {
    transition: none;
  }
}
</style>
