<template>
  <div
    class="flex flex-row justify-between items-center bg-white border mx-2 py-1 rounded-lg"
  >
    <h1 class="p-2 text-primary font-semibold text-1xl">
      <PERSON><PERSON> sách khách hàng
    </h1>
    <div class="flex items-center justify-center md:gap-2 mr-2">
      <div class="md:block hidden">
        <div class="flex items-center gap-2">
          <div
            class="border p-1.5 border-primary text-primary rounded cursor-pointer"
            @click="handleToogleVoucher"
          >
            G<PERSON><PERSON> tin nh<PERSON>n
          </div>
          <div class="flex" @click="isModalVisible = true">
            <span
              @click="isModalVisible = !true"
              class="inline-block cursor-pointer bg-primary p-1.5"
              ><svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="#fff"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                ></path>
              </svg>
            </span>
          </div>
        </div>
      </div>

      <div class="block md:hidden">
        <div class="relative mr-2" @click="handleOpenToolTip">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-5 cursor-pointer"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"
            />
          </svg>
          <div class="absolute -top-5 -left-[180px]" v-if="isOpenTooltip">
            <div class="border bg-white rounded">
              <div
                @click="handleToogleModal"
                class="text-nowrap border-b border-primary cursor-pointer px-2 py-1"
              >
                Thêm khách hàng mới
              </div>
              <div
                class="text-nowrap cursor-pointer px-2 py-1"
                @click="handleToogleVoucher"
              >
                Gửi tin nhắn
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ModalCreateCustomer
      v-if="isModalVisible"
      @closeModelUser="handleToogleModal"
      :isManagerCustomer="true"
    />
    <SendVoucher
      v-if="isOpenModalVoucher"
      @cancel="handleToogleVoucher"
      @confirm="handleConfirmModalVoucher"
    ></SendVoucher>
  </div>
</template>
<script setup lang="ts">
const isModalVisible = ref(false);
const isOpenModalVoucher = ref(false);
//
const isOpenTooltip = ref(false);
const handleOpenToolTip = () => {
  isOpenTooltip.value = !isOpenTooltip.value;
};
const handleToogleModal = () => {
  isModalVisible.value = !isModalVisible.value;
};
const handleToogleVoucher = () => {
  isOpenModalVoucher.value = !isOpenModalVoucher.value;
};
const handleConfirmModalVoucher = () => {
  console.log("confirm ");
};
</script>
