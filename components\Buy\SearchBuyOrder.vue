<template>
  <div>
    <div v-if="loading">
      <LoadingSpinner />
    </div>
    <div v-if="isAlert" class="text-red-600 ml-1 mb-2">
      Không có thông tin đơn hàng!
    </div>
    <div class="flex items-center mb-2">
      <div class="relative flex-grow">
        <input
          type="text"
          placeholder="Tìm kiếm mã đơn hàng..."
          class="border border-border rounded-lg p-2 w-full pr-10 outline-none"
          v-model="keyword"
        />
        <div
          v-if="isLoading"
          class="absolute inset-y-0 right-3 flex items-center"
        >
          <svg
            class="animate-spin h-5 w-5 text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
            ></path>
          </svg>
        </div>
        <div
          @click="clearInput"
          v-if="!isLoading && keyword"
          class="absolute inset-y-0 right-2 flex items-center cursor-pointer"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.0"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18 18 6M6 6l12 12"
            />
          </svg>
        </div>
      </div>
      <div class="flex items-center gap-2 ml-2">
        <!-- <div class="md:block hidden">
            <InputField
              type="select"
              :defaultText="'Theo Nhân viên'"
              :selectClass="'w-38 p-[9px] placeholder:text-gray-500  text-sm rounded  outline-none '"
              v-model="selectedEmployee"
              :options="dataEmployee"
              @change="handleEmployeeChange"
            />
          </div> -->
        <div class="md:block hidden">
          <flatpickr
            class="rounded p-[8.8px] w-[250px] max-w-[250px] text-center focus:outline-none"
            placeholder="Chọn khoảng thời gian"
            :config="datePickerConfig"
          />
        </div>
        <button
          @click="handleSearch"
          class="bg-primary text-white rounded-lg p-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
            />
          </svg>
        </button>
        <button
          @click="createBuyOrder"
          class="bg-primary text-white rounded-lg p-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
        </button>
      </div>
    </div>
    <!-- lọc ở giao diện mobile -->
    <div class="block md:hidden">
      <div class="flex gap-2">
        <flatpickr
          class="w-full rounded p-[8.8px] text-center focus:outline-none"
          placeholder="Chọn khoảng thời gian"
          :config="datePickerConfig"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
//
import flatpickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { Vietnamese } from "flatpickr/dist/l10n/vn.js";
const emits = defineEmits(["selectedDate", "selectedStaff", "changeKeyWord"]);
const datePickerConfig = {
  locale: Vietnamese,
  mode: "range",
  dateFormat: "d-m-Y",
  onChange: async (selectedDates: any) => {
    if (selectedDates.length === 2) {
      emits(
        "selectedDate",
        selectedDates[1].getTime(),
        selectedDates[0].getTime()
      );
    }
  },
};
//
const { createBuyOrder, loading } = useOrder();
const isAlert = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const keyword = ref<string>("");

const clearInput = () => {
  keyword.value = "";
  emits("changeKeyWord", keyword.value);
};
const handleSearch = async () => {
  emits("changeKeyWord", keyword.value);
};
const handlecreateOrder = () => {
  console.log("tạo đơn mua");
};
</script>
