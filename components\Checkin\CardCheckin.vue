<template>
  <div class="mt-4">
    <div class="mt-4">
      <div class="bg-white rounded-lg h-auto p-2 md:w-[700px]">
        <div class="flex flex-col gap-2">
          <div class="flex items-center justify-end gap-5">
            <div
              @click="handleCreateOrder(dataCheckin.id, dataCheckin.ownerId)"
              class="text-primary font-bold cursor-pointer flex gap-2"
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
                  />
                </svg>
              </span>
            </div>
            <div
              @click="handleOpenEditCheckin(dataCheckin)"
              class="text-primary font-bold cursor-pointer"
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                  />
                </svg>
              </span>
            </div>
          </div>
          <div class="flex justify-between">
            <div class="flex gap-2">
              <div class="text-primary flex gap-2 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18.2px"
                  height="18.2px"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="M5.615 21q-.69 0-1.152-.462Q4 20.075 4 19.385V6.615q0-.69.463-1.152Q4.925 5 5.615 5h1.77V3.308q0-.233.153-.386q.152-.153.385-.153t.386.153q.153.153.153.386V5h7.153V3.27q0-.214.144-.357t.356-.144q.214 0 .357.143t.143.357V5h1.77q.69 0 1.152.463q.463.462.463 1.152v12.77q0 .69-.462 1.152q-.463.463-1.153.463zm0-1h12.77q.23 0 .423-.192q.192-.193.192-.423v-8.77H5v8.77q0 .23.192.423q.193.192.423.192M5 9.615h14v-3q0-.23-.192-.423Q18.615 6 18.385 6H5.615q-.23 0-.423.192Q5 6.385 5 6.615zm0 0V6zm7 4.539q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.539q-.23.23-.54.23m-4 0q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.539q-.23.23-.54.23m8 0q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.539q-.23.23-.54.23M12 18q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.54Q12.31 18 12 18m-4 0q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.54Q8.31 18 8 18m8 0q-.31 0-.54-.23q-.23-.23-.23-.54q0-.309.23-.539q.23-.23.54-.23q.31 0 .54.23q.23.23.23.54q0 .31-.23.54Q16.31 18 16 18"
                  ></path>
                </svg>
                {{ formatDate(dataCheckin.createdStamp) }}
              </div>
            </div>
          </div>
          <div class="flex flex-col gap-2">
            <div class="flex gap-2">
              <span>
                <img
                  src="~assets/images/shop.png"
                  alt=""
                  class="w-[18.2px] h-[18.2px] object-contain"
                  loading="lazy"
                />
              </span>
              <span> Cửa hàng: {{ dataCheckin.name }}</span>
            </div>

            <div class="flex gap-2">
              <span>
                <img
                  src="~/assets/images/phone.jpg"
                  alt=""
                  class="w-[18.2px] h-[18.2px] object-contain"
                  loading="lazy"
                />
              </span>
              <span> Số điện thoại: {{ dataCheckin?.owner?.phone }} </span>
            </div>
            <div class="">
              <div class="flex gap-1 md:h-auto cursor-pointer">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="18"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="red"
                    d="M12 21.325q-.35 0-.7-.125t-.625-.375Q9.05 19.325 7.8 17.9t-2.087-2.762q-.838-1.338-1.275-2.575T4 10.2q0-3.75 2.413-5.975T12 2q3.175 0 5.588 2.225T20 10.2q0 1.125-.437 2.363t-1.275 2.575Q17.45 16.475 16.2 17.9t-2.875 2.925q-.275.25-.625.375t-.7.125M12 12q.825 0 1.413-.587T14 10q0-.825-.587-1.412T12 8q-.825 0-1.412.588T10 10q0 .825.588 1.413T12 12"
                  />
                </svg>
                <div class="">Locations:{{ handleLocation(dataCheckin) }}</div>
              </div>
            </div>

            <div>
              <div class="flex gap-2">
                <img
                  src="~/assets/images/description.png"
                  alt=""
                  class="w-[18.2px] h-[18.2px] object-contain"
                  loading="lazy"
                />
                <div>Mô tả:{{ dataCheckin.description }}</div>
              </div>
              <div></div>
            </div>
          </div>
          <div class="flex gap-2 ml-1">
            <div>Giai đoạn hiện tại:</div>
            <div class="font-bold text-primary">
              {{ handleProcessLine(dataCheckin?.processStatus) }}
            </div>
          </div>
          <!-- <div
            class="min-h-[200px] max-h-[550px] col-span-1 md:flex md:items-center md:justify-center"
            v-if="
              dataCheckin.effort &&
              dataCheckin.effort[0] &&
              dataCheckin.effort[0].toDoList[1].listAttachment
            "
          >
            <Swiper :options="{ loop: true }">
              <SwiperSlide v-for="(url, imgIndex) in image" :key="imgIndex">
                <img
                  loading="lazy"
                  class="object-contain min-h-[200px] max-h-[550px] z-1 w-screen h-[300px]"
                  :src="`https://s3-img-gw.longvan.net/img/omnichannel/${url?.srcPath}`"
                  alt="Image"
                />
              </SwiperSlide>
            </Swiper>
          </div> -->
          <!-- <div class="flex items-center justify-center gap-4">
            <div
              @click="handleOpenEditCheckin(item, index)"
              class="flex items-center justify-center border p-2 rounded-md bg-white border-primary-light font-bold text-primary-light"
            >
              Điều chỉnh
            </div>
            <div
              @click="handleCreateOrder(item.id, item.ownerId)"
              class="flex items-center justify-center border p-2 rounded-md bg-white border-primary-light font-bold text-primary-light"
            >
              Tạo đơn
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <ModalEditCheckin
    v-if="isEditCheckin"
    @isClose="isClose"
    :data="data"
  ></ModalEditCheckin>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { format } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { vi } from "date-fns/locale";
import type { Auth } from "~/types/Auth";

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const vietnamDate = toZonedTime(date, "Asia/Ho_Chi_Minh");
  return format(vietnamDate, "HH:mm:ss d/M/y", { locale: vi });
};

const { dataCheckin, dataPerson } = defineProps(["dataCheckin", "dataPerson"]);

const data = ref();
const { storeId,orgId } = useTabContext();
const token = useCookie("token").value;
const auth = useCookie("auth").value as unknown as Auth;
const isEditCheckin = ref(false);

const isClose = (value: boolean) => {
  isEditCheckin.value = value;
};

const handleImageUrl = (srcPath: any) => {
  if (!srcPath || !Array.isArray(srcPath) || srcPath.length === 0) {
    return [];
  }

  const urls = srcPath.map((item: any) => {
    const url = `https://s3-img-gw.longvan.net/img/omnichannel/${item.srcPath}`;
    return url;
  });

  return urls;
};

const handleOpenEditCheckin = (item: any) => {
  // console.log("da click vao checkin", item, index);
  // isEditCheckin.value = true;
  // console.log("data");
  // data.value = item;
  navigateTo(`/dms/${item.id}?orgId=${orgId}&storeId=${storeId.value}`);
};

const currentDate = new Date();
const timestamp = currentDate.getTime();

const handleCreateOrder = (id: string, idKH: string) => {
  const host = window.location.origin;
  const url = `${host}/sale?orgId=${orgId}&storeId=${storeId.value}&id=`;
  const encodedUrl = encodeURIComponent(url);
  const link = `https://storefront.dev.longvan.vn/v2/action-link/ORDER_ECOMOS?orgId=${orgId}&fromId=${id}&token=${token}&time=${timestamp}&orderId=&orderType=POS_SALE&platform=web&storeId=${storeId.value}&createdBy=${auth?.user?.id}&customerId=${idKH}&redirect=${encodedUrl}`;
  window.location.href = link;
};
const handleProcessLine = (status: any) => {
  if (status) {
    const data = dataCheckin.processPipeline.find(
      (item: any) => item.id === status
    );
    return data.name;
  }
};
const location = ref();
const handleLocation = (value: any) => {
  const item = value?.subTasks.find(
    (item: any) => item.workEffortTypeId === "TAKE_ADDRESS"
  );
  return item?.description;
};
const image = ref();
const handleImage = () => {
  const item = dataCheckin?.subTasks.find(
    (item: any) => item.workEffortTypeId === "TAKE_PHOTO"
  );
  if (item) {
    image.value = item.attachments;
  }
  // console.log("image", image);
};
// watch(() => dataCheckin, handleLocation, { immediate: true });

onMounted(() => {
  handleImage();
  handleLocation(dataCheckin);
});
</script>

<style scoped></style>
