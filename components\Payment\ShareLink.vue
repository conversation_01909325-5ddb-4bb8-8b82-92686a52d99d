<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <h3 class="text-base font-bold text-gray-900 mb-2 flex items-center gap-2">
      Chia sẻ thanh toán
    </h3>

    <!-- Payment Link -->
    <div class="space-y-2">
      <div>
        <label class="block text-xs font-medium text-gray-700 mb-1">
          Link thanh toán
        </label>
        <div class="flex gap-1">
          <input
            v-model="transactionId"
            type="text"
            readonly
            class="flex-1 px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-xs focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            placeholder="Link thanh toán"
          />
          <button
            @click="handleCopy"
            class="px-2 py-1.5 bg-primary hover:bg-primary/90 text-white rounded transition-colors duration-200 flex items-center gap-1 shadow-sm hover:shadow-md"
            title="Sao chép link"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="w-4 h-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
              />
            </svg>
            <span class="hidden sm:inline text-xs font-medium">Sao chép</span>
          </button>
        </div>
      </div>

      <!-- Quick Access Link -->
      <div>
        <a
          :href="transactionId"
          target="_blank"
          class="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm transition-colors duration-200"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
            />
          </svg>
          Mở link thanh toán
        </a>
      </div>

      <!-- Share Options -->
      <div class="border-t border-gray-200 pt-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700">Chia sẻ qua:</span>
          <SendZalo :orderDetail="orderDetails?.data" :payment="true" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(["orderDetails", "orderId"]);
const transactionId = ref<string>("");
const route = useRoute();
const { storeId, orgId } = useTabContext();

const { shareOrder } = useComhub();
const { getCustomerById } = useCustomer();
const template = computed(() => {
  const data = props.orderDetails?.data;
  return {
    templateData: {
      status: data?.statusDescription,
      orderId: data?.id,
      price: data?.remainTotal,
      createdStamp: formatTimestampV2(data?.order?.orderDate),
      customer_name: data?.order?.ownerName,
    },
    senderPartyId: "SYSTEM",
    receivePartyIds: [data?.order?.ownerPartyId],
  };
});
const handleCopy = async () => {
  await navigator.clipboard.writeText(transactionId.value);
  useNuxtApp().$toast.success("Đã lưu vào clipboard");
};
onMounted(() => {
  const host = window.location.origin;

  transactionId.value = `${host}/thanh-toan?orderId=${route.query.orderId}&orgId=${orgId.value}&storeId=${storeId.value}`;
});
const updateTransactionId = () => {
  const host = window.location.origin;
  transactionId.value = `${host}/thanh-toan?orderId=${route.query.orderId}&orgId=${orgId.value}&storeId=${storeId.value}`;
};
watch(() => route.query.orderId, updateTransactionId);
watch(() => storeId.value, updateTransactionId);
// watch(() => productStoreLink, updateTransactionId);
</script>

<style scoped></style>
