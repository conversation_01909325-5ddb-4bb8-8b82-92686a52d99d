<template>
  <div class="w-full bg-[#1565C0] py-2 flex items-center justify-between">
    <div class="text-white mx-1 text-nowrap">
      {{ `Cần thanh toán: ${formatCurrency(total)} đ` }}
    </div>
    <div
      class="text-white mx-1 bg-primary p-1 rounded cursor-pointer"
      @click="showConfirmationModal"
    >
      <PERSON><PERSON> toán
    </div>
    <ConfirmDialog
      v-if="isModalVisible"
      :title="'Xác nhận Thanh toán'"
      :message="'Bạn có chắc chắn muốn thanh toán đơn không?'"
      @confirm="confirmCheckout"
      @cancel="cancelCheckout"
    />
  </div>
</template>
<script setup lang="ts">
const isModalVisible = ref(false);
const { updateStatusOpen } = useOrder();
const saleStore = useSale();
const products = computed(() => saleStore.currentCartProducts);
const customer = computed(() => saleStore.getCustomerData);
const orderId = computed(() => saleStore.currentOrderId);
const discountMember = computed(() => saleStore.getOrderPromotion);
const authStore = useAuthStore();
const employee = computed(() => saleStore.currentEmployee.salePartyId);
const total = computed(() => saleStore.currentOrderTotal.total);
const route = useRoute();
const validateCart = () => {
  const app = useNuxtApp();

  if (saleStore.currentCartIndex === null) {
    app.$toast.warning(
      "Chưa có giỏ hàng. Vui lòng tạo giỏ hàng trước khi thanh toán."
    );
    return false;
  }
  if (!products.value.length) {
    app.$toast.warning("Giỏ hàng chưa có sản phẩm .");
    return false;
  }
  if (!customer.value) {
    app.$toast.warning(
      "Vui lòng thêm thông tin khách hàng trước khi thanh toán."
    );
    return false;
  }
  return true;
};

const showConfirmationModal = () => {
  if (validateCart()) {
    isModalVisible.value = true;
  }
};
const lineItems = computed(() => {
  return products.value.map((product) => ({
    id: product.id,
    productName: product.orderItemName,
    quantity: product.currentQuantity,
    typeDiscount: "MONEY",
    parent_id: "",
    product_id: product.variantId.id,
    input_price: product.price,
    supplier_id: "",
    discount_amount: product.discount.amount,
  }));
});
const currentDate = new Date();
const timestamp = currentDate.getTime();
// const currentCartCreationTime = computed(() => saleStore.time ?? timestamp);
const manualDiscount = computed(
  () => saleStore.currentOrderTotal.manualDiscount
);
const note = computed(() => saleStore.currentCartNote);
const confirmCheckout = async () => {
  isModalVisible.value = false;
  try {
    console.log("order", orderId.value);
    const response = await updateStatusOpen(orderId.value as string);
    console.log("response ", response);
    useNuxtApp().$toast.success(
      `Thanh toán đơn hàng ${orderId.value} thành công`
    );
    handleChangePagePayment(route.query.orderId as string);
    // navigateTo(`/detailOrder?orderId=${route.query.orderId}`)
    // saleStore.clearCarts();
  } catch (error) {
    console.log(error);
    useNuxtApp().$toast.error(`Thanh toán đơn hàng ${orderId.value} thất bại`);
  }
};
// Use tab-isolated context instead of cookies
const { storeId } = useTabContext();
const router = useRouter();
const cancelCheckout = () => {
  isModalVisible.value = false;
};

const handleChangePagePayment = async (orderId: string) => {
  console.log("🚀 ~ handleChangePagePayment ~ orderId:", orderId);
  try {
    router.push({
      path: "/payment",
      query: { store: storeId.value, orderId: orderId },
    });
  } catch (error) {
    console.error("Failed to change page", error);
  }
};
</script>
