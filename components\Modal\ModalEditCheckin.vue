<template>
  <div
    class="fixed inset-0 z-[50]"
    aria-labelledby="modal-title"
    aria-modal="true"
  >
    <div
      class="flex items-center justify-center h-full p-0 text-center sm:block sm:p-0"
    >
      <div
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        aria-hidden="true"
      ></div>
      <span
        class="hidden sm:inline-block sm:align-middle sm:h-screen"
        aria-hidden="true"
        >&#8203;</span
      >
      <div
        class="relative inline-block bg-white shadow w-full m-auto lg:w-[40%] h-full md:rounded-[0.5rem] md:h-full md:top-0"
      >
        <div
          class="flex justify-between items-center w-full p-2 rounded-t border-b bg-[#3F51B5]"
        >
          <div class="flex gap-1 items-center justify-between w-full">
            <div class="w-[32px] h-[32px]"></div>
            <div class="text-base text-white lg:text-lg">
              <PERSON><PERSON><PERSON><PERSON> chỉnh checkin
            </div>
            <button
              type="button"
              class="cursor-pointer text-gray-400 bg-transparent rounded-lg text-sm inline-flex items-center"
              @click="handleToogleModal"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 16 16"
              >
                <g fill="white">
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16"
                  />
                  <path
                    d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"
                  />
                </g>
              </svg>
            </button>
          </div>
        </div>
        <div class="text-left pt-6 pb-[3.25rem] h-full overflow-auto md:h-full">
          <div
            class="flex flex-col w-full justify-between"
            :class="previewImages?.length > 0 ? 'lg:h-full h-auto' : 'h-full'"
          >
            <div class="mx-3 flex flex-col gap-2 mb-2 h-full md:h-[80%]">
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold"
                  >Tên cửa hàng
                  <span class="text-red-500 text-xs">*</span></span
                >
                <input
                  class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
                  type="text"
                  placeholder="Tên cửa hàng"
                  v-model="nameStore"
                />
              </div>
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold"
                  >Số điện thoại
                  <span class="text-red-500 text-xs">*</span></span
                >
                <input
                  class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
                  type="text"
                  placeholder="Số điện thoại"
                  v-model="phone"
                />
              </div>
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold"
                  >Vị trí Checkin
                  <span class="text-red-500 text-xs">*</span></span
                >
                <input
                  class="w-full py-[10px] border border-[#3F51B5] pl-3 pr-12 text-base outline-none rounded-md bg-bgGray"
                  type="text"
                  placeholder="Vị trí Checkin"
                  v-model="location"
                />
              </div>
              <div class="flex flex-col">
                <span class="pb-2 text-sm font-bold">Mô tả</span>
                <div>
                  <div class="flex flex-col m-0 md:p-4 bg-white rounded">
                    <div class="grid grid-cols-1 gap-2">
                      <div class="flex justify-between gap-2">
                        <textarea
                          rows="8"
                          id="note"
                          v-model="description"
                          class="py-1 px-2 w-full h-[100px] text-base rounded outline-none border bg-secondary"
                          type="text"
                          placeholder="Mô tả"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex flex-col p-2">
                <span class="p-2 text-sm font-bold">Upload Images</span>
                <label class="inline-block cursor-pointer">
                  <input
                    type="file"
                    @change="handleImageUpload"
                    accept="image/*"
                    multiple
                    class="hidden"
                  />
                  <span
                    class="bg-blue-500 text-white py-2 px-4 rounded cursor-pointer"
                    >Choose Files</span
                  >
                </label>
                <div
                  v-if="previewImages?.length"
                  class="mt-2 grid grid-cols-3 gap-2"
                >
                  <div
                    v-for="(image, index) in previewImages"
                    :key="index"
                    class="relative border border-primary rounded-md"
                  >
                    <img
                      :src="image.url"
                      alt="Image Preview"
                      class="w-[80px] h-[80px] object-contain mx-auto"
                      loading="lazy"
                    />
                    <span
                      @click="removeImage(image, index)"
                      class="absolute top-0 right-0 text-red-700 cursor-pointer"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="md:h-full flex flex-col justify-end">
              <div
                class="flex text-black gap-4 flex-col w-full justify-between"
              >
                <div class="flex gap-1 flex-col w-full"></div>
              </div>
              <div class="flex justify-center">
                <div
                  @click="handleEditCheckin"
                  class="bg-[#3F51B5] w-[70%] text-center text-white py-1 rounded-md font-semibold border-2 border-[#3F51B5] cursor-pointer"
                >
                  Điều chỉnh
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const isClose = ref(false);
const emit = defineEmits(["isClose"]);
const props = defineProps(["data"]);
const nameStore = ref(props.data.name);
const checkinStore = useCheckinStore();
const handleLocation = (items) => {
  const data = items.find(
    (item) => item.actionLink.id === checkinStore.actionLinkLocation
  );
  return data ? data.description : "";
};
const handleLocationId = (items) => {
  const data = items.find(
    (item) => item.actionLink.id === checkinStore.actionLinkLocation
  );
  return data ? data.id : "";
};
const location = ref("be chưa trả về nè");
const phone = ref("chưa có nè");
const description = ref(props.data.description);
const dataImageRemove = ref([]);
const {
  UpdateWorkEffortName,
  UpdateWorkEffortDescriptions,
  updateStatusAttachmentByIds,
  AddAttachmentForWorkEffort,
  uploadImage,
  getListTodos,
} = useCheckin();
const auth = useCookie("auth").value;
const handleImageUrl = (srcPath) => {
  if (!srcPath || !Array.isArray(srcPath) || srcPath.length === 0) {
    return [];
  }

  const urls = srcPath.map((item) => {
    const url = `https://s3-img-gw.longvan.net/img/omnichannel/${item.srcPath}`;
    return url;
  });

  return urls;
};
const handleImage = (items) => {
  const data = items.find(
    (item) => item.actionLink.id === checkinStore.actionLinkImage
  );
  return data?.listAttachment;
};

const previewImages = ref();
// handleImage(props.data?.effort[0]?.toDoList)?.map((image) => ({
//   url: `https://s3-img-gw.longvan.net/img/omnichannel/${image.srcPath}`,
//   srcPath: image.srcPath,
//   id: image.id,
// })) || []

const dataImage = ref([]);

const handleToogleModal = () => {
  isClose.value = false;
  emit("isClose", isClose.value);
};

const handleEditCheckin = async () => {
  await UpdateWorkEffortName(auth.user.id, props.data.id, nameStore.value);
  await UpdateWorkEffortDescriptions(
    auth.user.id,
    props.data.id,
    description.value
  );
  await UpdateWorkEffortDescriptions(
    auth.user.id,
    handleLocationId(props.data.effort[0].toDoList),
    location.value
  );
  dataImageRemove.value.map(async (item, index) => {
    if (item.id) {
      await updateStatusAttachmentByIds(auth.user.id, item.id, "INACTIVE");
    }
  });
  await handleUpdateImage(props.data?.effort[0]?.toDoList[1].id, "create");
  await getListTodos();
  handleToogleModal();
};
const handleUpdateImage = async (id, type) => {
  dataImage.value.forEach(async (image) => {
    const res = await uploadImage(image);
    const validImgArr = [
      {
        fileType: res?.fileType,
        name: res?.fileName?.split("/")[0] + "-" + res?.fileName,
        srcConfigPathId: res?.configPathID,
        srcId: res?.id,
        srcName: res?.fileName,
        srcPath: res?.filePath,
        type: "RESULT",
      },
    ];
    await handleAddAttachmentForWorkEffort(validImgArr, id, type);
  });
};
const handleAddAttachmentForWorkEffort = async (arr, id, typeFuntion) => {
  try {
    if (arr.length > 0) {
      await AddAttachmentForWorkEffort(auth.user.id, id, arr);
    }
  } catch (error) {
    throw error;
  }
};
const handleImageUpload = (event) => {
  const files = event.target.files;
  Array.from(files).forEach((file) => {
    dataImage.value.push(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      previewImages?.value.push({ url: e.target.result });
    };
    reader.readAsDataURL(file);
  });
};

const removeImage = (image, index) => {
  previewImages.value.splice(index, 1);
  dataImage.value.splice(index, 1);
  if (image.id) {
    dataImageRemove.value.push(image);
  }
};
</script>

<style scoped>
.custom-file-upload {
  @apply inline-block cursor-pointer;
}

.custom-file-upload input[type="file"] {
  @apply hidden;
}

.upload-button {
  @apply bg-blue-500 text-white py-2 px-4 rounded cursor-pointer;
}
</style>
