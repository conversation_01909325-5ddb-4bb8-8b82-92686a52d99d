<template>
  <div class="animate-pulse">
    <!-- Desktop Table Loading -->
    <div class="hidden md:block">
      <table class="table-auto w-full text-sm">
        <tbody>
          <tr
            v-for="index in itemCount"
            :key="index"
            class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white border-b border-gray-100"
          >
            <!-- M<PERSON> đơn -->
            <td class="p-3 w-1/12 text-center">
              <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </td>
            
            <!-- Khách hàng -->
            <td class="p-3 w-2/12">
              <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </td>
            
            <!-- N<PERSON><PERSON><PERSON> ký -->
            <td class="p-3 w-2/12">
              <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </td>
            
            <!-- Nhân viên -->
            <td class="p-3 w-2/12">
              <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </td>
            
            <!-- Sản phẩm quan tâm -->
            <td class="p-3 w-5/12 max-w-0">
              <div class="space-y-2">
                <div class="h-4 bg-gray-200 rounded w-4/5"></div>
                <div class="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </td>
            
            <!-- Actions -->
            <td class="p-3 w-auto text-center">
              <div class="h-6 w-6 bg-gray-200 rounded mx-auto"></div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Mobile Cards Loading -->
    <div class="md:hidden space-y-3">
      <div
        v-for="index in itemCount"
        :key="index"
        class="bg-white p-4 rounded-lg border border-gray-200 shadow-sm"
      >
        <div class="space-y-3">
          <!-- Card header -->
          <div class="flex justify-between items-start">
            <div class="space-y-2 flex-1">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div class="h-6 w-16 bg-gray-200 rounded-full ml-4"></div>
          </div>
          
          <!-- Card content -->
          <div class="space-y-2">
            <div class="h-3 bg-gray-200 rounded w-full"></div>
            <div class="h-3 bg-gray-200 rounded w-2/3"></div>
            <div class="h-3 bg-gray-200 rounded w-4/5"></div>
          </div>
          
          <!-- Card footer -->
          <div class="flex justify-between items-center pt-2">
            <div class="h-3 bg-gray-200 rounded w-1/3"></div>
            <div class="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  itemCount?: number;
  showMobile?: boolean;
  showDesktop?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  itemCount: 5,
  showMobile: true,
  showDesktop: true,
});
</script>

<style scoped>
/* Enhanced pulse animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer effect for skeleton elements */
.bg-gray-200 {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation for better visual effect */
tbody tr:nth-child(1) { animation-delay: 0ms; }
tbody tr:nth-child(2) { animation-delay: 100ms; }
tbody tr:nth-child(3) { animation-delay: 200ms; }
tbody tr:nth-child(4) { animation-delay: 300ms; }
tbody tr:nth-child(5) { animation-delay: 400ms; }

.space-y-3 > *:nth-child(1) { animation-delay: 0ms; }
.space-y-3 > *:nth-child(2) { animation-delay: 100ms; }
.space-y-3 > *:nth-child(3) { animation-delay: 200ms; }
.space-y-3 > *:nth-child(4) { animation-delay: 300ms; }
.space-y-3 > *:nth-child(5) { animation-delay: 400ms; }

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>
