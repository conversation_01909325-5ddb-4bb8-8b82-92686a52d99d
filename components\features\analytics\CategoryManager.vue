<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Quản lý danh mục</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="flex h-[calc(90vh-120px)]">
        <!-- Category List -->
        <div class="flex-1 border-r border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <div class="flex items-center gap-3">
              <button
                @click="activeTab = 'income'"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  activeTab === 'income' 
                    ? 'bg-green-100 text-green-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                Danh mục thu
              </button>
              <button
                @click="activeTab = 'expense'"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  activeTab === 'expense' 
                    ? 'bg-red-100 text-red-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                Danh mục chi
              </button>
            </div>
          </div>

          <div class="p-4 overflow-y-auto">
            <div class="space-y-2">
              <div
                v-for="category in filteredCategories"
                :key="category.id"
                class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 rounded-lg flex items-center justify-center"
                       :class="category.type === 'income' ? 'bg-green-100' : 'bg-red-100'">
                    <svg class="w-4 h-4" :class="category.type === 'income' ? 'text-green-600' : 'text-red-600'" 
                         fill="currentColor" viewBox="0 0 20 20">
                      <path :d="category.icon || defaultIcon" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ category.name }}</div>
                    <div class="text-sm text-gray-500">{{ category.description }}</div>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <button
                    @click="editCategory(category)"
                    class="p-1 text-gray-400 hover:text-primary rounded transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteCategory(category)"
                    class="p-1 text-gray-400 hover:text-red-600 rounded transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Add New Category Button -->
            <button
              @click="addNewCategory"
              class="w-full mt-4 p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-primary hover:text-primary transition-colors"
            >
              <svg class="w-5 h-5 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Thêm danh mục mới
            </button>
          </div>
        </div>

        <!-- Category Form -->
        <div class="w-80 p-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">
            {{ isEditing ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới' }}
          </h4>

          <form @submit.prevent="saveCategory" class="space-y-4">
            <!-- Category Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Loại danh mục</label>
              <div class="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  @click="categoryForm.type = 'income'"
                  :class="[
                    'flex items-center justify-center gap-2 px-3 py-2 border rounded-lg transition-colors',
                    categoryForm.type === 'income' 
                      ? 'border-green-500 bg-green-50 text-green-700' 
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  ]"
                >
                  Thu
                </button>
                <button
                  type="button"
                  @click="categoryForm.type = 'expense'"
                  :class="[
                    'flex items-center justify-center gap-2 px-3 py-2 border rounded-lg transition-colors',
                    categoryForm.type === 'expense' 
                      ? 'border-red-500 bg-red-50 text-red-700' 
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  ]"
                >
                  Chi
                </button>
              </div>
            </div>

            <!-- Category Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tên danh mục</label>
              <input
                type="text"
                v-model="categoryForm.name"
                placeholder="Nhập tên danh mục"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              />
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Mô tả</label>
              <textarea
                v-model="categoryForm.description"
                rows="3"
                placeholder="Nhập mô tả danh mục"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
              ></textarea>
            </div>

            <!-- Icon Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Biểu tượng</label>
              <div class="grid grid-cols-4 gap-2">
                <button
                  type="button"
                  v-for="icon in iconOptions"
                  :key="icon.value"
                  @click="categoryForm.icon = icon.value"
                  :class="[
                    'p-2 border rounded-lg transition-colors',
                    categoryForm.icon === icon.value 
                      ? 'border-primary bg-primary/5 text-primary' 
                      : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                  ]"
                >
                  <svg class="w-5 h-5 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path :d="icon.value" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex gap-3 pt-4">
              <button
                type="button"
                @click="resetForm"
                class="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Hủy
              </button>
              <button
                type="submit"
                class="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              >
                {{ isEditing ? 'Cập nhật' : 'Thêm' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'save', 'delete'])

const activeTab = ref('income')
const isEditing = ref(false)
const editingId = ref(null)

const categoryForm = reactive({
  type: 'income',
  name: '',
  description: '',
  icon: ''
})

const defaultIcon = 'M9 2a1 1 0 000 2h2a1 1 0 100-2H9z'

const iconOptions = [
  { value: 'M9 2a1 1 0 000 2h2a1 1 0 100-2H9z' },
  { value: 'M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z' },
  { value: 'M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z' },
  { value: 'M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z' }
]

const filteredCategories = computed(() => {
  return props.categories.filter(cat => cat.type === activeTab.value)
})

const addNewCategory = () => {
  resetForm()
  categoryForm.type = activeTab.value
}

const editCategory = (category) => {
  isEditing.value = true
  editingId.value = category.id
  Object.assign(categoryForm, category)
}

const deleteCategory = (category) => {
  if (confirm(`Bạn có chắc chắn muốn xóa danh mục "${category.name}"?`)) {
    emit('delete', category.id)
  }
}

const saveCategory = () => {
  const data = { ...categoryForm }
  if (isEditing.value) {
    data.id = editingId.value
  }
  emit('save', data)
  resetForm()
}

const resetForm = () => {
  isEditing.value = false
  editingId.value = null
  Object.assign(categoryForm, {
    type: activeTab.value,
    name: '',
    description: '',
    icon: iconOptions[0].value
  })
}

onMounted(() => {
  categoryForm.icon = iconOptions[0].value
})
</script>
