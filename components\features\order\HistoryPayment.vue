<template>
  <div class="md:col-span-9 col-span-6 rounded-lg p-2 mt-2">
    <div class="text-lg font-semibold text-primary cursor-pointer">
      Lịch sử thanh toán
    </div>
    <div
      v-if="listPayment?.length > 0"
      class="mt-2 overflow-auto"
      v-for="payment in listPayment"
    >
      <div class="rounded-lg border py-2 px-1">
        <div class="font-semibold text-sm">
          {{ formatTimestampV3(payment?.transactionDate) }}
        </div>
        <div class="text-sm">
          <span class="font-semibold">Phương thức thanh toán: </span>
          <span>{{ payment?.methodDescription }}</span>
        </div>
        <div class="text-sm">
          <span class="font-semibold">Mã giao dịch: </span>
          <span>{{ payment?.paymentId }}</span>
        </div>
        <div class="text-sm">
          <span class="font-semibold">Số tiền: </span>
          <span> {{ formatCurrency(payment?.totalAmount) }} </span>
        </div>
        <div class="text-sm">
          <span class="font-semibold">Ghi chú: </span>
          <span> {{ payment?.orderInfo }} </span>
        </div>
      </div>
    </div>
    <div v-else class="flex items-center justify-center">
      <span
        class="rounded-lg border border-primary text-primary p-2 flex items-center justify-center font-semibold mt-2"
      >
        Chưa có lịch sử thanh toán
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const listPayment = ref();
const { paymentsByOrders } = usePayment();
const handlePayment = async () => {
  try {
    const response = await paymentsByOrders(orderDetail.value?.id);
    listPayment.value = response;
  } catch (error) {
    throw error;
  }
};
watch(orderDetail, () => {
  handlePayment();
});
</script>
